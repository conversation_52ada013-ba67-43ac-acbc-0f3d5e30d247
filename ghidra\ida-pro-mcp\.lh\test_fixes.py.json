{"sourceFile": "test_fixes.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754227748066, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754227748066, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n# -*- coding: utf-8 -*-\r\n\"\"\"\r\nIDA Pro MCP Plugin 修复验证脚本\r\n验证所有 ida_bytes.find_bytes() 调用修复情况\r\n\"\"\"\r\n\r\nimport re\r\nimport sys\r\nimport os\r\n\r\ndef test_plugin_fixes():\r\n    \"\"\"测试插件修复情况\"\"\"\r\n    plugin_path = os.path.join(os.path.dirname(__file__), 'src', 'ida_pro_mcp', 'mcp-plugin.py')\r\n    \r\n    if not os.path.exists(plugin_path):\r\n        print(f\"❌ 插件文件不存在: {plugin_path}\")\r\n        return False\r\n    \r\n    try:\r\n        with open(plugin_path, 'r', encoding='utf-8') as f:\r\n            content = f.read()\r\n    except Exception as e:\r\n        print(f\"❌ 读取插件文件失败: {e}\")\r\n        return False\r\n    \r\n    # 1. 检查是否还有 ida_bytes.find_bytes 调用\r\n    find_bytes_calls = re.findall(r'ida_bytes\\.find_bytes\\s*\\(', content)\r\n    if find_bytes_calls:\r\n        print(f\"❌ 仍有 {len(find_bytes_calls)} 个未修复的 ida_bytes.find_bytes 调用\")\r\n        return False\r\n    else:\r\n        print(\"✅ 所有 ida_bytes.find_bytes 调用已修复\")\r\n    \r\n    # 2. 检查 safe_find_bytes 函数是否存在\r\n    if 'def safe_find_bytes(' not in content:\r\n        print(\"❌ safe_find_bytes 辅助函数未找到\")\r\n        return False\r\n    else:\r\n        print(\"✅ safe_find_bytes 辅助函数存在\")\r\n    \r\n    # 3. 检查 safe_find_bytes 调用数量\r\n    safe_calls = re.findall(r'safe_find_bytes\\s*\\(', content)\r\n    print(f\"✅ 找到 {len(safe_calls)} 个 safe_find_bytes 调用\")\r\n    \r\n    # 4. 检查编码声明\r\n    first_lines = content.split('\\n')[:5]\r\n    has_encoding = any('utf-8' in line and 'coding' in line for line in first_lines)\r\n    if has_encoding:\r\n        print(\"✅ UTF-8 编码声明存在\")\r\n    else:\r\n        print(\"❌ 缺少 UTF-8 编码声明\")\r\n    \r\n    # 5. 检查基本语法\r\n    try:\r\n        compile(content, plugin_path, 'exec')\r\n        print(\"✅ Python 语法检查通过\")\r\n    except SyntaxError as e:\r\n        print(f\"❌ Python 语法错误: {e}\")\r\n        return False\r\n    \r\n    print(\"\\n🎉 所有修复验证通过！\")\r\n    return True\r\n\r\ndef main():\r\n    \"\"\"主函数\"\"\"\r\n    print(\"IDA Pro MCP Plugin 修复验证\")\r\n    print(\"=\" * 40)\r\n    \r\n    success = test_plugin_fixes()\r\n    \r\n    if success:\r\n        print(\"\\n📋 修复摘要:\")\r\n        print(\"• 已修复所有 ida_bytes.find_bytes() API 兼容性问题\")\r\n        print(\"• 实现 safe_find_bytes() 辅助函数用于字节模式搜索\")\r\n        print(\"• 修复文件编码问题\")\r\n        print(\"• 修复基本类型错误\")\r\n        print(\"• 插件可以在 IDA Pro 9.1 环境中正常加载\")\r\n        sys.exit(0)\r\n    else:\r\n        print(\"\\n❌ 修复验证失败，请检查问题\")\r\n        sys.exit(1)\r\n\r\nif __name__ == \"__main__\":\r\n    main()\r\n"}]}