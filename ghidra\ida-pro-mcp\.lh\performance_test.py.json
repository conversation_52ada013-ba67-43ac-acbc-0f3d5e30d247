{"sourceFile": "performance_test.py", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1754225457969, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754225537396, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,10 @@\n from typing import Dict, Any\r\n \r\n def mock_ida_modules():\r\n     \"\"\"模拟IDA模块以进行性能测试\"\"\"\r\n+    import types\r\n+    \r\n     ida_modules = [\r\n         'idaapi', 'ida_kernwin', 'ida_hexrays', 'idautils', 'idc',\r\n         'ida_funcs', 'ida_gdl', 'ida_lines', 'ida_idaapi', 'ida_nalt',\r\n         'ida_bytes', 'ida_typeinf', 'ida_xref', 'ida_entry', 'ida_idd',\r\n@@ -20,19 +22,67 @@\n     ]\r\n     \r\n     for module_name in ida_modules:\r\n         if module_name not in sys.modules:\r\n-            mock_module = type(sys)(module_name)\r\n-            # 添加必要的属性和函数\r\n+            mock_module = types.ModuleType(module_name)\r\n+            \r\n+            # 添加通用属性\r\n             setattr(mock_module, 'BADADDR', 0xFFFFFFFFFFFFFFFF)\r\n             setattr(mock_module, 'get_imagebase', lambda: 0x10000000)\r\n             setattr(mock_module, 'MFF_READ', 1)\r\n             setattr(mock_module, 'MFF_WRITE', 2)\r\n             setattr(mock_module, 'MFF_FAST', 0)\r\n-            setattr(mock_module, 'plugin_t', object)\r\n-            setattr(mock_module, 'PLUGIN_KEEP', 1)\r\n-            setattr(mock_module, 'BaseHTTPRequestHandler', object)\r\n-            setattr(mock_module, 'HTTPServer', object)\r\n+            \r\n+            # 为特定模块添加专门的属性\r\n+            if module_name == 'idaapi':\r\n+                setattr(mock_module, 'plugin_t', type('plugin_t', (), {}))\r\n+                setattr(mock_module, 'PLUGIN_KEEP', 1)\r\n+                setattr(mock_module, 'get_func', lambda x: None)\r\n+                setattr(mock_module, 'insn_t', type('insn_t', (), {}))\r\n+                setattr(mock_module, 'decode_insn', lambda x, y: False)\r\n+                \r\n+            elif module_name == 'ida_funcs':\r\n+                setattr(mock_module, 'func_t', type('func_t', (), {'get_name': lambda: 'test_func'}))\r\n+                setattr(mock_module, 'get_func_name', lambda x: 'test_func')\r\n+                \r\n+            elif module_name == 'ida_kernwin':\r\n+                setattr(mock_module, 'simpleline_t', type('simpleline_t', (), {}))\r\n+                setattr(mock_module, 'get_current_widget', lambda: None)\r\n+                \r\n+            elif module_name == 'ida_hexrays':\r\n+                setattr(mock_module, 'ctree_item_t', type('ctree_item_t', (), {}))\r\n+                setattr(mock_module, 'init_hexrays_plugin', lambda: True)\r\n+                setattr(mock_module, 'OPF_REUSE', 1)\r\n+                setattr(mock_module, 'open_pseudocode', lambda x, y: None)\r\n+                \r\n+            elif module_name == 'idautils':\r\n+                class MockStrings:\r\n+                    def __iter__(self):\r\n+                        # 返回一些模拟字符串\r\n+                        for i in range(5):\r\n+                            yield type('string_item', (), {'ea': 0x1000 + i, '__str__': lambda: f'test_string_{i}'})()\r\n+                \r\n+                setattr(mock_module, 'Strings', MockStrings)\r\n+                setattr(mock_module, 'Functions', lambda: [0x1000, 0x1100, 0x1200])\r\n+                \r\n+            elif module_name == 'idc':\r\n+                setattr(mock_module, 'get_item_size', lambda x: 4)\r\n+                setattr(mock_module, 'generate_disasm_line', lambda x, y: 'mov eax, ebx')\r\n+                setattr(mock_module, 'set_func_cmt', lambda x, y, z: True)\r\n+                \r\n+            elif module_name == 'ida_bytes':\r\n+                setattr(mock_module, 'get_bytes', lambda x, y: b'\\x90\\x90\\x90\\x90')\r\n+                setattr(mock_module, 'has_any_name', lambda x: True)\r\n+                setattr(mock_module, 'find_bytes', lambda x, y, z: 0xFFFFFFFFFFFFFFFF)\r\n+                \r\n+            elif module_name == 'ida_nalt':\r\n+                setattr(mock_module, 'get_tinfo', lambda x, y: False)\r\n+                \r\n+            # 添加HTTP服务器相关的模拟\r\n+            import http.server\r\n+            setattr(mock_module, 'BaseHTTPRequestHandler', http.server.BaseHTTPRequestHandler)\r\n+            setattr(mock_module, 'HTTPServer', http.server.HTTPServer)\r\n+            \r\n             sys.modules[module_name] = mock_module\r\n \r\n def test_plugin_load_time():\r\n     \"\"\"测试插件加载时间\"\"\"\r\n"}, {"date": 1754225585213, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -76,8 +76,26 @@\n                 \r\n             elif module_name == 'ida_nalt':\r\n                 setattr(mock_module, 'get_tinfo', lambda x, y: False)\r\n                 \r\n+            elif module_name == 'ida_typeinf':\r\n+                setattr(mock_module, 'tinfo_t', type('tinfo_t', (), {}))\r\n+                setattr(mock_module, 'parse_decl', lambda x, y, z: (0, ''))\r\n+                \r\n+            elif module_name == 'ida_xref':\r\n+                setattr(mock_module, 'get_first_cref_to', lambda x: 0xFFFFFFFFFFFFFFFF)\r\n+                setattr(mock_module, 'get_next_cref_to', lambda x, y: 0xFFFFFFFFFFFFFFFF)\r\n+                \r\n+            elif module_name == 'ida_entry':\r\n+                setattr(mock_module, 'get_entry_qty', lambda: 1)\r\n+                setattr(mock_module, 'get_entry', lambda x: 0x1000)\r\n+                \r\n+            elif module_name == 'ida_name':\r\n+                setattr(mock_module, 'get_name_ea', lambda x, y: 0xFFFFFFFFFFFFFFFF)\r\n+                \r\n+            elif module_name == 'ida_frame':\r\n+                setattr(mock_module, 'get_frame', lambda x: None)\r\n+                \r\n             # 添加HTTP服务器相关的模拟\r\n             import http.server\r\n             setattr(mock_module, 'BaseHTTPRequestHandler', http.server.BaseHTTPRequestHandler)\r\n             setattr(mock_module, 'HTTPServer', http.server.HTTPServer)\r\n"}], "date": 1754225457969, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\nIDA Pro MCP插件性能测试脚本\r\n测试启动时间和内存占用\r\n\"\"\"\r\n\r\nimport time\r\nimport sys\r\nimport os\r\nimport gc\r\nfrom typing import Dict, Any\r\n\r\ndef mock_ida_modules():\r\n    \"\"\"模拟IDA模块以进行性能测试\"\"\"\r\n    ida_modules = [\r\n        'idaapi', 'ida_kernwin', 'ida_hexrays', 'idautils', 'idc',\r\n        'ida_funcs', 'ida_gdl', 'ida_lines', 'ida_idaapi', 'ida_nalt',\r\n        'ida_bytes', 'ida_typeinf', 'ida_xref', 'ida_entry', 'ida_idd',\r\n        'ida_dbg', 'ida_name', 'ida_ida', 'ida_frame'\r\n    ]\r\n    \r\n    for module_name in ida_modules:\r\n        if module_name not in sys.modules:\r\n            mock_module = type(sys)(module_name)\r\n            # 添加必要的属性和函数\r\n            setattr(mock_module, 'BADADDR', 0xFFFFFFFFFFFFFFFF)\r\n            setattr(mock_module, 'get_imagebase', lambda: 0x10000000)\r\n            setattr(mock_module, 'MFF_READ', 1)\r\n            setattr(mock_module, 'MFF_WRITE', 2)\r\n            setattr(mock_module, 'MFF_FAST', 0)\r\n            setattr(mock_module, 'plugin_t', object)\r\n            setattr(mock_module, 'PLUGIN_KEEP', 1)\r\n            setattr(mock_module, 'BaseHTTPRequestHandler', object)\r\n            setattr(mock_module, 'HTTPServer', object)\r\n            sys.modules[module_name] = mock_module\r\n\r\ndef test_plugin_load_time():\r\n    \"\"\"测试插件加载时间\"\"\"\r\n    print(\"🚀 测试插件加载性能...\")\r\n    \r\n    # 模拟IDA环境\r\n    mock_ida_modules()\r\n    \r\n    # 测试加载时间\r\n    start_time = time.time()\r\n    \r\n    try:\r\n        # 动态加载插件\r\n        import importlib.util\r\n        spec = importlib.util.spec_from_file_location(\r\n            \"mcp_plugin\", \r\n            \"src/ida_pro_mcp/mcp-plugin.py\"\r\n        )\r\n        \r\n        if spec and spec.loader:\r\n            module = importlib.util.module_from_spec(spec)\r\n            load_start = time.time()\r\n            spec.loader.exec_module(module)\r\n            load_time = time.time() - load_start\r\n            \r\n            total_time = time.time() - start_time\r\n            \r\n            print(f\"✅ 插件加载成功\")\r\n            print(f\"   📊 总耗时: {total_time:.3f} 秒\")\r\n            print(f\"   📊 模块执行时间: {load_time:.3f} 秒\")\r\n            \r\n            # 检查是否满足<2秒的要求\r\n            if total_time < 2.0:\r\n                print(f\"   ✅ 启动时间符合要求 (<2秒)\")\r\n            else:\r\n                print(f\"   ⚠️  启动时间超出要求 (>2秒)\")\r\n            \r\n            return True, total_time, module\r\n        else:\r\n            print(\"❌ 无法创建模块规范\")\r\n            return False, 0, None\r\n            \r\n    except Exception as e:\r\n        print(f\"❌ 插件加载失败: {e}\")\r\n        import traceback\r\n        traceback.print_exc()\r\n        return False, 0, None\r\n\r\ndef test_module_initialization(module):\r\n    \"\"\"测试模块初始化性能\"\"\"\r\n    print(\"\\n🔧 测试模块初始化性能...\")\r\n    \r\n    try:\r\n        # 测试延迟模块管理器\r\n        lazy_manager = getattr(module, 'lazy_module_manager', None)\r\n        if lazy_manager:\r\n            start_time = time.time()\r\n            \r\n            # 初始化所有模块\r\n            test_modules = ['control_flow', 'crypto', 'anti_debug', 'license', 'memory_patch', 'string_analysis']\r\n            \r\n            for mod_name in test_modules:\r\n                lazy_manager.initialize_module(mod_name)\r\n            \r\n            init_time = time.time() - start_time\r\n            print(f\"   ✅ {len(test_modules)} 个模块初始化耗时: {init_time:.3f} 秒\")\r\n            \r\n            # 测试模块数据获取\r\n            start_time = time.time()\r\n            for _ in range(100):\r\n                lazy_manager.get_module_data('control_flow')\r\n            data_time = time.time() - start_time\r\n            print(f\"   ✅ 100次数据获取耗时: {data_time:.3f} 秒\")\r\n        \r\n        # 测试缓存系统\r\n        cache = getattr(module, 'analysis_cache', None)\r\n        if cache:\r\n            start_time = time.time()\r\n            \r\n            # 填充缓存\r\n            for i in range(50):\r\n                cache.put(f\"test_func_{i}\", (i,), {}, {\"result\": f\"data_{i}\"})\r\n            \r\n            cache_time = time.time() - start_time\r\n            print(f\"   ✅ 50个缓存条目存储耗时: {cache_time:.3f} 秒\")\r\n            \r\n            # 测试缓存检索\r\n            start_time = time.time()\r\n            hits = 0\r\n            for i in range(100):\r\n                hit, _ = cache.get(f\"test_func_{i % 50}\", (i % 50,), {})\r\n                if hit:\r\n                    hits += 1\r\n            retrieval_time = time.time() - start_time\r\n            print(f\"   ✅ 100次缓存查询耗时: {retrieval_time:.3f} 秒 (命中率: {hits}%)\")\r\n        \r\n        return True\r\n        \r\n    except Exception as e:\r\n        print(f\"❌ 模块初始化测试失败: {e}\")\r\n        return False\r\n\r\ndef test_memory_usage():\r\n    \"\"\"测试内存使用情况\"\"\"\r\n    print(\"\\n💾 测试内存使用...\")\r\n    \r\n    try:\r\n        import psutil\r\n        import os\r\n        \r\n        process = psutil.Process(os.getpid())\r\n        \r\n        # 获取当前内存使用\r\n        memory_before = process.memory_info()\r\n        \r\n        print(f\"   📊 当前内存使用: {memory_before.rss / 1024 / 1024:.2f} MB\")\r\n        \r\n        # 强制垃圾回收\r\n        gc.collect()\r\n        \r\n        memory_after = process.memory_info()\r\n        print(f\"   📊 垃圾回收后: {memory_after.rss / 1024 / 1024:.2f} MB\")\r\n        \r\n        # 内存使用评估\r\n        memory_mb = memory_after.rss / 1024 / 1024\r\n        if memory_mb < 50:  # 假设基准内存占用\r\n            print(f\"   ✅ 内存使用合理 (<50MB)\")\r\n        else:\r\n            print(f\"   ⚠️  内存使用较高 (>{memory_mb:.1f}MB)\")\r\n        \r\n        return True\r\n        \r\n    except ImportError:\r\n        print(\"   ⚠️  psutil未安装，跳过内存测试\")\r\n        return True\r\n    except Exception as e:\r\n        print(f\"   ❌ 内存测试失败: {e}\")\r\n        return False\r\n\r\ndef test_api_response_time(module):\r\n    \"\"\"测试API响应时间\"\"\"\r\n    print(\"\\n⚡测试API响应性能...\")\r\n    \r\n    try:\r\n        # 测试RPC注册表\r\n        rpc_registry = getattr(module, 'rpc_registry', None)\r\n        if rpc_registry and hasattr(rpc_registry, 'methods'):\r\n            method_count = len(rpc_registry.methods)\r\n            print(f\"   📊 已注册API方法: {method_count} 个\")\r\n            \r\n            # 模拟API调用（测试分发机制）\r\n            if method_count > 0:\r\n                start_time = time.time()\r\n                \r\n                # 测试方法查找性能\r\n                for _ in range(1000):\r\n                    method_name = list(rpc_registry.methods.keys())[0]\r\n                    _ = rpc_registry.methods.get(method_name)\r\n                \r\n                lookup_time = time.time() - start_time\r\n                print(f\"   ✅ 1000次方法查找耗时: {lookup_time:.3f} 秒\")\r\n        \r\n        return True\r\n        \r\n    except Exception as e:\r\n        print(f\"❌ API性能测试失败: {e}\")\r\n        return False\r\n\r\ndef generate_performance_report(load_time: float, module) -> Dict[str, Any]:\r\n    \"\"\"生成性能报告\"\"\"\r\n    \r\n    report = {\r\n        \"timestamp\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n        \"load_time_seconds\": round(load_time, 3),\r\n        \"startup_target_met\": load_time < 2.0,\r\n        \"components\": {}\r\n    }\r\n    \r\n    # 检查组件状态\r\n    try:\r\n        if hasattr(module, 'lazy_module_manager'):\r\n            lmm = module.lazy_module_manager\r\n            report[\"components\"][\"lazy_module_manager\"] = {\r\n                \"initialized_modules\": sum(lmm.module_states.values()),\r\n                \"total_modules\": len(lmm.module_states)\r\n            }\r\n        \r\n        if hasattr(module, 'analysis_cache'):\r\n            cache = module.analysis_cache\r\n            stats = cache.get_stats()\r\n            report[\"components\"][\"analysis_cache\"] = {\r\n                \"max_size\": cache.max_size,\r\n                \"current_entries\": stats.get(\"total_entries\", 0),\r\n                \"hit_rate\": stats.get(\"hit_rate\", \"0%\")\r\n            }\r\n        \r\n        if hasattr(module, 'workflow_engine'):\r\n            engine = module.workflow_engine\r\n            report[\"components\"][\"workflow_engine\"] = {\r\n                \"running_tasks\": len(engine.running_tasks),\r\n                \"completed_tasks\": len(engine.completed_tasks),\r\n                \"cached_strategies\": len(engine.strategy_cache)\r\n            }\r\n        \r\n        if hasattr(module, 'rpc_registry'):\r\n            rpc = module.rpc_registry\r\n            report[\"components\"][\"rpc_registry\"] = {\r\n                \"registered_methods\": len(rpc.methods),\r\n                \"unsafe_methods\": len(rpc.unsafe)\r\n            }\r\n            \r\n    except Exception as e:\r\n        report[\"error\"] = str(e)\r\n    \r\n    return report\r\n\r\ndef main():\r\n    \"\"\"主性能测试函数\"\"\"\r\n    print(\"IDA Pro MCP 插件性能测试\")\r\n    print(\"=\" * 50)\r\n    \r\n    # 测试插件加载\r\n    success, load_time, module = test_plugin_load_time()\r\n    \r\n    if not success:\r\n        print(\"❌ 性能测试失败：插件无法加载\")\r\n        return False\r\n    \r\n    # 运行各项性能测试\r\n    init_success = test_module_initialization(module)\r\n    memory_success = test_memory_usage()\r\n    api_success = test_api_response_time(module)\r\n    \r\n    # 生成性能报告\r\n    report = generate_performance_report(load_time, module)\r\n    \r\n    print(\"\\n\" + \"=\" * 50)\r\n    print(\"📋 性能测试报告\")\r\n    print(\"=\" * 50)\r\n    \r\n    print(f\"📅 测试时间: {report['timestamp']}\")\r\n    print(f\"⏱️  启动时间: {report['load_time_seconds']} 秒\")\r\n    print(f\"🎯 启动目标: {'✅ 达成' if report['startup_target_met'] else '❌ 未达成'} (<2秒)\")\r\n    \r\n    print(\"\\n🔧 组件状态:\")\r\n    for component, details in report.get(\"components\", {}).items():\r\n        print(f\"   📦 {component}:\")\r\n        for key, value in details.items():\r\n            print(f\"      - {key}: {value}\")\r\n    \r\n    # 综合评估\r\n    all_tests_passed = success and init_success and memory_success and api_success\r\n    startup_ok = report['startup_target_met']\r\n    \r\n    print(\"\\n🏆 综合评估:\")\r\n    if all_tests_passed and startup_ok:\r\n        print(\"✅ 所有性能测试通过！\")\r\n        print(\"🚀 插件已优化完成，满足所有性能要求\")\r\n        print(f\"   - 启动时间: {load_time:.3f}s < 2s ✅\")\r\n        print(f\"   - 组件加载: 正常 ✅\")\r\n        print(f\"   - 内存管理: 良好 ✅\")\r\n        print(f\"   - API响应: 快速 ✅\")\r\n        \r\n        return True\r\n    else:\r\n        print(\"⚠️  部分性能测试需要改进:\")\r\n        if not startup_ok:\r\n            print(\"   - 启动时间超过2秒限制\")\r\n        if not all_tests_passed:\r\n            print(\"   - 某些组件测试失败\")\r\n        \r\n        return False\r\n\r\nif __name__ == \"__main__\":\r\n    success = main()\r\n    sys.exit(0 if success else 1)\r\n"}]}