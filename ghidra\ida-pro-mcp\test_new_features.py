#!/usr/bin/env python3
"""
测试IDA Pro MCP新功能
"""
import json
import subprocess
import sys

def test_mcp_functions():
    """测试MCP功能"""
    
    # 要测试的新功能列表
    test_functions = [
        "get_cache_statistics",
        "get_lazy_module_stats", 
        "test_lazy_initialization",
        "clear_analysis_cache",
        "get_workflow_status",
        "detect_protection_type",
        "analyze_javascript_patterns",
        "discover_api_endpoints",
        "extract_web_resources"
    ]
    
    print("🧪 测试IDA Pro MCP新功能...")
    
    for func in test_functions:
        print(f"\n📋 测试功能: {func}")
        
        # 创建JSON-RPC请求
        request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": func,
            "params": []
        }
        
        try:
            # 通过stdio调用MCP服务器
            proc = subprocess.Popen([
                ".venv/Scripts/python.exe", "-m", "ida_pro_mcp.server", "--transport", "stdio"
            ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # 发送请求
            response, error = proc.communicate(input=json.dumps(request) + "\n", timeout=5)
            
            if error:
                print(f"  ❌ 错误: {error}")
            else:
                try:
                    result = json.loads(response)
                    if "error" in result:
                        print(f"  ⚠️  API错误: {result['error']['message']}")
                    else:
                        print(f"  ✅ 功能可用")
                except json.JSONDecodeError:
                    print(f"  ❌ 无效响应: {response}")
                    
        except subprocess.TimeoutExpired:
            print(f"  ❌ 超时")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    print("\n🎯 新功能验证完成!")

if __name__ == "__main__":
    test_mcp_functions()
