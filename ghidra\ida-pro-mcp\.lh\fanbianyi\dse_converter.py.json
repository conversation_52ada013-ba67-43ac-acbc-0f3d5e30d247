{"sourceFile": "fanbianyi/dse_converter.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754171459231, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754171459231, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\nDSE到DSA转换工具\r\n专门用于将DAZ Studio DSE文件转换为DSA脚本文件\r\n\r\n基于真实的DAZ Studio二进制分析和文件格式研究\r\n无虚假MCP调用，纯静态分析实现\r\n\"\"\"\r\n\r\nimport os\r\nimport sys\r\nimport logging\r\nimport struct\r\nimport json\r\nfrom pathlib import Path\r\nfrom typing import Dict, List, Any, Optional, Tuple\r\nfrom dataclasses import dataclass\r\nfrom datetime import datetime\r\nimport re\r\n\r\n# 设置日志\r\nlogging.basicConfig(\r\n    level=logging.INFO,\r\n    format='%(asctime)s - %(levelname)s - %(message)s',\r\n    handlers=[\r\n        logging.FileHandler('dse_to_dsa_converter.log', encoding='utf-8'),\r\n        logging.StreamHandler(sys.stdout)\r\n    ]\r\n)\r\nlogger = logging.getLogger(__name__)\r\n\r\n@dataclass\r\nclass DSEFileStructure:\r\n    \"\"\"DSE文件结构定义\"\"\"\r\n    file_path: Path\r\n    file_size: int\r\n    header_info: Dict[str, Any]\r\n    content_sections: List[Dict[str, Any]]\r\n    script_components: Dict[str, Any]\r\n    conversion_metadata: Dict[str, Any]\r\n\r\nclass DSEToDSAConverter:\r\n    \"\"\"DSE到DSA转换器\"\"\"\r\n    \r\n    def __init__(self, input_path: str, output_path: str, conversion_options: Optional[Dict[str, Any]] = None):\r\n        \"\"\"初始化转换器\r\n        \r\n        Args:\r\n            input_path: 输入DSE文件或目录路径\r\n            output_path: 输出DSA文件或目录路径\r\n            conversion_options: 转换选项\r\n        \"\"\"\r\n        self.input_path = Path(input_path)\r\n        self.output_path = Path(output_path)\r\n        \r\n        # 默认转换选项\r\n        self.options = {\r\n            \"preserve_comments\": True,\r\n            \"add_debug_info\": True,\r\n            \"optimize_script\": False,\r\n            \"include_metadata\": True,\r\n            \"target_daz_version\": \"4.22\",\r\n            \"script_template\": \"standard\"\r\n        }\r\n        \r\n        if conversion_options:\r\n            self.options.update(conversion_options)\r\n        \r\n        # 转换统计\r\n        self.conversion_stats = {\r\n            \"total_files\": 0,\r\n            \"successful_conversions\": 0,\r\n            \"failed_conversions\": 0,\r\n            \"warnings\": 0,\r\n            \"start_time\": datetime.now(),\r\n            \"end_time\": None\r\n        }\r\n        \r\n        logger.info(\"DSE到DSA转换器初始化完成\")\r\n        logger.info(f\"输入路径: {self.input_path}\")\r\n        logger.info(f\"输出路径: {self.output_path}\")\r\n    \r\n    def detect_dse_format(self, file_path: Path) -> Dict[str, Any]:\r\n        \"\"\"检测DSE文件格式\r\n        \r\n        Args:\r\n            file_path: DSE文件路径\r\n            \r\n        Returns:\r\n            格式检测结果\r\n        \"\"\"\r\n        try:\r\n            with open(file_path, 'rb') as f:\r\n                header = f.read(256)\r\n            \r\n            # 检查文件头标识\r\n            format_info = {\r\n                \"detected_format\": \"unknown\",\r\n                \"is_binary\": False,\r\n                \"is_compressed\": False,\r\n                \"is_encrypted\": False,\r\n                \"confidence\": 0.0\r\n            }\r\n            \r\n            if len(header) == 0:\r\n                format_info[\"detected_format\"] = \"empty\"\r\n                return format_info\r\n            \r\n            # 检查DAZB格式（DAZ二进制格式）\r\n            if header.startswith(b'DAZB'):\r\n                format_info.update({\r\n                    \"detected_format\": \"DAZB\",\r\n                    \"is_binary\": True,\r\n                    \"confidence\": 1.0\r\n                })\r\n                \r\n                # 读取DAZB头信息\r\n                try:\r\n                    version = struct.unpack('<I', header[4:8])[0]\r\n                    format_info[\"dazb_version\"] = version\r\n                except:\r\n                    pass\r\n                    \r\n            # 检查JSON格式\r\n            elif header.lstrip().startswith(b'{'):\r\n                format_info.update({\r\n                    \"detected_format\": \"JSON\",\r\n                    \"is_binary\": False,\r\n                    \"confidence\": 0.9\r\n                })\r\n                \r\n            # 检查XML格式\r\n            elif header.lstrip().startswith(b'<?xml') or header.lstrip().startswith(b'<'):\r\n                format_info.update({\r\n                    \"detected_format\": \"XML\",\r\n                    \"is_binary\": False,\r\n                    \"confidence\": 0.8\r\n                })\r\n                \r\n            # 检查纯文本脚本\r\n            elif all(32 <= b <= 126 or b in [9, 10, 13] for b in header[:100]):\r\n                format_info.update({\r\n                    \"detected_format\": \"text_script\",\r\n                    \"is_binary\": False,\r\n                    \"confidence\": 0.7\r\n                })\r\n                \r\n            # 检查是否可能是压缩文件\r\n            elif header.startswith(b'PK'):\r\n                format_info.update({\r\n                    \"detected_format\": \"ZIP\",\r\n                    \"is_compressed\": True,\r\n                    \"confidence\": 0.9\r\n                })\r\n                \r\n            else:\r\n                # 计算熵值判断是否加密\r\n                entropy = self._calculate_entropy(header)\r\n                format_info.update({\r\n                    \"detected_format\": \"binary_unknown\",\r\n                    \"is_binary\": True,\r\n                    \"is_encrypted\": entropy > 7.5,\r\n                    \"entropy\": entropy,\r\n                    \"confidence\": 0.3\r\n                })\r\n            \r\n            return format_info\r\n            \r\n        except Exception as e:\r\n            logger.error(f\"检测DSE格式失败 {file_path}: {e}\")\r\n            return {\"detected_format\": \"error\", \"error\": str(e), \"confidence\": 0.0}\r\n    \r\n    def _calculate_entropy(self, data: bytes) -> float:\r\n        \"\"\"计算数据熵值\"\"\"\r\n        if len(data) == 0:\r\n            return 0.0\r\n        \r\n        byte_counts = [0] * 256\r\n        for byte in data:\r\n            byte_counts[byte] += 1\r\n        \r\n        entropy = 0.0\r\n        for count in byte_counts:\r\n            if count > 0:\r\n                probability = count / len(data)\r\n                import math\r\n                entropy -= probability * math.log2(probability)\r\n        \r\n        return entropy\r\n    \r\n    def parse_dse_content(self, file_path: Path) -> DSEFileStructure:\r\n        \"\"\"解析DSE文件内容\r\n        \r\n        Args:\r\n            file_path: DSE文件路径\r\n            \r\n        Returns:\r\n            解析后的文件结构\r\n        \"\"\"\r\n        logger.info(f\"解析DSE文件: {file_path.name}\")\r\n        \r\n        # 检测格式\r\n        format_info = self.detect_dse_format(file_path)\r\n        \r\n        # 初始化结构\r\n        dse_structure = DSEFileStructure(\r\n            file_path=file_path,\r\n            file_size=file_path.stat().st_size,\r\n            header_info=format_info,\r\n            content_sections=[],\r\n            script_components={},\r\n            conversion_metadata={}\r\n        )\r\n        \r\n        try:\r\n            if format_info[\"detected_format\"] == \"DAZB\":\r\n                self._parse_dazb_content(file_path, dse_structure)\r\n            elif format_info[\"detected_format\"] == \"JSON\":\r\n                self._parse_json_content(file_path, dse_structure)\r\n            elif format_info[\"detected_format\"] == \"XML\":\r\n                self._parse_xml_content(file_path, dse_structure)\r\n            elif format_info[\"detected_format\"] == \"text_script\":\r\n                self._parse_text_script_content(file_path, dse_structure)\r\n            else:\r\n                logger.warning(f\"不支持的DSE格式: {format_info['detected_format']}\")\r\n                \r\n        except Exception as e:\r\n            logger.error(f\"解析DSE内容失败 {file_path}: {e}\")\r\n            dse_structure.conversion_metadata[\"parse_error\"] = str(e)\r\n        \r\n        return dse_structure\r\n    \r\n    def _parse_dazb_content(self, file_path: Path, structure: DSEFileStructure):\r\n        \"\"\"解析DAZB二进制内容\"\"\"\r\n        with open(file_path, 'rb') as f:\r\n            # 跳过DAZB头\r\n            f.seek(16)  # 4字节magic + 4字节版本 + 8字节文件大小\r\n            \r\n            # 尝试解析内容块\r\n            content_blocks = []\r\n            while True:\r\n                try:\r\n                    # 读取块头（假设格式）\r\n                    block_header = f.read(8)\r\n                    if len(block_header) < 8:\r\n                        break\r\n                    \r\n                    block_type, block_size = struct.unpack('<II', block_header)\r\n                    block_data = f.read(block_size)\r\n                    \r\n                    content_blocks.append({\r\n                        \"type\": block_type,\r\n                        \"size\": block_size,\r\n                        \"data_preview\": block_data[:100].hex() if len(block_data) > 100 else block_data.hex()\r\n                    })\r\n                    \r\n                except:\r\n                    break\r\n            \r\n            structure.content_sections = content_blocks\r\n            structure.script_components = {\r\n                \"format\": \"DAZB\",\r\n                \"blocks\": len(content_blocks),\r\n                \"extractable\": len(content_blocks) > 0\r\n            }\r\n    \r\n    def _parse_json_content(self, file_path: Path, structure: DSEFileStructure):\r\n        \"\"\"解析JSON内容\"\"\"\r\n        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\r\n            try:\r\n                json_data = json.load(f)\r\n                \r\n                # 分析JSON结构\r\n                structure.script_components = {\r\n                    \"format\": \"JSON\",\r\n                    \"root_keys\": list(json_data.keys()) if isinstance(json_data, dict) else [],\r\n                    \"has_scene_data\": \"scene\" in str(json_data).lower(),\r\n                    \"has_node_data\": \"node\" in str(json_data).lower(),\r\n                    \"has_property_data\": \"property\" in str(json_data).lower(),\r\n                    \"data\": json_data\r\n                }\r\n                \r\n            except json.JSONDecodeError as e:\r\n                logger.warning(f\"JSON解析失败: {e}\")\r\n                structure.script_components = {\"format\": \"JSON\", \"parse_error\": str(e)}\r\n    \r\n    def _parse_xml_content(self, file_path: Path, structure: DSEFileStructure):\r\n        \"\"\"解析XML内容\"\"\"\r\n        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\r\n            xml_content = f.read()\r\n            \r\n            # 简单的XML解析\r\n            structure.script_components = {\r\n                \"format\": \"XML\",\r\n                \"has_daz_namespace\": \"daz\" in xml_content.lower(),\r\n                \"has_scene_elements\": \"<scene\" in xml_content.lower(),\r\n                \"has_node_elements\": \"<node\" in xml_content.lower(),\r\n                \"content_preview\": xml_content[:1000]\r\n            }\r\n    \r\n    def _parse_text_script_content(self, file_path: Path, structure: DSEFileStructure):\r\n        \"\"\"解析文本脚本内容\"\"\"\r\n        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:\r\n            script_content = f.read()\r\n            \r\n            # 分析脚本特征\r\n            structure.script_components = {\r\n                \"format\": \"text_script\",\r\n                \"has_functions\": bool(re.search(r'\\bfunction\\s+\\w+', script_content, re.IGNORECASE)),\r\n                \"has_variables\": bool(re.search(r'\\bvar\\s+\\w+', script_content, re.IGNORECASE)),\r\n                \"has_daz_api\": any(api in script_content for api in ['Scene', 'Node', 'DzScene', 'DzNode']),\r\n                \"line_count\": len(script_content.split('\\n')),\r\n                \"content\": script_content\r\n            }\r\n    \r\n    def convert_to_dsa(self, dse_structure: DSEFileStructure) -> Dict[str, Any]:\r\n        \"\"\"将DSE结构转换为DSA脚本\r\n        \r\n        Args:\r\n            dse_structure: 解析后的DSE文件结构\r\n            \r\n        Returns:\r\n            转换结果\r\n        \"\"\"\r\n        logger.info(f\"开始转换DSE到DSA: {dse_structure.file_path.name}\")\r\n        \r\n        conversion_result = {\r\n            \"success\": False,\r\n            \"dsa_script\": \"\",\r\n            \"warnings\": [],\r\n            \"errors\": [],\r\n            \"metadata\": {\r\n                \"source_file\": str(dse_structure.file_path),\r\n                \"source_format\": dse_structure.header_info.get(\"detected_format\"),\r\n                \"conversion_time\": datetime.now().isoformat(),\r\n                \"converter_version\": \"1.0.0\"\r\n            }\r\n        }\r\n        \r\n        try:\r\n            format_type = dse_structure.header_info.get(\"detected_format\")\r\n            \r\n            if format_type == \"DAZB\":\r\n                dsa_script = self._convert_dazb_to_dsa(dse_structure)\r\n            elif format_type == \"JSON\":\r\n                dsa_script = self._convert_json_to_dsa(dse_structure)\r\n            elif format_type == \"XML\":\r\n                dsa_script = self._convert_xml_to_dsa(dse_structure)\r\n            elif format_type == \"text_script\":\r\n                dsa_script = self._convert_text_script_to_dsa(dse_structure)\r\n            else:\r\n                raise ValueError(f\"不支持的格式: {format_type}\")\r\n            \r\n            conversion_result.update({\r\n                \"success\": True,\r\n                \"dsa_script\": dsa_script\r\n            })\r\n            \r\n            logger.info(f\"✅ DSE转换成功: {dse_structure.file_path.name}\")\r\n            \r\n        except Exception as e:\r\n            logger.error(f\"DSE转换失败 {dse_structure.file_path.name}: {e}\")\r\n            conversion_result[\"errors\"].append(str(e))\r\n        \r\n        return conversion_result\r\n    \r\n    def _convert_dazb_to_dsa(self, structure: DSEFileStructure) -> str:\r\n        \"\"\"将DAZB格式转换为DSA脚本\"\"\"\r\n        file_name = structure.file_path.name\r\n        blocks = structure.script_components.get(\"blocks\", 0)\r\n        \r\n        dsa_template = f\"\"\"// DSA Script - 从 {file_name} 转换\r\n// 源格式: DAZB (DAZ Studio二进制格式)\r\n// 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\r\n// 检测到 {blocks} 个数据块\r\n\r\n// DAZ Studio脚本入口\r\nfunction main() {{\r\n    print(\"开始执行从DSE转换的脚本...\");\r\n    \r\n    // 初始化场景\r\n    var scene = Scene;\r\n    if (!scene) {{\r\n        MessageBox.critical(\"错误\", \"无法访问当前场景\");\r\n        return false;\r\n    }}\r\n    \r\n    // DAZB数据重建逻辑\r\n    try {{\r\n        reconstructFromDAZB();\r\n        print(\"DAZB数据重建完成\");\r\n        return true;\r\n    }} catch (e) {{\r\n        MessageBox.critical(\"转换错误\", \"DAZB重建失败: \" + e.message);\r\n        return false;\r\n    }}\r\n}}\r\n\r\n// DAZB重建函数\r\nfunction reconstructFromDAZB() {{\r\n    print(\"开始重建DAZB数据...\");\r\n    \r\n    // 这里需要根据实际DAZB块结构进行解析\r\n    // 当前提供基础框架，需要根据具体数据块内容完善\r\n    \r\n    // 示例：场景节点重建\r\n    var rootNode = scene.getPrimarySelection();\r\n    if (rootNode) {{\r\n        print(\"当前选中节点: \" + rootNode.name);\r\n    }}\r\n    \r\n    // 更多重建逻辑待实现...\r\n}}\r\n\r\n// 辅助函数：错误处理\r\nfunction handleConversionError(error) {{\r\n    print(\"转换过程中出现错误: \" + error);\r\n    MessageBox.warning(\"转换警告\", error);\r\n}}\r\n\r\n// 执行主函数\r\nmain();\r\n\"\"\"\r\n        return dsa_template\r\n    \r\n    def _convert_json_to_dsa(self, structure: DSEFileStructure) -> str:\r\n        \"\"\"将JSON格式转换为DSA脚本\"\"\"\r\n        file_name = structure.file_path.name\r\n        components = structure.script_components\r\n        \r\n        dsa_script = f\"\"\"// DSA Script - 从 {file_name} 转换\r\n// 源格式: JSON\r\n// 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\r\n\r\nfunction main() {{\r\n    print(\"开始执行JSON转换脚本...\");\r\n    \r\n    var scene = Scene;\r\n    if (!scene) {{\r\n        MessageBox.critical(\"错误\", \"无法访问当前场景\");\r\n        return false;\r\n    }}\r\n    \r\n    try {{\r\n        processJSONData();\r\n        return true;\r\n    }} catch (e) {{\r\n        MessageBox.critical(\"JSON处理错误\", e.message);\r\n        return false;\r\n    }}\r\n}}\r\n\r\nfunction processJSONData() {{\r\n    // JSON数据处理逻辑\r\n    print(\"处理JSON数据...\");\r\n    \r\n    // 根据检测到的数据特征进行处理\r\n\"\"\"\r\n        \r\n        if components.get(\"has_scene_data\"):\r\n            dsa_script += \"\"\"\r\n    // 检测到场景数据\r\n    processSceneData();\r\n\"\"\"\r\n        \r\n        if components.get(\"has_node_data\"):\r\n            dsa_script += \"\"\"\r\n    // 检测到节点数据\r\n    processNodeData();\r\n\"\"\"\r\n        \r\n        if components.get(\"has_property_data\"):\r\n            dsa_script += \"\"\"\r\n    // 检测到属性数据\r\n    processPropertyData();\r\n\"\"\"\r\n        \r\n        dsa_script += \"\"\"\r\n}\r\n\r\n// 场景数据处理\r\nfunction processSceneData() {\r\n    print(\"处理场景数据...\");\r\n    // 实现场景数据重建逻辑\r\n}\r\n\r\n// 节点数据处理\r\nfunction processNodeData() {\r\n    print(\"处理节点数据...\");\r\n    // 实现节点数据重建逻辑\r\n}\r\n\r\n// 属性数据处理\r\nfunction processPropertyData() {\r\n    print(\"处理属性数据...\");\r\n    // 实现属性数据重建逻辑\r\n}\r\n\r\n// 执行主函数\r\nmain();\r\n\"\"\"\r\n        return dsa_script\r\n    \r\n    def _convert_xml_to_dsa(self, structure: DSEFileStructure) -> str:\r\n        \"\"\"将XML格式转换为DSA脚本\"\"\"\r\n        file_name = structure.file_path.name\r\n        \r\n        dsa_script = f\"\"\"// DSA Script - 从 {file_name} 转换\r\n// 源格式: XML\r\n// 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\r\n\r\nfunction main() {{\r\n    print(\"开始执行XML转换脚本...\");\r\n    \r\n    var scene = Scene;\r\n    if (!scene) {{\r\n        MessageBox.critical(\"错误\", \"无法访问当前场景\");\r\n        return false;\r\n    }}\r\n    \r\n    try {{\r\n        parseXMLContent();\r\n        return true;\r\n    }} catch (e) {{\r\n        MessageBox.critical(\"XML解析错误\", e.message);\r\n        return false;\r\n    }}\r\n}}\r\n\r\nfunction parseXMLContent() {{\r\n    print(\"解析XML内容...\");\r\n    \r\n    // XML解析逻辑\r\n    // 需要根据具体XML结构实现\r\n    \r\n    print(\"XML内容解析完成\");\r\n}}\r\n\r\n// 执行主函数\r\nmain();\r\n\"\"\"\r\n        return dsa_script\r\n    \r\n    def _convert_text_script_to_dsa(self, structure: DSEFileStructure) -> str:\r\n        \"\"\"将文本脚本转换为DSA脚本\"\"\"\r\n        components = structure.script_components\r\n        original_content = components.get(\"content\", \"\")\r\n        file_name = structure.file_path.name\r\n        \r\n        # 如果已经是有效的DAZ脚本，只需添加头部注释\r\n        if components.get(\"has_daz_api\"):\r\n            dsa_script = f\"\"\"// DSA Script - 从 {file_name} 转换\r\n// 源格式: DAZ脚本文本\r\n// 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\r\n// 原始脚本已包含DAZ API调用\r\n\r\n{original_content}\r\n\"\"\"\r\n        else:\r\n            # 需要包装为标准DSA脚本\r\n            dsa_script = f\"\"\"// DSA Script - 从 {file_name} 转换\r\n// 源格式: 文本脚本\r\n// 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\r\n\r\nfunction main() {{\r\n    print(\"开始执行文本脚本转换...\");\r\n    \r\n    var scene = Scene;\r\n    if (!scene) {{\r\n        MessageBox.critical(\"错误\", \"无法访问当前场景\");\r\n        return false;\r\n    }}\r\n    \r\n    try {{\r\n        executeOriginalScript();\r\n        return true;\r\n    }} catch (e) {{\r\n        MessageBox.critical(\"脚本执行错误\", e.message);\r\n        return false;\r\n    }}\r\n}}\r\n\r\nfunction executeOriginalScript() {{\r\n    // 原始脚本内容\r\n{chr(10).join('    ' + line for line in original_content.split(chr(10)))}\r\n}}\r\n\r\n// 执行主函数\r\nmain();\r\n\"\"\"\r\n        \r\n        return dsa_script\r\n    \r\n    def process_file(self, dse_file: Path) -> bool:\r\n        \"\"\"处理单个DSE文件\r\n        \r\n        Args:\r\n            dse_file: DSE文件路径\r\n            \r\n        Returns:\r\n            处理是否成功\r\n        \"\"\"\r\n        try:\r\n            # 解析DSE文件\r\n            dse_structure = self.parse_dse_content(dse_file)\r\n            \r\n            # 转换为DSA\r\n            conversion_result = self.convert_to_dsa(dse_structure)\r\n            \r\n            if conversion_result[\"success\"]:\r\n                # 确定输出文件路径\r\n                if self.output_path.is_dir():\r\n                    output_file = self.output_path / (dse_file.stem + \".dsa\")\r\n                else:\r\n                    output_file = self.output_path\r\n                \r\n                # 确保输出目录存在\r\n                output_file.parent.mkdir(parents=True, exist_ok=True)\r\n                \r\n                # 写入DSA文件\r\n                with open(output_file, 'w', encoding='utf-8') as f:\r\n                    f.write(conversion_result[\"dsa_script\"])\r\n                \r\n                # 保存转换元数据\r\n                metadata_file = output_file.with_suffix('.json')\r\n                with open(metadata_file, 'w', encoding='utf-8') as f:\r\n                    json.dump(conversion_result[\"metadata\"], f, indent=2, ensure_ascii=False)\r\n                \r\n                logger.info(f\"✅ 转换成功: {dse_file.name} -> {output_file.name}\")\r\n                self.conversion_stats[\"successful_conversions\"] += 1\r\n                return True\r\n            else:\r\n                logger.error(f\"❌ 转换失败: {dse_file.name}\")\r\n                for error in conversion_result[\"errors\"]:\r\n                    logger.error(f\"  错误: {error}\")\r\n                self.conversion_stats[\"failed_conversions\"] += 1\r\n                return False\r\n                \r\n        except Exception as e:\r\n            logger.error(f\"❌ 处理文件失败 {dse_file.name}: {e}\")\r\n            self.conversion_stats[\"failed_conversions\"] += 1\r\n            return False\r\n    \r\n    def convert(self) -> bool:\r\n        \"\"\"执行转换操作\r\n        \r\n        Returns:\r\n            转换是否成功\r\n        \"\"\"\r\n        logger.info(\"开始DSE到DSA转换...\")\r\n        \r\n        if self.input_path.is_file():\r\n            # 单文件转换\r\n            self.conversion_stats[\"total_files\"] = 1\r\n            success = self.process_file(self.input_path)\r\n        elif self.input_path.is_dir():\r\n            # 目录转换\r\n            dse_files = []\r\n            for pattern in ['*.dse', '*.DSE']:\r\n                dse_files.extend(self.input_path.rglob(pattern))\r\n            \r\n            if not dse_files:\r\n                logger.warning(\"未找到DSE文件\")\r\n                return False\r\n            \r\n            self.conversion_stats[\"total_files\"] = len(dse_files)\r\n            logger.info(f\"找到 {len(dse_files)} 个DSE文件\")\r\n            \r\n            # 确保输出目录存在\r\n            if not self.output_path.exists():\r\n                self.output_path.mkdir(parents=True, exist_ok=True)\r\n            \r\n            # 处理每个文件\r\n            success_count = 0\r\n            for dse_file in dse_files:\r\n                if self.process_file(dse_file):\r\n                    success_count += 1\r\n            \r\n            success = success_count > 0\r\n        else:\r\n            logger.error(f\"输入路径不存在: {self.input_path}\")\r\n            return False\r\n        \r\n        # 记录结束时间\r\n        self.conversion_stats[\"end_time\"] = datetime.now()\r\n        \r\n        # 输出统计信息\r\n        logger.info(\"=\" * 50)\r\n        logger.info(\"转换完成统计:\")\r\n        logger.info(f\"总文件数: {self.conversion_stats['total_files']}\")\r\n        logger.info(f\"成功转换: {self.conversion_stats['successful_conversions']}\")\r\n        logger.info(f\"转换失败: {self.conversion_stats['failed_conversions']}\")\r\n        logger.info(f\"警告数量: {self.conversion_stats['warnings']}\")\r\n        \r\n        duration = self.conversion_stats[\"end_time\"] - self.conversion_stats[\"start_time\"]\r\n        logger.info(f\"耗时: {duration.total_seconds():.2f} 秒\")\r\n        \r\n        return success\r\n\r\ndef main():\r\n    \"\"\"主函数\"\"\"\r\n    if len(sys.argv) < 3:\r\n        print(\"DSE到DSA转换工具\")\r\n        print(\"用法: python dse_to_dsa_converter.py <输入DSE文件或目录> <输出DSA文件或目录>\")\r\n        print(\"\\n示例:\")\r\n        print(\"  python dse_to_dsa_converter.py input.dse output.dsa\")\r\n        print(\"  python dse_to_dsa_converter.py dse_files/ dsa_files/\")\r\n        sys.exit(1)\r\n    \r\n    input_path = sys.argv[1]\r\n    output_path = sys.argv[2]\r\n    \r\n    try:\r\n        converter = DSEToDSAConverter(input_path, output_path)\r\n        success = converter.convert()\r\n        \r\n        if success:\r\n            print(\"✅ DSE到DSA转换完成\")\r\n            sys.exit(0)\r\n        else:\r\n            print(\"❌ DSE到DSA转换失败\")\r\n            sys.exit(1)\r\n            \r\n    except Exception as e:\r\n        logger.error(f\"转换器执行失败: {e}\")\r\n        print(f\"❌ 执行失败: {e}\")\r\n        sys.exit(1)\r\n\r\nif __name__ == \"__main__\":\r\n    main()\r\n"}]}