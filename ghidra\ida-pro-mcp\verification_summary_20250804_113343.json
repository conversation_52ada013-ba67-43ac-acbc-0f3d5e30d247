{"timestamp": "2025-08-04T11:33:43.406659", "summary": {"syntax_check": {"pass": 2, "total": 2}, "fix_check": {"pass": 4, "total": 4}, "doc_check": {"pass": 7, "total": 7}, "overall": {"pass": 13, "total": 13, "success_rate": 100.0, "status": "EXCELLENT", "recommendation": "修复质量优秀，可以部署"}}, "details": {"syntax_results": [{"file": "src/ida_pro_mcp/mcp-plugin.py", "status": "pass"}, {"file": "src/ida_pro_mcp/server_generated.py", "status": "pass"}], "fix_results": [{"check": "参数类型修复", "status": "pass", "pattern": "json.loads"}, {"check": "指令常量修复", "status": "pass", "pattern": "ida_allins.NN_"}, {"check": "上下文检测修复", "status": "pass", "pattern": "check_local_variable_exists"}, {"check": "输出验证修复", "status": "pass", "pattern": "disassembly_function.update(return_type="}], "doc_results": [{"document": "comprehensive_fix_documentation.md", "status": "pass", "size": 6173}, {"document": "code_modification_recommendations.md", "status": "pass", "size": 7597}, {"document": "verification_test_plan.md", "status": "pass", "size": 5391}, {"document": "parameter_fix_validation_report.md", "status": "pass", "size": 1295}, {"document": "instruction_constants_fix_report.md", "status": "pass", "size": 754}, {"document": "disassemble_function_fix_report.md", "status": "pass", "size": 851}, {"document": "local_variable_context_fix_report.md", "status": "pass", "size": 1024}]}}