{"sourceFile": "docs/任务.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754221233116, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754221233116, "name": "Commit-0", "content": "## 任務拆分 - clearAllTasks 模式\r\n\r\n## 拆分策略\r\n\r\n1. **按功能分解** - 獨立可測試的子功能，明確輸入輸出\r\n2. **按技術層次分解** - 沿架構層次分離任務，確保接口明確\r\n3. **按開發階段分解** - 核心功能先行，優化功能後續\r\n4. **按風險分解** - 隔離高風險部分，降低整體風險\r\n\r\n## 任務質量審核\r\n\r\n1. **任務原子性** - 每個任務足夠小且具體，可獨立完成\r\n2. **依賴關係** - 任務依賴形成有向無環圖，避免循環依賴\r\n3. **描述完整性** - 每個任務描述清晰準確，包含必要上下文\r\n\r\n## 任務清單\r\n\r\n### 任務 1：延迟初始化框架实现\r\n\r\n**ID:** `37c67873-a282-4145-9a30-54f68f415c14`\r\n**描述:** 基于现有RPCRegistry机制扩展LazyModuleManager类，实现零配置的模块延迟初始化系统。包含模块状态管理、初始化触发机制、使用统计追踪功能。重点集成到现有装饰器链(@jsonrpc + @idaread/@idawrite)中，确保线程安全和API兼容性。\r\n\r\n**注意事項:** 必须保持与现有装饰器模式完全兼容，不能破坏现有API接口。延迟初始化逻辑要轻量级，避免影响启动性能。\r\n\r\n**實現指南:** 创建LazyModuleManager类，利用现有的RPCRegistry.register机制。实现lazy_init_module装饰器，在现有装饰器链中插入延迟初始化逻辑。设计模块状态管理字典，...\r\n\r\n**驗證標準:** LazyModuleManager类正确实现，lazy_init_module装饰器可正常工作，现有@jsonrpc函数保持完全兼容，延迟初始化逻辑不影响启动时间，线程安全机制正常运作\r\n\r\n**依賴任務:** \r\n\r\n### 任務 2：代码压缩与结构优化\r\n\r\n**ID:** `58d041a6-a3c6-47c8-ba6b-9a303a261d55`\r\n**描述:** 对现有63KB代码进行系统性压缩优化，包括冗余注释删除、导入语句合并、重复代码提取、函数内联优化。目标减少8-10KB体积为后续模块添加预留空间，同时保持代码可读性和维护性。\r\n\r\n**注意事項:** 压缩过程中必须保持所有现有功能不变，确保类型注解完整性，避免破坏现有的错误处理逻辑\r\n\r\n**實現指南:** 分析现有代码结构，识别压缩机会：1.精简注释保留核心API文档 2.合并重复的ida_*导入语句 3.提取重复的异常处理逻辑为公共函数 4.内联parse_address等小工具函数到调用点 5.优化...\r\n\r\n**驗證標準:** 文件大小减少至55KB以下，所有现有@jsonrpc函数功能保持不变，类型注解完整，代码仍具备良好可读性，无语法或逻辑错误\r\n\r\n**依賴任務:** \r\n\r\n### 任務 3：控制流分析模块实现\r\n\r\n**ID:** `c18df76f-9996-440f-baf3-37936ba229af`\r\n**描述:** 实现破解导向的控制流分析功能，包括验证点识别、跳转条件分析、函数调用链追踪、返回值篡改点定位。采用压缩实现策略，单模块代码量控制在1KB以内，提供完整的破解分析能力。\r\n\r\n**注意事項:** 必须使用@lazy_init_module('control_flow')装饰器，确保数据结构延迟加载。算法实现要高效简洁，避免复杂的深度分析\r\n\r\n**實現指南:** 添加四个核心@jsonrpc函数：identify_verification_points(识别关键验证逻辑), analyze_jump_conditions(分析条件跳转), trace_func...\r\n\r\n**驗證標準:** 四个控制流分析函数正确实现并注册，能够识别基本的验证点和跳转逻辑，返回结构化的分析结果，模块代码量<1KB\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 4：加密算法识别模块实现\r\n\r\n**ID:** `a03c26c8-79f9-4090-837f-b681b7f37b54`\r\n**描述:** 实现加密算法自动识别功能，支持AES、DES、RSA等常见算法的特征检测。包括S-box模式识别、轮常数检测、密钥定位功能。使用压缩的特征库和高效的模式匹配算法。\r\n\r\n**注意事項:** 特征库要精简但覆盖主要算法，搜索算法要考虑性能，避免全内存扫描。使用延迟初始化加载特征库数据\r\n\r\n**實現指南:** 实现三个核心@jsonrpc函数：identify_crypto_algorithms(算法特征识别), locate_crypto_keys(密钥定位), analyze_crypto_flow(加...\r\n\r\n**驗證標準:** 三个加密分析函数正确实现，能够识别常见加密算法特征，返回置信度和位置信息，模块代码量<1KB\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 5：反调试检测模块实现\r\n\r\n**ID:** `ecd71817-b43d-4dbf-b091-94738dde81de`\r\n**描述:** 实现反调试技术的自动检测和绕过建议功能。包括API调用检测、时间检查识别、异常处理分析、自修改代码发现等反调试对抗技术。\r\n\r\n**注意事項:** 检测逻辑要准确但不能误报，绕过建议要实用且安全。应用补丁功能要谨慎，提供详细的风险提示\r\n\r\n**實現指南:** 实现三个核心@jsonrpc函数：detect_anti_debug_techniques(检测反调试技术), generate_bypass_strategies(生成绕过策略), apply_an...\r\n\r\n**驗證標準:** 三个反调试分析函数正确实现，能够检测常见反调试技术，提供有效的绕过策略，补丁功能安全可控，模块代码量<1KB\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 6：许可证验证分析模块实现\r\n\r\n**ID:** `017c439c-8971-4e3b-b855-5c64942cf3b9`\r\n**描述:** 实现软件许可证验证逻辑的自动分析功能，包括许可证文件识别、验证算法分析、序列号格式检测、时间限制识别等破解导向的许可证分析工具。\r\n\r\n**注意事項:** 分析要深入但保持教育研究目的，提供的信息要有助于理解验证机制而非直接破解\r\n\r\n**實現指南:** 实现四个核心@jsonrpc函数：analyze_license_validation(分析许可证验证逻辑), trace_serial_validation(追踪序列号验证), detect_tim...\r\n\r\n**驗證標準:** 四个许可证分析函数正确实现，能够识别许可证验证逻辑，分析序列号格式，检测时间限制机制，模块代码量<1KB\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 7：内存补丁与代码修改模块实现\r\n\r\n**ID:** `744fe087-847d-4542-b5c4-66a250ef2d83`\r\n**描述:** 实现运行时内存补丁和代码修改功能，包括指令修改、返回值篡改、函数Hook、动态补丁应用等高级破解技术支持。提供安全的补丁管理和回滚机制。\r\n\r\n**注意事項:** 补丁功能要谨慎实现，提供完整的撤销机制。所有修改操作要有详细的日志记录，确保用户了解风险\r\n\r\n**實現指南:** 实现五个核心@jsonrpc函数：apply_memory_patch(应用内存补丁), modify_instruction(修改指令), hook_function_calls(Hook函数调用)...\r\n\r\n**驗證標準:** 五个内存补丁函数正确实现，补丁应用安全可控，提供完整的回滚机制，操作日志详细准确，模块代码量<1.2KB\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 8：智能缓存系统实现\r\n\r\n**ID:** `7b334c77-53fc-4d2c-b9db-965d603729ea`\r\n**描述:** 实现基于LRU算法的智能缓存系统，用于存储分析结果、模式匹配数据、函数信息等。包括缓存策略配置、自动清理机制、缓存命中率统计功能。\r\n\r\n**注意事項:** 缓存系统要轻量级，不能显著增加内存占用。缓存策略要智能，避免缓存无用数据\r\n\r\n**實現指南:** 实现AnalysisCache类，基于LRU算法管理缓存数据。提供缓存装饰器@cached_analysis，自动缓存函数结果。实现缓存清理策略，基于内存使用阈值和时间策略。添加缓存统计功能，追踪命中...\r\n\r\n**驗證標準:** AnalysisCache类正确实现，缓存装饰器功能正常，缓存清理机制有效，缓存统计准确，集成到分析模块无冲突\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`)\r\n\r\n### 任務 9：字符串分析增强模块实现\r\n\r\n**ID:** `c3749858-fbef-4d7b-8555-797e0febf1b0`\r\n**描述:** 扩展现有的字符串分析功能，添加加密字符串解密、许可证信息识别、错误消息分析、资源字符串提取等破解导向的字符串分析能力。\r\n\r\n**注意事項:** 解密功能要支持常见编码但不要过于复杂，字符串分类要准确实用，避免误报\r\n\r\n**實現指南:** 基于现有的list_strings函数扩展功能。实现四个核心@jsonrpc函数：decrypt_encoded_strings(解密编码字符串), extract_license_strings(提...\r\n\r\n**驗證標準:** 四个字符串分析函数正确实现，能够识别和解密常见编码，准确分类字符串类型，与现有字符串功能无冲突\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`), \"智能缓存系统实现\" (`7b334c77-53fc-4d2c-b9db-965d603729ea`)\r\n\r\n### 任務 10：自动化工作流引擎实现\r\n\r\n**ID:** `beb2a0c1-152b-4b67-bb7f-e0c69e8f9c27`\r\n**描述:** 实现智能的破解分析工作流引擎，能够自动识别程序保护类型、生成分析策略、执行批量分析任务、生成破解分析报告等高级自动化功能。\r\n\r\n**注意事項:** 工作流引擎要智能但不过于复杂，策略选择要基于实际检测结果，批量处理要考虑性能\r\n\r\n**實現指南:** 实现WorkflowEngine类，管理分析工作流程。实现四个核心@jsonrpc函数：detect_protection_type(检测保护类型), generate_analysis_strate...\r\n\r\n**驗證標準:** WorkflowEngine类正确实现，能够自动检测保护类型，生成合理的分析策略，批量分析功能正常，报告生成准确详细\r\n\r\n**依賴任務:** \"控制流分析模块实现\" (`c18df76f-9996-440f-baf3-37936ba229af`), \"加密算法识别模块实现\" (`a03c26c8-79f9-4090-837f-b681b7f37b54`), \"反调试检测模块实现\" (`ecd71817-b43d-4dbf-b091-94738dde81de`)\r\n\r\n### 任務 11：性能优化与最终整合测试\r\n\r\n**ID:** `d8115dec-3fdd-49e8-acb6-f73dd4a373fa`\r\n**描述:** 进行全面的性能优化和功能集成测试，确保所有模块协同工作，验证文件大小目标达成，测试启动性能和内存占用，确保API兼容性和功能完整性。\r\n\r\n**注意事項:** 测试要全面覆盖所有功能模块，性能测试要在真实IDA环境中进行，确保所有优化目标达成\r\n\r\n**實現指南:** 执行全面的集成测试：1.验证所有@jsonrpc函数正常注册和调用 2.测试延迟初始化机制性能影响 3.验证缓存系统效果和内存管理 4.测试所有破解分析模块功能 5.进行压力测试和边界条件测试 6.验...\r\n\r\n**驗證標準:** 所有模块功能正常，文件大小<60KB，启动时间<2秒，内存占用减少40%，API完全兼容，无语法或逻辑错误，所有测试用例通过\r\n\r\n**依賴任務:** \"延迟初始化框架实现\" (`37c67873-a282-4145-9a30-54f68f415c14`), \"代码压缩与结构优化\" (`58d041a6-a3c6-47c8-ba6b-9a303a261d55`), \"控制流分析模块实现\" (`c18df76f-9996-440f-baf3-37936ba229af`), \"加密算法识别模块实现\" (`a03c26c8-79f9-4090-837f-b681b7f37b54`), \"反调试检测模块实现\" (`ecd71817-b43d-4dbf-b091-94738dde81de`), \"许可证验证分析模块实现\" (`017c439c-8971-4e3b-b855-5c64942cf3b9`), \"内存补丁与代码修改模块实现\" (`744fe087-847d-4542-b5c4-66a250ef2d83`), \"智能缓存系统实现\" (`7b334c77-53fc-4d2c-b9db-965d603729ea`), \"字符串分析增强模块实现\" (`c3749858-fbef-4d7b-8555-797e0febf1b0`), \"自动化工作流引擎实现\" (`beb2a0c1-152b-4b67-bb7f-e0c69e8f9c27`)\r\n\r\n\r\n## 依賴關係管理\r\n\r\n- 設置依賴可使用任務名稱或任務 ID\r\n- 最小化依賴數量，只設置直接前置任務\r\n- 避免循環依賴，確保任務圖有向無環\r\n- 平衡關鍵路徑，優化並行執行可能性\r\n\r\n## 決策點\r\n\r\n- 發現任務拆分不合理：重新呼叫「split_tasks」調整\r\n- 確認任務拆分完善：生成執行計劃，確定優先順序\r\n\r\n**嚴重警告** 你每次呼叫 split_tasks 傳遞的參數不能超過 5000 個字，如果超出 5000 個字請多次呼叫工具完成\r\n\r\n**如果還有剩餘任務請繼續呼叫「split_tasks」**\r\n"}]}