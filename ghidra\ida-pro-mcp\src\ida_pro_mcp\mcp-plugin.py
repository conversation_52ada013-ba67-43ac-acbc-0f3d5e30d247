#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDA Pro MCP Plugin - 逆向工程自动化插件
支持83个JSON-RPC功能的高级逆向分析工具
"""

import os
import sys

if sys.version_info < (3, 11):
    raise RuntimeError("Python 3.11 or higher is required for the MCP plugin")

import json
import struct
import threading
import functools
import http.server
import time
import hashlib
import weakref
import base64
import re
from collections import OrderedDict
from urllib.parse import urlparse
from typing import Any, Callable, get_type_hints, TypedDict, Optional, Annotated, TypeVar, Generic, NotRequired


class JSONRPCError(Exception):
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data

class RPCRegistry:
    def __init__(self):
        self.methods: dict[str, Callable] = {}
        self.unsafe: set[str] = set()

    def register(self, func: Callable) -> Callable:
        self.methods[func.__name__] = func
        return func

    def mark_unsafe(self, func: Callable) -> Callable:
        self.unsafe.add(func.__name__)
        return func

    def dispatch(self, method: str, params: Any) -> Any:
        if method not in self.methods:
            raise JSONRPCError(-32601, f"Method '{method}' not found")

        func = self.methods[method]
        hints = get_type_hints(func)

        # Remove return annotation if present
        hints.pop("return", None)

        if isinstance(params, list):
            if len(params) != len(hints):
                raise JSONRPCError(-32602, f"Invalid params: expected {len(hints)} arguments, got {len(params)}")

            # Validate and convert parameters
            converted_params = []
            for value, (param_name, expected_type) in zip(params, hints.items()):
                try:
                    if not isinstance(value, expected_type):
                        value = expected_type(value)
                    converted_params.append(value)
                except (ValueError, TypeError):
                    raise JSONRPCError(-32602, f"Invalid type for parameter '{param_name}': expected {expected_type.__name__}")

            return func(*converted_params)
        elif isinstance(params, dict):
            if set(params.keys()) != set(hints.keys()):
                raise JSONRPCError(-32602, f"Invalid params: expected {list(hints.keys())}")

            # Validate and convert parameters
            converted_params = {}
            for param_name, expected_type in hints.items():
                value = params.get(param_name)
                try:
                    if not isinstance(value, expected_type):
                        value = expected_type(value)
                    converted_params[param_name] = value
                except (ValueError, TypeError):
                    raise JSONRPCError(-32602, f"Invalid type for parameter '{param_name}': expected {expected_type.__name__}")

            return func(**converted_params)
        else:
            raise JSONRPCError(-32600, "Invalid Request: params must be array or object")

rpc_registry = RPCRegistry()

class LazyModuleManager:
    """延迟初始化模块管理器 - 零配置模块延迟加载系统"""
    def __init__(self):
        self.module_states: dict[str, bool] = {}
        self.module_data: dict[str, Any] = {}
        self.usage_stats: dict[str, int] = {}
        self.initialization_lock = threading.Lock()
    
    def is_initialized(self, module_name: str) -> bool:
        """检查模块是否已初始化"""
        return self.module_states.get(module_name, False)
    
    def _init_module_data(self, module_name: str) -> None:
        """延迟加载模块数据"""
        if module_name == 'control_flow':
            self.module_data[module_name] = {
                'verification_patterns': [b'\x85\xc0', b'\x84\xc0', b'\x83\xf8', b'\x3b\xc0'],
                'jump_opcodes': [b'\x74', b'\x75', b'\x73', b'\x72', b'\xe9', b'\xeb']
            }
        elif module_name == 'crypto':
            self.module_data[module_name] = {
                'aes_sbox': bytes.fromhex('********************************'),
                'des_sbox': bytes.fromhex('********************************'),
                'rsa_patterns': [b'RSA', b'PKCS', b'\x30\x82']
            }
        elif module_name == 'anti_debug':
            self.module_data[module_name] = {
                'api_signatures': [
                    'IsDebuggerPresent', 'CheckRemoteDebuggerPresent', 'NtQueryInformationProcess',
                    'OutputDebugString', 'GetTickCount', 'QueryPerformanceCounter', 
                    'ZwQueryInformationProcess', 'NtSetInformationThread', 'NtQueryObject',
                    'NtQuerySystemInformation', 'CreateToolhelp32Snapshot', 'Process32First',
                    'Process32Next', 'FindWindow', 'SetUnhandledExceptionFilter'
                ],
                'time_check_patterns': [
                    b'\x0F\x31',  # rdtsc
                    b'\xFF\x15',  # call GetTickCount
                    b'\x0F\xA2',  # cpuid  
                    b'\x64\xA1\x18\x00\x00\x00'  # mov eax, fs:[18h] - PEB access
                ],
                'debug_detection_opcodes': [0xCC, 0xCD, 0xF1, 0xCE],  # int3, int imm8, int1, into
                'bypass_patterns': {
                    'nop_replace': b'\x90',
                    'ret_immediate': b'\xC3',
                    'xor_eax_ret': b'\x31\xC0\xC3',
                    'jmp_short': b'\xEB\x02'
                }
            }
        elif module_name == 'license':
            self.module_data[module_name] = {
                'license_keywords': ['LICENSE','SERIAL','REGISTER','ACTIVATION','TRIAL','EXPIRED','VALID','INVALID','KEY','CODE','PRODUCT_ID','USER_NAME','COMPANY','EVALUATION','DEMO','FULL_VERSION'],
                'validation_patterns': [b'\x83\xF8\x01\x74',b'\x85\xC0\x75',b'\x3D\x00\x00\x00\x00\x74',b'\x81\xFE'],
                'serial_patterns': [b'\x83\xF9\x10',b'\x83\xF9\x14',b'\x3C\x2D',b'\x80\xF9\x2D'],
                'time_apis': ['GetSystemTime','GetLocalTime','GetFileTime','GetTickCount','QueryPerformanceCounter','time','mktime','_time64'],
                'time_check_patterns': [b'\x8B\x45\xF8\x3B\x45\xFC',b'\x39\x4D\xFC'],
                'trial_patterns': [b'\xFF\x4D\xFC',b'\x83\x6D\xFC\x01',b'\x8B\x45\xFC\x85\xC0'],
                'algorithm_signatures': {b'\x69\xC0\x6D\x4E\xC6\x41':'linear_congruential',b'\x8B\xC8\xC1\xE1\x05':'simple_hash',b'\x33\xC1\x8B\xC8':'xor_cipher',b'\x0F\xAF\xC1':'multiplication_check'}
            }
        elif module_name == 'memory_patch':
            self.module_data[module_name] = {
                'patch_history': [],
                'safe_instructions': {'nop':b'\x90','ret':b'\xC3','int3':b'\xCC','push_eax':b'\x50','pop_eax':b'\x58'},
                'hook_templates': {'entry_redirect':b'\xE9\x00\x00\x00\x00','call_replacement':b'\xE8\x00\x00\x00\x00','short_jump':b'\xEB\x00','infinite_loop':b'\xEB\xFE'},
                'return_value_patches': {'mov_eax_0':b'\xB8\x00\x00\x00\x00','mov_eax_1':b'\xB8\x01\x00\x00\x00','xor_eax':b'\x31\xC0','or_eax_1':b'\x83\xC8\x01'},
                'risk_levels': ['low','medium','high','critical']
            }
        elif module_name == 'string_analysis':
            self.module_data[module_name] = {
                'encoding_patterns': {'base64':'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=','hex':'0123456789ABCDEFabcdef','base32':'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567='},
                'license_keywords': ['license','serial','key','activation','registration','trial','expired','invalid','valid','product','version','user','company','evaluation','demo','full','lite','professional','enterprise','standard'],
                'error_patterns': ['error','exception','fail','invalid','incorrect','wrong','bad','corrupt','missing','not found','access denied','permission','unable','cannot','could not','timeout','overflow','underflow'],
                'resource_indicators': ['menu','dialog','button','label','title','message','text','string','resource','icon','bitmap','cursor','accelerator','version','manifest','caption','tooltip','status'],
                'common_xor_keys': [0x01,0x02,0x04,0x08,0x10,0x20,0x40,0x80,0xFF,0xAA,0x55,0xCC,0x33],
                'string_categories': {'high_importance':['password','key','secret','token','hash','encrypted'],'medium_importance':['config','setting','option','parameter','path','file'],'low_importance':['debug','info','log','trace','verbose','temp']}
            }
    
    def initialize_module(self, module_name: str) -> None:
        """线程安全的模块初始化"""
        if self.is_initialized(module_name):
            return
        
        with self.initialization_lock:
            if self.is_initialized(module_name):
                return
            
            self._init_module_data(module_name)
            self.module_states[module_name] = True
            self.usage_stats[module_name] = 0
    
    def get_module_data(self, module_name: str) -> dict[str, Any]:
        """获取模块数据，触发延迟初始化"""
        self.initialize_module(module_name)
        self.usage_stats[module_name] = self.usage_stats.get(module_name, 0) + 1
        return self.module_data.get(module_name, {})
    
    def get_usage_stats(self) -> dict[str, int]:
        """获取模块使用统计"""
        return self.usage_stats.copy()

lazy_module_manager = LazyModuleManager()

class CacheEntry(TypedDict):
    """缓存条目类型定义"""
    data: Any
    timestamp: float
    access_count: int
    size: int

class AnalysisCache:
    """基于LRU算法的智能缓存系统 - 轻量级分析结果缓存"""
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 10, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.current_memory = 0
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_evictions': 0
        }
        self.lock = threading.RLock()
    
    def _calculate_size(self, data: Any) -> int:
        """估算数据大小（字节）"""
        try:
            if isinstance(data, (str, bytes)):
                return len(data)
            elif isinstance(data, (list, tuple)):
                return sum(self._calculate_size(item) for item in data)
            elif isinstance(data, dict):
                return sum(self._calculate_size(k) + self._calculate_size(v) for k, v in data.items())
            else:
                return sys.getsizeof(data)
        except (TypeError, RecursionError):
            return 100  # 默认估算值
    
    def _make_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        try:
            key_data = f"{func_name}:{str(args)}:{str(sorted(kwargs.items()))}"
            return hashlib.md5(key_data.encode('utf-8')).hexdigest()[:16]
        except (TypeError, UnicodeEncodeError):
            return f"{func_name}:{id(args)}:{id(kwargs)}"
    
    def _cleanup_expired(self) -> None:
        """清理过期条目"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time - entry['timestamp'] > self.ttl_seconds
        ]
        for key in expired_keys:
            self._remove_entry(key)
    
    def _remove_entry(self, key: str) -> None:
        """移除缓存条目并更新内存统计"""
        if key in self.cache:
            entry = self.cache.pop(key)
            self.current_memory -= entry['size']
    
    def _enforce_limits(self) -> None:
        """强制执行缓存大小和内存限制"""
        # 内存限制
        while self.current_memory > self.max_memory_bytes and self.cache:
            key = next(iter(self.cache))
            self._remove_entry(key)
            self.stats['memory_evictions'] += 1
        
        # 大小限制（LRU）
        while len(self.cache) > self.max_size:
            key = next(iter(self.cache))
            self._remove_entry(key)
            self.stats['evictions'] += 1
    
    def get(self, func_name: str, args: tuple, kwargs: dict) -> tuple[bool, Any]:
        """获取缓存数据"""
        with self.lock:
            key = self._make_key(func_name, args, kwargs)
            
            if key not in self.cache:
                self.stats['misses'] += 1
                return False, None
            
            # 检查过期
            entry = self.cache[key]
            if time.time() - entry['timestamp'] > self.ttl_seconds:
                self._remove_entry(key)
                self.stats['misses'] += 1
                return False, None
            
            # 移动到末尾（LRU更新）
            self.cache.move_to_end(key)
            entry['access_count'] += 1
            self.stats['hits'] += 1
            return True, entry['data']
    
    def put(self, func_name: str, args: tuple, kwargs: dict, data: Any) -> None:
        """存储缓存数据"""
        with self.lock:
            key = self._make_key(func_name, args, kwargs)
            data_size = self._calculate_size(data)
            
            # 如果单个数据太大，不缓存
            if data_size > self.max_memory_bytes // 4:
                return
            
            # 移除旧条目（如果存在）
            if key in self.cache:
                self._remove_entry(key)
            
            # 创建新条目
            entry: CacheEntry = {
                'data': data,
                'timestamp': time.time(),
                'access_count': 1,
                'size': data_size
            }
            
            self.cache[key] = entry
            self.current_memory += data_size
            
            # 清理过期条目
            self._cleanup_expired()
            
            # 强制执行限制
            self._enforce_limits()
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.current_memory = 0
    
    def get_stats(self) -> dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
            
            return {
                'hit_rate': f"{hit_rate:.2%}",
                'total_entries': len(self.cache),
                'memory_usage_mb': f"{self.current_memory / 1024 / 1024:.2f}",
                'stats': self.stats.copy()
            }

analysis_cache = AnalysisCache()

def cached_analysis(cache_ttl: int = 3600, cache_size_limit: int = 100):
    """缓存装饰器 - 自动缓存分析函数结果"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取缓存
            hit, cached_result = analysis_cache.get(func.__name__, args, kwargs)
            if hit:
                return cached_result
            
            # 执行函数
            result = func(*args, **kwargs)
            
            # 存储到缓存
            analysis_cache.put(func.__name__, args, kwargs, result)
            
            return result
        
        setattr(wrapper, '_cached', True)
        setattr(wrapper, '_cache_ttl', cache_ttl)
        return wrapper
    return decorator

def lazy_init_module(module_name: str):
    """延迟初始化模块装饰器 - 集成到现有装饰器链"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 确保模块已初始化（延迟初始化）
            lazy_module_manager.initialize_module(module_name)
            return func(*args, **kwargs)
        setattr(wrapper, '_lazy_module', module_name)
        return wrapper
    return decorator

def jsonrpc(func: Callable) -> Callable:
    """Decorator to register a function as a JSON-RPC method"""
    global rpc_registry
    return rpc_registry.register(func)

def unsafe(func: Callable) -> Callable:
    """Decorator to register mark a function as unsafe"""
    return rpc_registry.mark_unsafe(func)

class JSONRPCRequestHandler(http.server.BaseHTTPRequestHandler):
    def send_jsonrpc_error(self, code: int, message: str, id: Any = None):
        response = {
            "jsonrpc": "2.0",
            "error": {
                "code": code,
                "message": message
            }
        }
        if id is not None:
            response["id"] = id
        response_body = json.dumps(response).encode("utf-8")
        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        self.send_header("Content-Length", str(len(response_body)))
        self.end_headers()
        self.wfile.write(response_body)

    def do_POST(self):
        global rpc_registry

        parsed_path = urlparse(self.path)
        if parsed_path.path != "/mcp":
            self.send_jsonrpc_error(-32098, "Invalid endpoint", None)
            return

        content_length = int(self.headers.get("Content-Length", 0))
        if content_length == 0:
            self.send_jsonrpc_error(-32700, "Parse error: missing request body", None)
            return

        request_body = self.rfile.read(content_length)
        try:
            request = json.loads(request_body)
        except json.JSONDecodeError:
            self.send_jsonrpc_error(-32700, "Parse error: invalid JSON", None)
            return

        # Prepare the response
        response = {
            "jsonrpc": "2.0"
        }
        if request.get("id") is not None:
            response["id"] = request.get("id")

        try:
            # Basic JSON-RPC validation
            if not isinstance(request, dict):
                raise JSONRPCError(-32600, "Invalid Request")
            if request.get("jsonrpc") != "2.0":
                raise JSONRPCError(-32600, "Invalid JSON-RPC version")
            if "method" not in request:
                raise JSONRPCError(-32600, "Method not specified")

            # Dispatch the method
            result = rpc_registry.dispatch(request["method"], request.get("params", []))
            response["result"] = result

        except JSONRPCError as e:
            response["error"] = {
                "code": e.code,
                "message": e.message
            }
            if e.data is not None:
                response["error"]["data"] = e.data
        except IDAError as e:
            response["error"] = {
                "code": -32000,
                "message": e.message,
            }
        except Exception as e:
            traceback.print_exc()
            response["error"] = {
                "code": -32603,
                "message": "Internal error (please report a bug)",
                "data": traceback.format_exc(),
            }

        try:
            response_body = json.dumps(response).encode("utf-8")
        except Exception as e:
            traceback.print_exc()
            response_body = json.dumps({
                "error": {
                    "code": -32603,
                    "message": "Internal error (please report a bug)",
                    "data": traceback.format_exc(),
                }
            }).encode("utf-8")

        self.send_response(200)
        self.send_header("Content-Type", "application/json")
        self.send_header("Content-Length", str(len(response_body)))
        self.end_headers()
        self.wfile.write(response_body)

    def log_message(self, format, *args):
        # Suppress logging - 禁用HTTP服务器日志输出以避免控制台干扰
        return

class MCPHTTPServer(http.server.HTTPServer):
    allow_reuse_address = False

class Server:
    HOST = "localhost"
    PORT = 13337

    def __init__(self):
        self.server = None
        self.server_thread = None
        self.running = False

    def start(self):
        if self.running:
            print("[MCP] Server is already running")
            return

        self.server_thread = threading.Thread(target=self._run_server, daemon=True)
        self.running = True
        self.server_thread.start()

    def stop(self):
        if not self.running:
            return

        self.running = False
        if self.server:
            self.server.shutdown()
            self.server.server_close()
        if self.server_thread:
            self.server_thread.join()
            self.server = None
        print("[MCP] Server stopped")

    def _run_server(self):
        try:
            # Create server in the thread to handle binding
            self.server = MCPHTTPServer((Server.HOST, Server.PORT), JSONRPCRequestHandler)
            print(f"[MCP] Server started at http://{Server.HOST}:{Server.PORT}")
            self.server.serve_forever()
        except OSError as e:
            if e.errno == 98 or e.errno == 10048:  # Port already in use (Linux/Windows)
                print("[MCP] Error: Port 13337 is already in use")
            else:
                print(f"[MCP] Server error: {e}")
            self.running = False
        except Exception as e:
            print(f"[MCP] Server error: {e}")
        finally:
            self.running = False

import logging, queue, traceback, functools
import ida_hexrays, ida_kernwin, ida_funcs, ida_gdl, ida_lines, ida_idaapi, ida_nalt, ida_bytes, ida_typeinf, ida_xref, ida_entry, ida_idd, ida_dbg, ida_name, ida_ida, ida_frame, ida_allins, ida_ua
import idc, idaapi, idautils

def safe_find_bytes(start_ea: int, end_ea: int, pattern: bytes) -> int:
    """安全的字节搜索，避免类型转换错误"""
    try:
        # 使用ida_search模块进行字节搜索
        import ida_search
        # 将bytes转换为十六进制字符串格式，适用于ida_search.find_binary
        hex_pattern = ' '.join(f'{b:02X}' for b in pattern)
        result = ida_search.find_binary(start_ea, end_ea, hex_pattern, 0, idaapi.SEARCH_DOWN)
        return result if result != idaapi.BADADDR else idaapi.BADADDR
    except Exception:
        return idaapi.BADADDR

class IDAError(Exception):
    def __init__(self, message: str):
        super().__init__(message)

    @property
    def message(self) -> str:
        return self.args[0]

class IDASyncError(Exception):
    """IDA同步操作异常"""
    def __init__(self, message: str):
        super().__init__(message)
        self.message = message

class DecompilerLicenseError(IDAError):
    """反编译器许可证异常"""
    def __init__(self, message: str):
        super().__init__(message)

logger = logging.getLogger(__name__)

class IDASafety:
    ida_kernwin.MFF_READ
    SAFE_NONE = ida_kernwin.MFF_FAST
    SAFE_READ = ida_kernwin.MFF_READ
    SAFE_WRITE = ida_kernwin.MFF_WRITE

call_stack: queue.LifoQueue = queue.LifoQueue()

def sync_wrapper(ff, safety_mode: IDASafety):
    """Call a function ff with a specific IDA safety_mode."""
    if safety_mode not in [IDASafety.SAFE_READ, IDASafety.SAFE_WRITE]:
        error_str = f'Invalid safety mode {safety_mode} over function {ff.__name__}'
        logger.error(error_str)
        raise IDASyncError(error_str)
    res_container: queue.Queue = queue.Queue()
    def runned():
        if not call_stack.empty():
            last_func_name = call_stack.get()
            error_str = f'Call stack is not empty while calling the function {ff.__name__} from {last_func_name}'
            raise IDASyncError(error_str)
        call_stack.put((ff.__name__))
        try:
            res_container.put(ff())
        except Exception as x:
            res_container.put(x)
        finally:
            call_stack.get()
    ret_val = idaapi.execute_sync(runned, safety_mode)
    res = res_container.get()
    if isinstance(res, Exception):
        raise res
    return res

def idawrite(f):
    """Decorator for marking a function as modifying the IDB."""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        ff = functools.partial(f, *args, **kwargs)
        ff.__name__ = f.__name__
        return sync_wrapper(ff, idaapi.MFF_WRITE)
    return wrapper

def idaread(f):
    """Decorator for marking a function as reading from the IDB."""
    @functools.wraps(f)
    def wrapper(*args, **kwargs):
        ff = functools.partial(f, *args, **kwargs)
        ff.__name__ = f.__name__
        return sync_wrapper(ff, idaapi.MFF_READ)
    return wrapper

def is_window_active():
    """Returns whether IDA is currently active"""
    try:
        from PyQt5.QtWidgets import QApplication
    except ImportError:
        return False

    app = QApplication.instance()
    if app is None:
        return False

    for widget in app.topLevelWidgets():
        if widget.isActiveWindow():
            return True
    return False

class Metadata(TypedDict):
    path: str
    module: str
    base: str
    size: str
    md5: str
    sha256: str
    crc32: str
    filesize: str

def get_image_size() -> int:
    try:
        info = idaapi.get_inf_structure()
        omin_ea = info.omin_ea
        omax_ea = info.omax_ea
    except AttributeError:
        import ida_ida
        omin_ea = ida_ida.inf_get_omin_ea()
        omax_ea = ida_ida.inf_get_omax_ea()
    image_size = omax_ea - omin_ea
    header = idautils.peutils_t().header()
    if header and header[:4] == b"PE\0\0":
        image_size = struct.unpack("<I", header[0x50:0x54])[0]
    return image_size

@jsonrpc
@idaread
def get_metadata() -> Metadata:
    """Get metadata about the current IDB - 版本：2025-01-04-增强版"""
    def hash(f):
        try:
            return f().hex()
        except:
            return None

    return Metadata(path=idaapi.get_input_file_path(),
                    module=idaapi.get_root_filename(),
                    base=hex(idaapi.get_imagebase()),
                    size=hex(get_image_size()),
                    md5=str(hash(ida_nalt.retrieve_input_file_md5() or b"")),
                    sha256=str(hash(ida_nalt.retrieve_input_file_sha256() or b"")),
                    crc32=hex(ida_nalt.retrieve_input_file_crc32()),
                    filesize=hex(ida_nalt.retrieve_input_file_size()))

@jsonrpc
@idaread
def test_enhanced_version() -> dict[str, Any]:
    """测试增强版本是否加载"""
    return {
        "status": "success",
        "version": "2025-01-04-enhanced",
        "message": "增强版本已成功加载！",
        "new_features_count": 20
    }

def get_prototype(fn: ida_funcs.func_t) -> Optional[str]:
    try:
        prototype: ida_typeinf.tinfo_t = fn.get_prototype()
        if prototype is not None:
            return str(prototype)
        else:
            return None
    except AttributeError:
        try:
            return idc.get_type(fn.start_ea)
        except:
            tif = ida_typeinf.tinfo_t()
            if ida_nalt.get_tinfo(tif, fn.start_ea):
                return str(tif)
            return None
    except Exception as e:
        print(f"Error getting function prototype: {e}")
        return None

class Function(TypedDict):
    address: str
    name: str
    size: str

def parse_address(address: str) -> int:
    try:
        return int(address, 0)
    except ValueError:
        for ch in address:
            if ch not in "0123456789abcdefABCDEF":
                raise IDAError(f"Failed to parse address: {address}")
        raise IDAError(f"Failed to parse address (missing 0x prefix): {address}")

def get_function(address: int, *, raise_error=True) -> Optional[Function]:
    fn = idaapi.get_func(address)
    if fn is None:
        if raise_error:
            raise IDAError(f"No function found at address {hex(address)}")
        return None

    try:
        name = fn.get_name()
    except AttributeError:
        name = ida_funcs.get_func_name(fn.start_ea)

    return Function(address=hex(address), name=name, size=hex(fn.end_ea - fn.start_ea))

DEMANGLED_TO_EA = {}

def create_demangled_to_ea_map():
    for ea in idautils.Functions():
        # Get the function name and demangle it
        # MNG_NODEFINIT inhibits everything except the main name
        # where default demangling adds the function signature
        # and decorators (if any)
        demangled = idaapi.demangle_name(
            idc.get_name(ea, 0), idaapi.MNG_NODEFINIT)
        if demangled:
            DEMANGLED_TO_EA[demangled] = ea


def get_type_by_name(type_name: str) -> ida_typeinf.tinfo_t:
    # 8-bit integers
    if type_name in ('int8', '__int8', 'int8_t', 'char', 'signed char'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT8)
    elif type_name in ('uint8', '__uint8', 'uint8_t', 'unsigned char', 'byte', 'BYTE'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT8)

    # 16-bit integers
    elif type_name in ('int16', '__int16', 'int16_t', 'short', 'short int', 'signed short', 'signed short int'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT16)
    elif type_name in ('uint16', '__uint16', 'uint16_t', 'unsigned short', 'unsigned short int', 'word', 'WORD'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT16)

    # 32-bit integers
    elif type_name in ('int32', '__int32', 'int32_t', 'int', 'signed int', 'long', 'long int', 'signed long', 'signed long int'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT32)
    elif type_name in ('uint32', '__uint32', 'uint32_t', 'unsigned int', 'unsigned long', 'unsigned long int', 'dword', 'DWORD'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT32)

    # 64-bit integers
    elif type_name in ('int64', '__int64', 'int64_t', 'long long', 'long long int', 'signed long long', 'signed long long int'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT64)
    elif type_name in ('uint64', '__uint64', 'uint64_t', 'unsigned int64', 'unsigned long long', 'unsigned long long int', 'qword', 'QWORD'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT64)

    # 128-bit integers
    elif type_name in ('int128', '__int128', 'int128_t', '__int128_t'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT128)
    elif type_name in ('uint128', '__uint128', 'uint128_t', '__uint128_t', 'unsigned int128'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT128)

    # Floating point types
    elif type_name in ('float', ):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_FLOAT)
    elif type_name in ('double', ):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_DOUBLE)
    elif type_name in ('long double', 'ldouble'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_LDOUBLE)

    # Boolean type
    elif type_name in ('bool', '_Bool', 'boolean'):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_BOOL)

    # Void type
    elif type_name in ('void', ):
        return ida_typeinf.tinfo_t(ida_typeinf.BTF_VOID)

    # If not a standard type, try to get a named type
    tif = ida_typeinf.tinfo_t()
    if tif.get_named_type(None, type_name, ida_typeinf.BTF_STRUCT):
        return tif

    if tif.get_named_type(None, type_name, ida_typeinf.BTF_TYPEDEF):
        return tif

    if tif.get_named_type(None, type_name, ida_typeinf.BTF_ENUM):
        return tif

    if tif.get_named_type(None, type_name, ida_typeinf.BTF_UNION):
        return tif

    if tif := ida_typeinf.tinfo_t(type_name):
        return tif

    raise IDAError(f"Unable to retrieve {type_name} type info object")

@jsonrpc
@idaread
def get_function_by_name(
    name: Annotated[str, "Name of the function to get"]
) -> Function:
    """Get a function by its name"""
    function_address = idaapi.get_name_ea(idaapi.BADADDR, name)
    if function_address == idaapi.BADADDR:
        # If map has not been created yet, create it
        if len(DEMANGLED_TO_EA) == 0:
            create_demangled_to_ea_map()
        # Try to find the function in the map, else raise an error
        if name in DEMANGLED_TO_EA:
            function_address = DEMANGLED_TO_EA[name]
        else:
            raise IDAError(f"No function found with name {name}")
    
    result = get_function(function_address)
    if result is None:
        raise IDAError(f"No function found at address {hex(function_address)}")
    return result

@jsonrpc
@idaread
def get_function_by_address(
    address: Annotated[str, "Address of the function to get"],
) -> Function:
    """Get a function by its address"""
    result = get_function(parse_address(address))
    if result is None:
        raise IDAError(f"No function found at address {address}")
    return result

@jsonrpc
@idaread
def get_current_address() -> str:
    """Get the address currently selected by the user"""
    return hex(idaapi.get_screen_ea())

@jsonrpc
@idaread
def get_current_function() -> Optional[Function]:
    """Get the function currently selected by the user"""
    return get_function(idaapi.get_screen_ea())

class ConvertedNumber(TypedDict):
    decimal: str
    hexadecimal: str
    bytes: str
    ascii: Optional[str]
    binary: str

@jsonrpc
def convert_number(
    text: Annotated[str, "Textual representation of the number to convert"],
    size: Annotated[Optional[int], "Size of the variable in bytes"],
) -> ConvertedNumber:
    """Convert a number (decimal, hexadecimal) to different representations"""
    try:
        value = int(text, 0)
    except ValueError:
        raise IDAError(f"Invalid number: {text}")

    # Estimate the size of the number
    if not size:
        size = 0
        n = abs(value)
        while n:
            size += 1
            n >>= 1
        size += 7
        size //= 8

    # Convert the number to bytes
    try:
        bytes = value.to_bytes(size, "little", signed=True)
    except OverflowError:
        raise IDAError(f"Number {text} is too big for {size} bytes")

    # Convert the bytes to ASCII
    ascii = ""
    for byte in bytes.rstrip(b"\x00"):
        if byte >= 32 and byte <= 126:
            ascii += chr(byte)
        else:
            ascii = None
            break

    return ConvertedNumber(
        decimal=str(value),
        hexadecimal=hex(value),
        bytes=bytes.hex(" "),
        ascii=ascii,
        binary=bin(value),
    )

T = TypeVar("T")

class Page(TypedDict, Generic[T]):
    data: list[T]
    next_offset: Optional[int]

def paginate(data: list[T], offset: int, count: int) -> Page[T]:
    if count == 0:
        count = len(data)
    next_offset = offset + count
    if next_offset >= len(data):
        next_offset = None
    return {
        "data": data[offset:offset + count],
        "next_offset": next_offset,
    }

def pattern_filter(data: list[T], pattern: str, key: str) -> list[T]:
    if not pattern:
        return data

    # 实现基于字符串包含匹配的过滤功能
    def matches(item: T) -> bool:
        if hasattr(item, key) and isinstance(getattr(item, key), str):
            return pattern.lower() in getattr(item, key).lower()
        elif isinstance(item, dict) and key in item:
            return pattern.lower() in str(item[key]).lower()
        return False
    return list(filter(matches, data))

@jsonrpc
@idaread
def list_functions(
    offset: Annotated[int, "Offset to start listing from (start at 0)"],
    count: Annotated[int, "Number of functions to list (100 is a good default, 0 means remainder)"],
) -> Page[Function]:
    """List all functions in the database (paginated)"""
    functions = [get_function(address) for address in idautils.Functions()]
    return paginate(functions, offset, count)

class Global(TypedDict):
    address: str
    name: str

@jsonrpc
@idaread
def list_globals_filter(
    offset: Annotated[int, "Offset to start listing from (start at 0)"],
    count: Annotated[int, "Number of globals to list (100 is a good default, 0 means remainder)"],
    filter: Annotated[str, "Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax"],
) -> Page[Global]:
    """List matching globals in the database (paginated, filtered)"""
    globals = []
    for addr, name in idautils.Names():
        # Skip functions
        if not idaapi.get_func(addr):
            globals += [Global(address=hex(addr), name=name)]

    globals = pattern_filter(globals, filter, "name")
    return paginate(globals, offset, count)

@jsonrpc
def list_globals(
    offset: Annotated[int, "Offset to start listing from (start at 0)"],
    count: Annotated[int, "Number of globals to list (100 is a good default, 0 means remainder)"],
) -> Page[Global]:
    """List all globals in the database (paginated)"""
    return list_globals_filter(offset, count, "")

class Import(TypedDict):
    address: str
    imported_name: str
    module: str

@jsonrpc
@idaread
def list_imports(
        offset: Annotated[int, "Offset to start listing from (start at 0)"],
        count: Annotated[int, "Number of imports to list (100 is a good default, 0 means remainder)"],
) -> Page[Import]:
    """ List all imported symbols with their name and module (paginated) """
    nimps = ida_nalt.get_import_module_qty()

    rv = []
    for i in range(nimps):
        module_name = ida_nalt.get_import_module_name(i)
        if not module_name:
            module_name = "<unnamed>"

        def imp_cb(ea, symbol_name, ordinal, acc):
            if not symbol_name:
                symbol_name = f"#{ordinal}"

            acc += [Import(address=hex(ea), imported_name=symbol_name, module=module_name)]

            return True

        imp_cb_w_context = lambda ea, symbol_name, ordinal: imp_cb(ea, symbol_name, ordinal, rv)
        ida_nalt.enum_import_names(i, imp_cb_w_context)

    return paginate(rv, offset, count)

class String(TypedDict):
    address: str
    length: int
    string: str

@jsonrpc
@idaread
def list_strings_filter(
    offset: Annotated[int, "Offset to start listing from (start at 0)"],
    count: Annotated[int, "Number of strings to list (100 is a good default, 0 means remainder)"],
    filter: Annotated[str, "Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax"],
) -> Page[String]:
    """List matching strings in the database (paginated, filtered)"""
    strings = []
    for item in idautils.Strings():
        try:
            string = str(item)
            if string:
                strings += [
                    String(address=hex(item.ea), length=item.length, string=string),
                ]
        except:
            continue
    strings = pattern_filter(strings, filter, "string")
    return paginate(strings, offset, count)

@jsonrpc
def list_strings(
    offset: Annotated[int, "Offset to start listing from (start at 0)"],
    count: Annotated[int, "Number of strings to list (100 is a good default, 0 means remainder)"],
) -> Page[String]:
    """List all strings in the database (paginated)"""
    return list_strings_filter(offset, count, "")

@jsonrpc
@idaread
def list_local_types():
    """List all Local types in the database"""
    error = ida_hexrays.hexrays_failure_t()
    locals = []
    idati = ida_typeinf.get_idati()
    type_count = ida_typeinf.get_ordinal_limit(idati)
    for ordinal in range(1, type_count):
        try:
            tif = ida_typeinf.tinfo_t()
            if tif.get_numbered_type(idati, ordinal):
                type_name = tif.get_type_name()
                if not type_name:
                    type_name = f"<Anonymous Type #{ordinal}>"
                locals.append(f"\nType #{ordinal}: {type_name}")
                if tif.is_udt():
                    c_decl_flags = (ida_typeinf.PRTYPE_MULTI | ida_typeinf.PRTYPE_TYPE | ida_typeinf.PRTYPE_SEMI | ida_typeinf.PRTYPE_DEF | ida_typeinf.PRTYPE_METHODS | ida_typeinf.PRTYPE_OFFSETS)
                    c_decl_output = tif._print(None, c_decl_flags)
                    if c_decl_output:
                        locals.append(f"  C declaration:\n{c_decl_output}")
                else:
                    simple_decl = tif._print(None, ida_typeinf.PRTYPE_1LINE | ida_typeinf.PRTYPE_TYPE | ida_typeinf.PRTYPE_SEMI)
                    if simple_decl:
                        locals.append(f"  Simple declaration:\n{simple_decl}")  
            else:
                message = f"\nType #{ordinal}: Failed to retrieve information."
                if error.str:
                    message += f": {error.str}"
                if error.errea != idaapi.BADADDR:
                    message += f"from (address: {hex(error.errea)})"
                raise IDAError(message)
        except:
            continue
    return locals

def decompile_checked(address: int) -> ida_hexrays.cfunc_t:
    if not ida_hexrays.init_hexrays_plugin():
        raise IDAError("Hex-Rays decompiler is not available")
    error = ida_hexrays.hexrays_failure_t()
    cfunc: ida_hexrays.cfunc_t = ida_hexrays.decompile_func(address, error, ida_hexrays.DECOMP_WARNINGS)
    if not cfunc:
        if error.code == ida_hexrays.MERR_LICENSE:
            raise DecompilerLicenseError("Decompiler licence is not available. Use `disassemble_function` to get the assembly code instead.")

        message = f"Decompilation failed at {hex(address)}"
        if error.str:
            message += f": {error.str}"
        if error.errea != idaapi.BADADDR:
            message += f" (address: {hex(error.errea)})"
        raise IDAError(message)
    return cfunc

@jsonrpc
@idaread
def decompile_function(
    address: Annotated[str, "Address of the function to decompile"],
) -> str:
    """Decompile a function at the given address"""
    addr_ea = parse_address(address)
    cfunc = decompile_checked(addr_ea)
    if is_window_active():
        ida_hexrays.open_pseudocode(addr_ea, ida_hexrays.OPF_REUSE)
    sv = cfunc.get_pseudocode()
    pseudocode = ""
    for i, sl in enumerate(sv):
        sl: ida_kernwin.simpleline_t
        item = ida_hexrays.ctree_item_t()
        addr = None if i > 0 else cfunc.entry_ea
        if cfunc.get_line_item(sl.line, 0, False, None, item, None):
            ds = item.dstr().split(": ")
            if len(ds) == 2:
                try:
                    addr = int(ds[0], 16)
                except ValueError:
                    # 地址解析失败，保持为None
                    addr = None
        line = ida_lines.tag_remove(sl.line)
        if len(pseudocode) > 0:
            pseudocode += "\n"
        if not addr:
            pseudocode += f"/* line: {i} */ {line}"
        else:
            pseudocode += f"/* line: {i}, address: {hex(addr)} */ {line}"

    return pseudocode

class DisassemblyLine(TypedDict):
    segment: NotRequired[str]
    address: str
    label: NotRequired[str]
    instruction: str
    comments: NotRequired[list[str]]

class Argument(TypedDict):
    name: str
    type: str

class DisassemblyFunction(TypedDict):
    name: str
    start_ea: str
    return_type: NotRequired[str]
    arguments: NotRequired[list[Argument]]
    stack_frame: list[dict]
    lines: list[DisassemblyLine]

@jsonrpc
@idaread
def disassemble_function(
    start_address: Annotated[str, "Address of the function to disassemble"],
) -> DisassemblyFunction:
    """Get assembly code for a function"""
    start = parse_address(start_address)
    func: ida_funcs.func_t = idaapi.get_func(start)
    if not func:
        raise IDAError(f"No function found containing address {start_address}")
    if is_window_active():
        ida_kernwin.jumpto(start)

    # 获取函数名
    try:
        func_name = func.get_name()
    except AttributeError:
        func_name = ida_funcs.get_func_name(func.start_ea) or f"sub_{func.start_ea:X}"

    lines = []
    for address in ida_funcs.func_item_iterator_t(func):
        seg = idaapi.getseg(address)
        segment = idaapi.get_segm_name(seg) if seg else None

        label = idc.get_name(address, 0)
        if label and label == func_name and address == func.start_ea:
            label = None
        if label == "":
            label = None

        comments = []
        if comment := idaapi.get_cmt(address, False):
            comments += [comment]
        if comment := idaapi.get_cmt(address, True):
            comments += [comment]

        raw_instruction = idaapi.generate_disasm_line(address, 0)
        tls = ida_kernwin.tagged_line_sections_t()
        ida_kernwin.parse_tagged_line_sections(tls, raw_instruction)
        insn_section = tls.first(ida_lines.COLOR_INSN)

        operands = []
        for op_tag in range(ida_lines.COLOR_OPND1, ida_lines.COLOR_OPND8 + 1):
            op_n = tls.first(op_tag)
            if not op_n:
                break

            op: str = op_n.substr(raw_instruction)
            op_str = ida_lines.tag_remove(op)

            # Do a lot of work to add address comments for symbols
            for idx in range(len(op) - 2):
                if op[idx] != idaapi.COLOR_ON:
                    continue

                idx += 1
                if ord(op[idx]) != idaapi.COLOR_ADDR:
                    continue

                idx += 1
                addr_string = op[idx:idx + idaapi.COLOR_ADDR_SIZE]
                idx += idaapi.COLOR_ADDR_SIZE

                addr = int(addr_string, 16)

                # Find the next color and slice until there
                symbol = op[idx:op.find(idaapi.COLOR_OFF, idx)]

                if symbol == '':
                    # We couldn't figure out the symbol, so use the whole op_str
                    symbol = op_str

                comments += [f"{symbol}={addr:#x}"]

                # print its value if its type is available
                try:
                    value = get_global_variable_value_internal(addr)
                except:
                    continue

                comments += [f"*{symbol}={value}"]

            operands += [op_str]

        mnem = ida_lines.tag_remove(insn_section.substr(raw_instruction)) if insn_section else ""
        mnem = mnem or "nop"  # 确保mnem不为空
        instruction = f"{mnem} {', '.join(operands)}" if operands else mnem

        line = DisassemblyLine(
            address=f"{address:#x}",
            instruction=instruction,
        )

        if len(comments) > 0:
            line.update(comments=comments)

        if segment:
            line.update(segment=segment)

        if label:
            line.update(label=label)

        lines += [line]

    # 获取函数原型信息
    try:
        prototype = func.get_prototype()
        if prototype:
            arguments: list[Argument] = []
            try:
                for arg in prototype.iter_func():
                    arguments.append(Argument(name=arg.name, type=f"{arg.type}"))
            except (AttributeError, TypeError):
                arguments = []
        else:
            arguments = []
    except (AttributeError, TypeError):
        prototype = None
        arguments = []

    disassembly_function = DisassemblyFunction(
        name=func_name,
        start_ea=f"{func.start_ea:#x}",
        stack_frame=get_stack_frame_variables_internal(func.start_ea),
        lines=lines
    )

    if prototype:
        try:
            return_type = f"{prototype.get_rettype()}"
            disassembly_function.update(return_type=return_type)
        except (AttributeError, TypeError):
            # 无法获取返回类型信息，设置默认值
            disassembly_function.update(return_type="unknown")

    if arguments:
        disassembly_function.update(arguments=arguments)

    return disassembly_function

class Xref(TypedDict):
    address: str
    type: str
    function: Optional[Function]

@jsonrpc
@idaread
def get_xrefs_to(
    address: Annotated[str, "Address to get cross references to"],
) -> list[Xref]:
    """Get all cross references to the given address"""
    xrefs = []
    xref: ida_xref.xrefblk_t
    for xref in idautils.XrefsTo(parse_address(address)):
        xrefs += [
            Xref(address=hex(xref.frm),
                 type="code" if xref.iscode else "data",
                 function=get_function(xref.frm, raise_error=False))
        ]
    return xrefs

@jsonrpc
@idaread
def get_xrefs_to_field(
    struct_name: Annotated[str, "Name of the struct (type) containing the field"],
    field_name: Annotated[str, "Name of the field (member) to get xrefs to"],
) -> list[Xref]:
    """Get all cross references to a named struct field (member)"""

    # Get the type library
    til = ida_typeinf.get_idati()
    if not til:
        raise IDAError("Failed to retrieve type library.")

    # Get the structure type info
    tif = ida_typeinf.tinfo_t()
    if not tif.get_named_type(til, struct_name, ida_typeinf.BTF_STRUCT, True, False):
        print(f"Structure '{struct_name}' not found.")
        return []

    # Get The field index
    idx = ida_typeinf.get_udm_by_fullname(None, struct_name + '.' + field_name)
    if idx == -1:
        print(f"Field '{field_name}' not found in structure '{struct_name}'.")
        return []

    # Get the type identifier
    tid = tif.get_udm_tid(idx)
    if tid == ida_idaapi.BADADDR:
        raise IDAError(f"Unable to get tid for structure '{struct_name}' and field '{field_name}'.")

    # Get xrefs to the tid
    xrefs = []
    xref: ida_xref.xrefblk_t
    for xref in idautils.XrefsTo(tid):

        xrefs += [
            Xref(address=hex(xref.frm),
                 type="code" if xref.iscode else "data",
                 function=get_function(xref.frm, raise_error=False))
        ]
    return xrefs

@jsonrpc
@idaread
def get_entry_points() -> list[Function]:
    """Get all entry points in the database"""
    result = []
    for i in range(ida_entry.get_entry_qty()):
        ordinal = ida_entry.get_entry_ordinal(i)
        address = ida_entry.get_entry(ordinal)
        func = get_function(address, raise_error=False)
        if func is not None:
            result.append(func)
    return result

@jsonrpc
@idawrite
def set_comment(
    address: Annotated[str, "Address in the function to set the comment for"],
    comment: Annotated[str, "Comment text"],
):
    """Set a comment for a given address in the function disassembly and pseudocode"""
    addr_ea = parse_address(address)

    if not idaapi.set_cmt(addr_ea, comment, False):
        raise IDAError(f"Failed to set disassembly comment at {hex(addr_ea)}")

    if not ida_hexrays.init_hexrays_plugin():
        return

    # Reference: https://cyber.wtf/2019/03/22/using-ida-python-to-analyze-trickbot/
    # Check if the address corresponds to a line
    try:
        cfunc = decompile_checked(addr_ea)
    except DecompilerLicenseError:
        # We failed to decompile the function due to a decompiler license error
        return

    # Special case for function entry comments
    if addr_ea == cfunc.entry_ea:
        idc.set_func_cmt(addr_ea, comment, True)
        cfunc.refresh_func_ctext()
        return

    eamap = cfunc.get_eamap()
    if addr_ea not in eamap:
        print(f"Failed to set decompiler comment at {hex(addr_ea)}")
        return
    nearest_ea = eamap[addr_ea][0].ea

    # Remove existing orphan comments
    if cfunc.has_orphan_cmts():
        cfunc.del_orphan_cmts()
        cfunc.save_user_cmts()

    # Set the comment by trying all possible item types
    tl = idaapi.treeloc_t()
    tl.ea = nearest_ea
    for itp in range(idaapi.ITP_SEMI, idaapi.ITP_COLON):
        tl.itp = itp
        cfunc.set_user_cmt(tl, comment)
        cfunc.save_user_cmts()
        cfunc.refresh_func_ctext()
        if not cfunc.has_orphan_cmts():
            return
        cfunc.del_orphan_cmts()
        cfunc.save_user_cmts()
    print(f"Failed to set decompiler comment at {hex(addr_ea)}")

def refresh_decompiler_widget():
    widget = ida_kernwin.get_current_widget()
    if widget is not None:
        vu = ida_hexrays.get_widget_vdui(widget)
        if vu is not None:
            vu.refresh_ctext()

def refresh_decompiler_ctext(function_address: int):
    error = ida_hexrays.hexrays_failure_t()
    cfunc: ida_hexrays.cfunc_t = ida_hexrays.decompile_func(function_address, error, ida_hexrays.DECOMP_WARNINGS)
    if cfunc:
        cfunc.refresh_func_ctext()

def check_local_variable_exists(function_ea: int, variable_name: str) -> bool:
    """检查局部变量是否存在于指定函数中"""
    try:
        # 尝试获取函数的反编译信息
        cfunc = ida_hexrays.decompile(function_ea)
        if not cfunc:
            return False

        # 检查局部变量列表
        lvars = cfunc.get_lvars()
        if not lvars:
            return False

        # 查找指定名称的变量
        for lvar in lvars:
            if lvar.name == variable_name:
                return True

        return False
    except Exception:
        return False

@jsonrpc
@idawrite
def rename_local_variable(
    function_address: Annotated[str, "Address of the function containing the variable"],
    old_name: Annotated[str, "Current name of the variable"],
    new_name: Annotated[str, "New name for the variable (empty for a default name)"],
):
    """Rename a local variable in a function"""
    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    # 检查局部变量是否存在
    if not check_local_variable_exists(func.start_ea, old_name):
        # 如果变量不存在，提供有用的错误信息和建议
        available_vars = []
        try:
            cfunc = ida_hexrays.decompile(func.start_ea)
            if cfunc:
                lvars = cfunc.get_lvars()
                if lvars:
                    available_vars = [lvar.name for lvar in lvars if lvar.name]
        except Exception:
            pass

        error_msg = f"Local variable '{old_name}' not found in function {hex(func.start_ea)}"
        if available_vars:
            error_msg += f". Available variables: {', '.join(available_vars)}"
        else:
            error_msg += ". No local variables found in this function."
        raise IDAError(error_msg)

    if not ida_hexrays.rename_lvar(func.start_ea, old_name, new_name):
        raise IDAError(f"Failed to rename local variable {old_name} in function {hex(func.start_ea)}")
    refresh_decompiler_ctext(func.start_ea)

@jsonrpc
@idawrite
def rename_global_variable(
    old_name: Annotated[str, "Current name of the global variable"],
    new_name: Annotated[str, "New name for the global variable (empty for a default name)"],
):
    """Rename a global variable"""
    ea = idaapi.get_name_ea(idaapi.BADADDR, old_name)
    if not idaapi.set_name(ea, new_name):
        raise IDAError(f"Failed to rename global variable {old_name} to {new_name}")
    refresh_decompiler_ctext(ea)

@jsonrpc
@idawrite
def set_global_variable_type(
    variable_name: Annotated[str, "Name of the global variable"],
    new_type: Annotated[str, "New type for the variable"],
):
    """Set a global variable's type"""
    ea = idaapi.get_name_ea(idaapi.BADADDR, variable_name)
    tif = get_type_by_name(new_type)
    if not tif:
        raise IDAError(f"Parsed declaration is not a variable type")
    if not ida_typeinf.apply_tinfo(ea, tif, ida_typeinf.PT_SIL):
        raise IDAError(f"Failed to apply type")

@jsonrpc
@idaread
def get_global_variable_value_by_name(variable_name: Annotated[str, "Name of the global variable"]) -> str:
    """
    Read a global variable's value (if known at compile-time)

    Prefer this function over the `data_read_*` functions.
    """
    ea = idaapi.get_name_ea(idaapi.BADADDR, variable_name)
    if ea == idaapi.BADADDR:
        raise IDAError(f"Global variable {variable_name} not found")

    return get_global_variable_value_internal(ea)

@jsonrpc
@idaread
def get_global_variable_value_at_address(ea: Annotated[str, "Address of the global variable"]) -> str:
    """
    Read a global variable's value by its address (if known at compile-time)

    Prefer this function over the `data_read_*` functions.
    """
    addr_ea = parse_address(ea)
    return get_global_variable_value_internal(addr_ea)

def get_global_variable_value_internal(ea: int) -> str:
     # Get the type information for the variable
     tif = ida_typeinf.tinfo_t()
     if not ida_nalt.get_tinfo(tif, ea):
         # No type info, maybe we can figure out its size by its name
         if not ida_bytes.has_any_name(ea):
             raise IDAError(f"Failed to get type information for variable at {ea:#x}")

         size = ida_bytes.get_item_size(ea)
         if size == 0:
             raise IDAError(f"Failed to get type information for variable at {ea:#x}")
     else:
         # Determine the size of the variable
         size = tif.get_size()

     # Read the value based on the size
     if size == 0 and tif.is_array() and tif.get_array_element().is_decl_char():
         return_string = idaapi.get_strlit_contents(ea, -1, 0).decode("utf-8").strip()
         return f"\"{return_string}\""
     elif size == 1:
         return hex(ida_bytes.get_byte(ea))
     elif size == 2:
         return hex(ida_bytes.get_word(ea))
     elif size == 4:
         return hex(ida_bytes.get_dword(ea))
     elif size == 8:
         return hex(ida_bytes.get_qword(ea))
     else:
         # For other sizes, return the raw bytes
         return ' '.join(hex(x) for x in ida_bytes.get_bytes(ea, size))


@jsonrpc
@idawrite
def rename_function(
    function_address: Annotated[str, "Address of the function to rename"],
    new_name: Annotated[str, "New name for the function (empty for a default name)"],
):
    """Rename a function"""
    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")
    if not idaapi.set_name(func.start_ea, new_name):
        raise IDAError(f"Failed to rename function {hex(func.start_ea)} to {new_name}")
    refresh_decompiler_ctext(func.start_ea)

@jsonrpc
@idawrite
def set_function_prototype(
    function_address: Annotated[str, "Address of the function"],
    prototype: Annotated[str, "New function prototype"],
):
    """Set a function's prototype"""
    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")
    try:
        tif = ida_typeinf.tinfo_t(prototype, None, ida_typeinf.PT_SIL)
        if not tif.is_func():
            raise IDAError(f"Parsed declaration is not a function type")
        if not ida_typeinf.apply_tinfo(func.start_ea, tif, ida_typeinf.PT_SIL):
            raise IDAError(f"Failed to apply type")
        refresh_decompiler_ctext(func.start_ea)
    except Exception as e:
        raise IDAError(f"Failed to parse prototype string: {prototype}")

class my_modifier_t(ida_hexrays.user_lvar_modifier_t):
    def __init__(self, var_name: str, new_type: ida_typeinf.tinfo_t):
        ida_hexrays.user_lvar_modifier_t.__init__(self)
        self.var_name = var_name
        self.new_type = new_type

    def modify_lvars(self, lvars):
        for lvar_saved in lvars.lvvec:
            lvar_saved: ida_hexrays.lvar_saved_info_t
            if lvar_saved.name == self.var_name:
                lvar_saved.type = self.new_type
                return True
        return False

# NOTE: This is extremely hacky, but necessary to get errors out of IDA
def parse_decls_ctypes(decls: str, hti_flags: int) -> tuple[int, str]:
    if sys.platform == "win32":
        import ctypes

        assert isinstance(decls, str), "decls must be a string"
        assert isinstance(hti_flags, int), "hti_flags must be an int"
        c_decls = decls.encode("utf-8")
        c_til = None
        ida_dll = ctypes.CDLL("ida")
        ida_dll.parse_decls.argtypes = [
            ctypes.c_void_p,
            ctypes.c_char_p,
            ctypes.c_void_p,
            ctypes.c_int,
        ]
        ida_dll.parse_decls.restype = ctypes.c_int

        messages = []

        @ctypes.CFUNCTYPE(ctypes.c_int, ctypes.c_char_p, ctypes.c_char_p)
        def magic_printer(fmt: bytes, arg1: bytes):
            if fmt.count(b"%") == 1 and b"%s" in fmt:
                formatted = fmt.replace(b"%s", arg1)
                messages.append(formatted.decode("utf-8"))
                return len(formatted) + 1
            else:
                messages.append(f"unsupported magic_printer fmt: {repr(fmt)}")
                return 0

        errors = ida_dll.parse_decls(c_til, c_decls, magic_printer, hti_flags)
    else:
        # NOTE: The approach above could also work on other platforms, but it's
        # not been tested and there are differences in the vararg ABIs.
        errors = ida_typeinf.parse_decls(None, decls, False, hti_flags)
        messages = []
    return errors, messages

@jsonrpc
@idawrite
def declare_c_type(
    c_declaration: Annotated[str, "C declaration of the type. Examples include: typedef int foo_t; struct bar { int a; bool b; };"],
):
    """Create or update a local type from a C declaration"""
    # PT_SIL: Suppress warning dialogs (although it seems unnecessary here)
    # PT_EMPTY: Allow empty types (also unnecessary?)
    # PT_TYP: Print back status messages with struct tags
    flags = ida_typeinf.PT_SIL | ida_typeinf.PT_EMPTY | ida_typeinf.PT_TYP
    errors, messages = parse_decls_ctypes(c_declaration, flags)

    pretty_messages = "\n".join(messages)
    if errors > 0:
        raise IDAError(f"Failed to parse type:\n{c_declaration}\n\nErrors:\n{pretty_messages}")
    return f"success\n\nInfo:\n{pretty_messages}"

@jsonrpc
@idawrite
def set_local_variable_type(
    function_address: Annotated[str, "Address of the decompiled function containing the variable"],
    variable_name: Annotated[str, "Name of the variable"],
    new_type: Annotated[str, "New type for the variable"],
):
    """Set a local variable's type"""
    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    # 检查局部变量是否存在
    if not check_local_variable_exists(func.start_ea, variable_name):
        # 如果变量不存在，提供有用的错误信息和建议
        available_vars = []
        try:
            cfunc = ida_hexrays.decompile(func.start_ea)
            if cfunc:
                lvars = cfunc.get_lvars()
                if lvars:
                    available_vars = [lvar.name for lvar in lvars if lvar.name]
        except Exception:
            pass

        error_msg = f"Local variable '{variable_name}' not found in function {hex(func.start_ea)}"
        if available_vars:
            error_msg += f". Available variables: {', '.join(available_vars)}"
        else:
            error_msg += ". No local variables found in this function."
        raise IDAError(error_msg)

    # 解析新类型
    try:
        # Some versions of IDA don't support this constructor
        new_tif = ida_typeinf.tinfo_t(new_type, None, ida_typeinf.PT_SIL)
    except Exception:
        try:
            new_tif = ida_typeinf.tinfo_t()
            # parse_decl requires semicolon for the type
            ida_typeinf.parse_decl(new_tif, None, new_type + ";", ida_typeinf.PT_SIL)
        except Exception:
            raise IDAError(f"Failed to parse type: {new_type}")

    # 验证变量存在（使用rename_lvar作为存在性检查）
    if not ida_hexrays.rename_lvar(func.start_ea, variable_name, variable_name):
        raise IDAError(f"Failed to access local variable: {variable_name}")

    # 修改变量类型
    modifier = my_modifier_t(variable_name, new_tif)
    if not ida_hexrays.modify_user_lvars(func.start_ea, modifier):
        raise IDAError(f"Failed to modify local variable type: {variable_name}")
    refresh_decompiler_ctext(func.start_ea)

class StackFrameVariable(TypedDict):
    name: str
    offset: str
    size: str
    type: str

@jsonrpc
@idaread
def get_stack_frame_variables(
        function_address: Annotated[str, "Address of the disassembled function to retrieve the stack frame variables"]
) -> list[StackFrameVariable]:
    """ Retrieve the stack frame variables for a given function """
    return get_stack_frame_variables_internal(parse_address(function_address))

def get_stack_frame_variables_internal(function_address: int) -> list[dict]:
    func = idaapi.get_func(function_address)
    if not func:
        return []  # 返回空列表而不是抛出异常

    members = []
    try:
        tif = ida_typeinf.tinfo_t()
        if not tif.get_type_by_tid(func.frame) or not tif.is_udt():
            return []

        udt = ida_typeinf.udt_type_data_t()
        tif.get_udt_details(udt)
        for udm in udt:
            if not udm.is_gap():
                name = udm.name or f"var_{udm.offset:X}"
                offset = udm.offset // 8
                size = udm.size // 8
                type_str = str(udm.type) if udm.type else "unknown"

                members.append({
                    "name": name,
                    "offset": hex(offset),
                    "size": hex(size),
                    "type": type_str
                })
    except Exception:
        # 如果获取堆栈帧信息失败，返回空列表
        return []

    return members


class StructureMember(TypedDict):
    name: str
    offset: str
    size: str
    type: str

class StructureDefinition(TypedDict):
    name: str
    size: str
    members: list[StructureMember]

@jsonrpc
@idaread
def get_defined_structures() -> list[StructureDefinition]:
    """ Returns a list of all defined structures """

    rv = []
    limit = ida_typeinf.get_ordinal_limit()
    for ordinal in range(1, limit):
        tif = ida_typeinf.tinfo_t()
        tif.get_numbered_type(None, ordinal)
        if tif.is_udt():
            udt = ida_typeinf.udt_type_data_t()
            members = []
            if tif.get_udt_details(udt):
                members = [
                    StructureMember(name=x.name,
                                    offset=hex(x.offset // 8),
                                    size=hex(x.size // 8),
                                    type=str(x.type))
                    for _, x in enumerate(udt)
                ]

            rv += [StructureDefinition(name=tif.get_type_name(),
                                       size=hex(tif.get_size()),
                                       members=members)]

    return rv

@jsonrpc
@idawrite
def rename_stack_frame_variable(
        function_address: Annotated[str, "Address of the disassembled function to set the stack frame variables"],
        old_name: Annotated[str, "Current name of the variable"],
        new_name: Annotated[str, "New name for the variable (empty for a default name)"]
):
    """ Change the name of a stack variable for an IDA function """
    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    frame_tif = ida_typeinf.tinfo_t()
    if not ida_frame.get_func_frame(frame_tif, func):
        raise IDAError("No frame returned.")

    idx, udm = frame_tif.get_udm(old_name)
    if not udm:
        raise IDAError(f"{old_name} not found.")

    tid = frame_tif.get_udm_tid(idx)
    if ida_frame.is_special_frame_member(tid):
        raise IDAError(f"{old_name} is a special frame member. Will not change the name.")

    udm = ida_typeinf.udm_t()
    frame_tif.get_udm_by_tid(udm, tid)
    offset = udm.offset // 8
    if ida_frame.is_funcarg_off(func, offset):
        raise IDAError(f"{old_name} is an argument member. Will not change the name.")

    sval = ida_frame.soff_to_fpoff(func, offset)
    if not ida_frame.define_stkvar(func, new_name, sval, udm.type):
        raise IDAError("failed to rename stack frame variable")

@jsonrpc
@idawrite
def create_stack_frame_variable(
        function_address: Annotated[str, "Address of the disassembled function to set the stack frame variables"],
        offset: Annotated[str, "Offset of the stack frame variable"],
        variable_name: Annotated[str, "Name of the stack variable"],
        type_name: Annotated[str, "Type of the stack variable"]
):
    """ For a given function, create a stack variable at an offset and with a specific type """

    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    offset = parse_address(offset)

    frame_tif = ida_typeinf.tinfo_t()
    if not ida_frame.get_func_frame(frame_tif, func):
        raise IDAError("No frame returned.")

    tif = get_type_by_name(type_name)
    if not ida_frame.define_stkvar(func, variable_name, offset, tif):
        raise IDAError("failed to define stack frame variable")

@jsonrpc
@idawrite
def set_stack_frame_variable_type(
        function_address: Annotated[str, "Address of the disassembled function to set the stack frame variables"],
        variable_name: Annotated[str, "Name of the stack variable"],
        type_name: Annotated[str, "Type of the stack variable"]
):
    """ For a given disassembled function, set the type of a stack variable """

    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    frame_tif = ida_typeinf.tinfo_t()
    if not ida_frame.get_func_frame(frame_tif, func):
        raise IDAError("No frame returned.")

    idx, udm = frame_tif.get_udm(variable_name)
    if not udm:
        raise IDAError(f"{variable_name} not found.")

    tid = frame_tif.get_udm_tid(idx)
    udm = ida_typeinf.udm_t()
    frame_tif.get_udm_by_tid(udm, tid)
    offset = udm.offset // 8

    tif = get_type_by_name(type_name)
    if not ida_frame.set_frame_member_type(func, offset, tif):
        raise IDAError("failed to set stack frame variable type")

@jsonrpc
@idawrite
def delete_stack_frame_variable(
        function_address: Annotated[str, "Address of the function to set the stack frame variables"],
        variable_name: Annotated[str, "Name of the stack variable"]
):
    """ Delete the named stack variable for a given function """

    func = idaapi.get_func(parse_address(function_address))
    if not func:
        raise IDAError(f"No function found at address {function_address}")

    frame_tif = ida_typeinf.tinfo_t()
    if not ida_frame.get_func_frame(frame_tif, func):
        raise IDAError("No frame returned.")

    idx, udm = frame_tif.get_udm(variable_name)
    if not udm:
        raise IDAError(f"{variable_name} not found.")

    tid = frame_tif.get_udm_tid(idx)
    if ida_frame.is_special_frame_member(tid):
        raise IDAError(f"{variable_name} is a special frame member. Will not delete.")

    udm = ida_typeinf.udm_t()
    frame_tif.get_udm_by_tid(udm, tid)
    offset = udm.offset // 8
    size = udm.size // 8
    if ida_frame.is_funcarg_off(func, offset):
        raise IDAError(f"{variable_name} is an argument member. Will not delete.")

    if not ida_frame.delete_frame_members(func, offset, offset+size):
        raise IDAError("failed to delete stack frame variable")

@jsonrpc
@idaread
def read_memory_bytes(
        memory_address: Annotated[str, "Address of the memory value to be read"],
        size: Annotated[int, "size of memory to read"]
) -> str:
    """
    Read bytes at a given address.

    Only use this function if `get_global_variable_at` and `get_global_variable_by_name`
    both failed.
    """
    return ' '.join(f'{x:#02x}' for x in ida_bytes.get_bytes(parse_address(memory_address), size))

@jsonrpc
@idaread
def data_read_byte(
    address: Annotated[str, "Address to get 1 byte value from"],
) -> int:
    """
    Read the 1 byte value at the specified address.

    Only use this function if `get_global_variable_at` failed.
    """
    ea = parse_address(address)
    return ida_bytes.get_wide_byte(ea)

@jsonrpc
@idaread
def data_read_word(
    address: Annotated[str, "Address to get 2 bytes value from"],
) -> int:
    """
    Read the 2 byte value at the specified address as a WORD.

    Only use this function if `get_global_variable_at` failed.
    """
    ea = parse_address(address)
    return ida_bytes.get_wide_word(ea)

@jsonrpc
@idaread
def data_read_dword(
    address: Annotated[str, "Address to get 4 bytes value from"],
) -> int:
    """
    Read the 4 byte value at the specified address as a DWORD.

    Only use this function if `get_global_variable_at` failed.
    """
    ea = parse_address(address)
    return ida_bytes.get_wide_dword(ea)

@jsonrpc
@idaread
def data_read_qword(
        address: Annotated[str, "Address to get 8 bytes value from"]
) -> int:
    """
    Read the 8 byte value at the specified address as a QWORD.

    Only use this function if `get_global_variable_at` failed.
    """
    ea = parse_address(address)
    return ida_bytes.get_qword(ea)

@jsonrpc
@idaread
def data_read_string(
        address: Annotated[str, "Address to get string from"]
) -> str:
    """
    Read the string at the specified address.

    Only use this function if `get_global_variable_at` failed.
    """
    try:
        return idaapi.get_strlit_contents(parse_address(address),-1,0).decode("utf-8")
    except Exception as e:
        return "Error:" + str(e)

@jsonrpc
@idaread
@unsafe
def dbg_get_registers() -> list[dict[str, str]]:
    """Get all registers and their values. This function is only available when debugging."""
    result = []
    dbg = ida_idd.get_dbg()
    # 检查调试器状态，非调试状态时抛出异常
    if not dbg or not ida_dbg.is_debugger_on():
        raise IDAError("调试器未运行，无法获取寄存器状态")
    for thread_index in range(ida_dbg.get_thread_qty()):
        tid = ida_dbg.getn_thread(thread_index)
        regs = []
        regvals = ida_dbg.get_reg_vals(tid)
        for reg_index, rv in enumerate(regvals):
            reg_info = dbg.regs(reg_index)
            reg_value = rv.pyval(reg_info.dtype)
            if isinstance(reg_value, int):
                reg_value = hex(reg_value)
            if isinstance(reg_value, bytes):
                reg_value = reg_value.hex(" ")
            regs.append({
                "name": reg_info.name,
                "value": reg_value,
            })
        result.append({
            "thread_id": tid,
            "registers": regs,
        })
    return result

@jsonrpc
@idaread
@unsafe
def dbg_get_call_stack() -> list[dict[str, str]]:
    """Get the current call stack."""
    callstack = []
    try:
        tid = ida_dbg.get_current_thread()
        trace = ida_idd.call_stack_t()

        if not ida_dbg.collect_stack_trace(tid, trace):
            return []
        for frame in trace:
            frame_info = {
                "address": hex(frame.callea),
            }
            try:
                module_info = ida_idd.modinfo_t()
                if ida_dbg.get_module_info(frame.callea, module_info):
                    frame_info["module"] = os.path.basename(module_info.name)
                else:
                    frame_info["module"] = "<unknown>"

                name = (
                    ida_name.get_nice_colored_name(
                        frame.callea,
                        ida_name.GNCN_NOCOLOR
                        | ida_name.GNCN_NOLABEL
                        | ida_name.GNCN_NOSEG
                        | ida_name.GNCN_PREFDBG,
                    )
                    or "<unnamed>"
                )
                frame_info["symbol"] = name

            except Exception as e:
                frame_info["module"] = "<error>"
                frame_info["symbol"] = str(e)

            callstack.append(frame_info)

    except Exception as e:
        # 获取调用栈失败，返回空列表并记录错误
        print(f"获取调用栈时发生错误: {e}")
    return callstack

def list_breakpoints():
    ea = ida_ida.inf_get_min_ea()
    end_ea = ida_ida.inf_get_max_ea()
    breakpoints = []
    while ea <= end_ea:
        bpt = ida_dbg.bpt_t()
        if ida_dbg.get_bpt(ea, bpt):
            breakpoints.append(
                {
                    "ea": hex(bpt.ea),
                    "type": bpt.type,
                    "enabled": bpt.flags & ida_dbg.BPT_ENABLED,
                    "condition": bpt.condition if bpt.condition else None,
                }
            )
        ea = ida_bytes.next_head(ea, end_ea)
    return breakpoints

@jsonrpc
@idaread
@unsafe
def dbg_list_breakpoints():
    """List all breakpoints in the program."""
    return list_breakpoints()

@jsonrpc
@idaread
@unsafe
def dbg_start_process() -> str:
    """Start the debugger"""
    if idaapi.start_process("", "", ""):
        return "Debugger started"
    return "Failed to start debugger"

@jsonrpc
@idaread
@unsafe
def dbg_exit_process() -> str:
    """Exit the debugger"""
    if idaapi.exit_process():
        return "Debugger exited"
    return "Failed to exit debugger"

@jsonrpc
@idaread
@unsafe
def dbg_continue_process() -> str:
    """Continue the debugger"""
    if idaapi.continue_process():
        return "Debugger continued"
    return "Failed to continue debugger"

@jsonrpc
@idaread
@unsafe
def dbg_run_to(
    address: Annotated[str, "Run the debugger to the specified address"],
) -> str:
    """Run the debugger to the specified address"""
    ea = parse_address(address)
    if idaapi.run_to(ea):
        return f"Debugger run to {hex(ea)}"
    return f"Failed to run to address {hex(ea)}"

@jsonrpc
@idaread
@unsafe
def dbg_set_breakpoint(
    address: Annotated[str, "Set a breakpoint at the specified address"],
) -> str:
    """Set a breakpoint at the specified address"""
    ea = parse_address(address)
    if idaapi.add_bpt(ea, 0, idaapi.BPT_SOFT):
        return f"Breakpoint set at {hex(ea)}"
    breakpoints = list_breakpoints()
    for bpt in breakpoints:
        if bpt["ea"] == hex(ea):
            return f"Breakpoint already exists at {hex(ea)}"
    return f"Failed to set breakpoint at address {hex(ea)}"

@jsonrpc
@idaread
@unsafe
def dbg_delete_breakpoint(
    address: Annotated[str, "del a breakpoint at the specified address"],
) -> str:
    """del a breakpoint at the specified address"""
    ea = parse_address(address)
    if idaapi.del_bpt(ea):
        return f"Breakpoint deleted at {hex(ea)}"
    return f"Failed to delete breakpoint at address {hex(ea)}"

@jsonrpc
@idaread
@unsafe
def dbg_enable_breakpoint(
    address: Annotated[str, "Enable or disable a breakpoint at the specified address"],
    enable: Annotated[bool, "Enable or disable a breakpoint"],
) -> str:
    """Enable or disable a breakpoint at the specified address"""
    ea = parse_address(address)
    if idaapi.enable_bpt(ea, enable):
        return f"Breakpoint {'enabled' if enable else 'disabled'} at {hex(ea)}"
    return f"Failed to {'' if enable else 'disable '}breakpoint at address {hex(ea)}"

class MCP(idaapi.plugin_t):
    flags = idaapi.PLUGIN_KEEP
    comment = "MCP Plugin"
    help = "MCP"
    wanted_name = "MCP"
    wanted_hotkey = "Ctrl-Alt-M"

    def init(self):
        self.server = Server()
        hotkey = MCP.wanted_hotkey.replace("-", "+")
        if sys.platform == "darwin":
            hotkey = hotkey.replace("Alt", "Option")
        print(f"[MCP] Plugin loaded, use Edit -> Plugins -> MCP ({hotkey}) to start the server")
        return idaapi.PLUGIN_KEEP

    def run(self, args):
        self.server.start()

    def term(self):
        self.server.stop()

def PLUGIN_ENTRY():
    return MCP()

# 控制流分析模块 - 破解导向的控制流分析功能
class VerificationPoint(TypedDict):
    address: str
    type: str
    instruction: str
    target: Optional[str]

class JumpCondition(TypedDict):
    address: str
    instruction: str
    condition: str
    true_target: str
    false_target: str

class CallChainEntry(TypedDict):
    caller: str
    callee: str
    call_site: str

class TamperingPoint(TypedDict):
    address: str
    function: str
    return_type: str
    modification_hint: str

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存，最多100个条目
@lazy_init_module('control_flow')
@idaread
def identify_verification_points(function_address: Annotated[str, "函数地址"]) -> list[VerificationPoint]:
    """识别关键验证逻辑点"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"未找到函数: {function_address}")
    
    patterns = lazy_module_manager.get_module_data('control_flow')['verification_patterns']
    points = []
    
    for addr in ida_funcs.func_item_iterator_t(func):
        raw_bytes = ida_bytes.get_bytes(addr, 8)
        if any(pattern in raw_bytes for pattern in patterns):
            disasm_line = idc.generate_disasm_line(addr, 0)
            mnem = disasm_line.split()[0].lower()
            if mnem in ['cmp', 'test', 'sub', 'xor']:
                points.append(VerificationPoint(
                    address=hex(addr),
                    type="comparison",
                    instruction=disasm_line,
                    target=None
                ))
    
    return points[:10]  # 限制返回数量

@jsonrpc
@lazy_init_module('control_flow')
@idaread 
def analyze_jump_conditions(function_address: Annotated[str, "函数地址"]) -> list[JumpCondition]:
    """分析条件跳转逻辑"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"未找到函数: {function_address}")
    
    jump_opcodes = lazy_module_manager.get_module_data('control_flow')['jump_opcodes']
    conditions = []
    
    for addr in ida_funcs.func_item_iterator_t(func):
        raw_bytes = ida_bytes.get_bytes(addr, 1)
        if any(raw_bytes.startswith(opcode) for opcode in jump_opcodes):
            disasm_line = idc.generate_disasm_line(addr, 0)
            mnem = disasm_line.split()[0].lower()
            if mnem.startswith('j') and mnem != 'jmp':
                # 尝试获取目标地址
                try:
                    operand = idc.get_operand_value(addr, 0)
                    true_target = hex(operand) if operand != idaapi.BADADDR else "unknown"
                except:
                    true_target = "unknown"
                false_target = hex(addr + idc.get_item_size(addr))
                
                conditions.append(JumpCondition(
                    address=hex(addr),
                    instruction=disasm_line,
                    condition=mnem,
                    true_target=true_target,
                    false_target=false_target
                ))
    
    return conditions[:8]

@jsonrpc
@lazy_init_module('control_flow')
@idaread
def trace_function_call_chain(start_address: Annotated[str, "起始地址"]) -> list[CallChainEntry]:
    """追踪函数调用链"""
    ea = parse_address(start_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"未找到函数: {start_address}")
    
    chain = []
    caller_name = ida_funcs.get_func_name(func.start_ea)
    
    for addr in ida_funcs.func_item_iterator_t(func):
        disasm_line = idc.generate_disasm_line(addr, 0)
        mnem = disasm_line.split()[0].lower()
        
        if mnem == 'call':
            try:
                callee_addr = idc.get_operand_value(addr, 0)
                if callee_addr != idaapi.BADADDR:
                    callee_func = idaapi.get_func(callee_addr)
                    if callee_func:
                        callee_name = ida_funcs.get_func_name(callee_func.start_ea)
                        chain.append(CallChainEntry(
                            caller=caller_name,
                            callee=callee_name,
                            call_site=hex(addr)
                        ))
            except:
                continue
    
    return chain[:12]

@jsonrpc
@lazy_init_module('control_flow')
@idaread
def identify_return_tampering_points(function_address: Annotated[str, "函数地址"]) -> list[TamperingPoint]:
    """定位返回值篡改点"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"未找到函数: {function_address}")
    
    points = []
    func_name = ida_funcs.get_func_name(func.start_ea)
    
    for addr in ida_funcs.func_item_iterator_t(func):
        disasm_line = idc.generate_disasm_line(addr, 0)
        mnem = disasm_line.split()[0].lower()
        
        if mnem in ['ret', 'retn']:
            # 检查返回前的指令
            prev_addr = idaapi.prev_head(addr, func.start_ea)
            if prev_addr != idaapi.BADADDR:
                prev_disasm = idc.generate_disasm_line(prev_addr, 0)
                prev_mnem = prev_disasm.split()[0].lower()
                if prev_mnem in ['mov', 'xor', 'sub', 'add']:
                    # 检查是否修改返回寄存器 (简化检查)
                    if any(reg in prev_disasm.lower() for reg in ['eax', 'rax', 'al', 'ax']):
                        points.append(TamperingPoint(
                            address=hex(prev_addr),
                            function=func_name,
                            return_type="register_modification",
                            modification_hint="修改返回寄存器值以绕过验证"
                        ))
    
    return points[:6]

# 加密算法识别模块 - 自动识别加密算法特征
class CryptoMatch(TypedDict):
    algorithm: str
    confidence: float
    address: str
    signature_type: str

class CryptoKey(TypedDict):
    address: str
    algorithm: str
    key_size: int
    confidence: float

class CryptoFlow(TypedDict):
    algorithm: str
    operations: list[str]
    data_flow: list[str]
    confidence: float

@jsonrpc
@cached_analysis(cache_ttl=1800, cache_size_limit=50)  # 30分钟缓存，最多50个条目
@lazy_init_module('crypto')
@idaread
def identify_crypto_algorithms(search_area: Annotated[Optional[str], "搜索区域地址（可选）"] = None) -> list[CryptoMatch]:
    """识别加密算法特征"""
    signatures = lazy_module_manager.get_module_data('crypto')
    matches = []
    
    if search_area:
        start_ea = parse_address(search_area)
        end_ea = start_ea + 0x1000  # 搜索4KB范围
    else:
        start_ea = idaapi.get_imagebase()
        end_ea = start_ea + 0x10000  # 搜索64KB范围
    
    # AES S-box识别
    aes_sbox = signatures['aes_sbox']
    # 使用安全的字节搜索函数
    addr = safe_find_bytes(start_ea, end_ea, aes_sbox[:8])
    if addr != idaapi.BADADDR:
        matches.append(CryptoMatch(
            algorithm="AES",
            confidence=0.85,
            address=hex(addr),
            signature_type="sbox"
        ))
    
    # DES S-box识别
    des_sbox = signatures['des_sbox']
    addr = safe_find_bytes(start_ea, end_ea, des_sbox[:8])
    if addr != idaapi.BADADDR:
        matches.append(CryptoMatch(
            algorithm="DES",
            confidence=0.80,
            address=hex(addr),
            signature_type="sbox"
        ))
    
    # RSA特征识别
    for pattern in signatures['rsa_patterns']:
        addr = safe_find_bytes(start_ea, end_ea, pattern)
        if addr != idaapi.BADADDR:
            matches.append(CryptoMatch(
                algorithm="RSA",
                confidence=0.70,
                address=hex(addr),
                signature_type="asn1_header"
            ))
    
    return matches[:8]

@jsonrpc
@lazy_init_module('crypto')
@idaread
def locate_crypto_keys(algorithm: Annotated[str, "算法类型"], search_address: Annotated[str, "搜索起始地址"]) -> list[CryptoKey]:
    """定位加密密钥"""
    ea = parse_address(search_address)
    keys = []
    
    if algorithm.upper() == "AES":
        # AES密钥通常是16/24/32字节的连续数据
        for key_size in [16, 24, 32]:
            for offset in range(0, 0x1000, 4):  # 搜索4KB范围
                addr = ea + offset
                key_data = ida_bytes.get_bytes(addr, key_size)
                if key_data and len(key_data) == key_size:
                    # 简单启发式：检查密钥熵
                    entropy = len(set(key_data)) / len(key_data)
                    if entropy > 0.6:  # 高熵值可能是密钥
                        keys.append(CryptoKey(
                            address=hex(addr),
                            algorithm="AES",
                            key_size=key_size,
                            confidence=entropy
                        ))
    
    elif algorithm.upper() == "DES":
        # DES密钥是8字节
        for offset in range(0, 0x800, 4):
            addr = ea + offset
            key_data = ida_bytes.get_bytes(addr, 8)
            if key_data and len(key_data) == 8:
                entropy = len(set(key_data)) / len(key_data)
                if entropy > 0.5:
                    keys.append(CryptoKey(
                        address=hex(addr),
                        algorithm="DES",
                        key_size=8,
                        confidence=entropy
                    ))
    
    return keys[:6]

@jsonrpc
@lazy_init_module('crypto')
@idaread
def analyze_crypto_flow(function_address: Annotated[str, "函数地址"]) -> CryptoFlow:
    """分析加密流程"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"未找到函数: {function_address}")
    
    operations = []
    data_flow = []
    algorithm = "unknown"
    confidence = 0.0
    
    # 分析函数内的操作模式
    xor_count = 0
    shift_count = 0
    loop_count = 0
    
    for addr in ida_funcs.func_item_iterator_t(func):
        disasm = idc.generate_disasm_line(addr, 0)
        mnem = disasm.split()[0].lower()
        
        if mnem == 'xor':
            xor_count += 1
            operations.append("xor")
        elif mnem in ['shl', 'shr', 'rol', 'ror']:
            shift_count += 1
            operations.append("shift")
        elif mnem in ['loop', 'loope', 'loopne']:
            loop_count += 1
            operations.append("loop")
        elif mnem == 'call':
            operations.append("call")
            data_flow.append(hex(addr))
    
    # 简单的算法推断
    if xor_count > 5 and shift_count > 3:
        algorithm = "stream_cipher"
        confidence = min(0.8, (xor_count + shift_count) / 20.0)
    elif loop_count > 0 and xor_count > 3:
        algorithm = "block_cipher"
        confidence = min(0.7, (loop_count + xor_count) / 15.0)
    elif shift_count > 8:
        algorithm = "hash_function"
        confidence = min(0.6, shift_count / 20.0)
    
    return CryptoFlow(
        algorithm=algorithm,
        operations=operations[:10],
        data_flow=data_flow[:8],
        confidence=confidence
    )

# 反调试检测模块 - 自动检测和绕过反调试技术
class AntiDebugDetection(TypedDict):
    technique: str
    address: str
    api_name: str
    risk_level: str
    bypass_suggestion: str

class BypassStrategy(TypedDict):
    target: str
    method: str
    description: str
    risk_level: str
    patch_bytes: Optional[str]

class PatchResult(TypedDict):
    address: str
    original_bytes: str
    patched_bytes: str
    status: str
    backup_info: str

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=75)
@lazy_init_module('anti_debug')
@idaread
def detect_anti_debug_techniques(search_area: Annotated[Optional[str], "搜索区域（可选）"] = None) -> list[AntiDebugDetection]:
    """检测反调试技术"""
    signatures = lazy_module_manager.get_module_data('anti_debug')
    detections = []
    if search_area:
        start_ea = parse_address(search_area)
        end_ea = start_ea + 0x2000
    else:
        start_ea = idaapi.get_imagebase()
        end_ea = start_ea + 0x20000
    for api_name in signatures['api_signatures']:
        for addr, name in idautils.Names():
            if start_ea <= addr <= end_ea and api_name.lower() in name.lower():
                for xref in idautils.XrefsTo(addr):
                    if xref.type == ida_xref.fl_CN:
                        detections.append(AntiDebugDetection(technique="api_call",address=hex(xref.frm),api_name=api_name,risk_level="high",bypass_suggestion=f"在{hex(xref.frm)}处NOP掉对{api_name}的调用"))
    for pattern in signatures['time_check_patterns']:
        addr = safe_find_bytes(start_ea, end_ea, pattern)
        if addr != idaapi.BADADDR:
            detections.append(AntiDebugDetection(technique="timing_check",address=hex(addr),api_name="rdtsc/GetTickCount",risk_level="medium",bypass_suggestion="修改时间检查逻辑或固定时间差值"))
    addr = start_ea
    while addr < end_ea:
        byte_val = ida_bytes.get_byte(addr)
        if byte_val == 0xCC:
            func = idaapi.get_func(addr)
            if func:
                detections.append(AntiDebugDetection(technique="int3_check",address=hex(addr),api_name="INT3",risk_level="medium",bypass_suggestion="将INT3指令替换为NOP(0x90)"))
        addr += 1
        if len(detections) >= 5:
            break
    
    return detections[:10]

@jsonrpc
@lazy_init_module('anti_debug')
@idaread
def generate_bypass_strategies(detection_address: Annotated[str, "检测点地址"]) -> list[BypassStrategy]:
    """生成绕过策略"""
    ea = parse_address(detection_address)
    strategies = []
    
    # 获取当前指令
    disasm = idc.generate_disasm_line(ea, 0)
    mnem = disasm.split()[0].lower()
    
    if 'call' in mnem:
        # API调用绕过策略
        strategies.append(BypassStrategy(
            target=hex(ea),
            method="nop_instruction",
            description="将CALL指令替换为NOP指令",
            risk_level="low",
            patch_bytes="90" * idc.get_item_size(ea)
        ))
        
        strategies.append(BypassStrategy(
            target=hex(ea),
            method="return_zero",
            description="修改函数返回值为0（未检测到调试器）",
            risk_level="medium",
            patch_bytes="31C0C3"  # xor eax,eax; ret
        ))
    
    elif mnem == 'int':
        # 中断指令绕过
        strategies.append(BypassStrategy(
            target=hex(ea),
            method="nop_replace",
            description="将INT指令替换为NOP",
            risk_level="low",
            patch_bytes="90"
        ))
    
    elif mnem in ['rdtsc', 'cpuid']:
        # 时间检查绕过
        strategies.append(BypassStrategy(
            target=hex(ea),
            method="constant_value",
            description="返回固定的时间值",
            risk_level="medium",
            patch_bytes="B800000000C3"  # mov eax, 0; ret
        ))
    
    # 通用策略
    strategies.append(BypassStrategy(
        target=hex(ea),
        method="conditional_jump",
        description="修改条件跳转，强制跳过反调试检查",
        risk_level="high",
        patch_bytes="EB"  # JMP short
    ))
    
    return strategies[:6]

@jsonrpc
@lazy_init_module('anti_debug')
@idawrite
def apply_anti_debug_patches(patch_address: Annotated[str, "补丁地址"], patch_method: Annotated[str, "补丁方法"]) -> PatchResult:
    """应用反调试绕过补丁"""
    ea = parse_address(patch_address)
    original_size = idc.get_item_size(ea)
    original_bytes = ida_bytes.get_bytes(ea, original_size)
    if not original_bytes:
        raise IDAError(f"无法读取地址{patch_address}的原始字节")
    backup_info = f"{hex(ea)}:{original_bytes.hex()}"
    try:
        if patch_method == "nop_instruction":
            patch_bytes = b'\x90' * original_size
        elif patch_method == "return_zero":
            if original_size >= 3:
                patch_bytes = b'\x31\xC0\xC3' + b'\x90' * (original_size - 3)
            else:
                patch_bytes = b'\x90' * original_size
        elif patch_method == "nop_replace":
            patch_bytes = b'\x90' * original_size
        elif patch_method == "conditional_jump":
            if original_size >= 2:
                patch_bytes = b'\xEB\x00' + b'\x90' * (original_size - 2)
            else:
                patch_bytes = b'\x90' * original_size
        else:
            raise IDAError(f"不支持的补丁方法: {patch_method}")
        for i, byte_val in enumerate(patch_bytes):
            ida_bytes.patch_byte(ea + i, byte_val)
        return PatchResult(address=hex(ea),original_bytes=original_bytes.hex(),patched_bytes=patch_bytes.hex(),status="success",backup_info=backup_info)
    except Exception as e:
        return PatchResult(address=hex(ea),original_bytes=original_bytes.hex(),patched_bytes="",status=f"failed: {str(e)}",backup_info=backup_info)

# 许可证验证分析模块 - 用于分析软件许可证验证逻辑的破解导向工具
class LicenseValidation(TypedDict):
    location: str
    validation_type: str
    algorithm_pattern: str
    input_format: str
    validation_strength: str
    bypass_suggestion: str

class SerialFormat(TypedDict):
    pattern: str
    length: int
    charset: str
    validation_address: str
    algorithm_hint: str
    sample_format: str

class TimeRestriction(TypedDict):
    check_type: str
    address: str
    limit_value: str
    comparison_method: str
    bypass_strategy: str
    risk_level: str

class KeygenHint(TypedDict):
    algorithm_type: str
    input_constraints: str
    validation_steps: list[str]
    key_generation_strategy: str
    success_indicators: str
    difficulty_level: str

@jsonrpc
@lazy_init_module('license')
@idaread
def analyze_license_validation(target_area: Annotated[Optional[str], "目标分析区域（可选）"] = None) -> list[LicenseValidation]:
    """分析许可证验证逻辑"""
    patterns = lazy_module_manager.get_module_data('license')
    validations = []
    
    if target_area:
        start_ea = parse_address(target_area)
        end_ea = start_ea + 0x5000  # 搜索20KB范围
    else:
        start_ea = idaapi.get_imagebase()
        end_ea = start_ea + 0x50000  # 搜索320KB范围
    
    # 搜索许可证相关字符串
    for license_str in patterns['license_keywords']:
        string_refs = []
        for s in idautils.Strings():
            if license_str.lower() in str(s).lower() and start_ea <= s.ea <= end_ea:
                string_refs.append(s.ea)
        
        for str_addr in string_refs:
            # 查找引用此字符串的代码
            for xref in idautils.XrefsTo(str_addr):
                if xref.type == ida_xref.fl_CN:  # 代码引用
                    func = idaapi.get_func(xref.frm)
                    if func:
                        # 分析函数中的验证模式
                        validation_type = "string_check"
                        if "serial" in license_str.lower():
                            validation_type = "serial_validation"
                        elif "register" in license_str.lower():
                            validation_type = "registration_check"
                        elif "license" in license_str.lower():
                            validation_type = "license_verification"
                        
                        validations.append(LicenseValidation(
                            location=hex(xref.frm),
                            validation_type=validation_type,
                            algorithm_pattern="string_comparison",
                            input_format="user_input_string",
                            validation_strength="low",
                            bypass_suggestion=f"在{hex(xref.frm)}处修改比较结果或跳过验证"
                        ))
    
    # 检测常见的许可证验证算法模式
    for pattern in patterns['validation_patterns']:
        addr = safe_find_bytes(start_ea, end_ea, pattern)
        if addr != idaapi.BADADDR:
            # 分析周围的代码模式
            func = idaapi.get_func(addr)
            if func:
                validations.append(LicenseValidation(
                    location=hex(addr),
                    validation_type="algorithmic_check",
                    algorithm_pattern="checksum_validation",
                    input_format="numeric_calculation",
                    validation_strength="medium",
                    bypass_suggestion="识别算法逻辑，构造有效的验证值"
                ))
    
    return validations[:8]

@jsonrpc
@lazy_init_module('license')
@idaread
def trace_serial_validation(serial_input_area: Annotated[str, "序列号输入处理区域地址"]) -> list[SerialFormat]:
    """追踪序列号验证过程"""
    ea = parse_address(serial_input_area)
    patterns = lazy_module_manager.get_module_data('license')
    formats = []
    
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"地址{serial_input_area}不在有效函数内")
    
    # 分析函数中的数据流
    for addr in range(func.start_ea, func.end_ea, 4):
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            # 检测字符串长度检查
            if insn.itype == ida_allins.NN_cmp and insn.Op2.value in range(8, 64):
                formats.append(SerialFormat(
                    pattern="fixed_length",
                    length=insn.Op2.value,
                    charset="alphanumeric",
                    validation_address=hex(addr),
                    algorithm_hint="length_check",
                    sample_format=f"{'X' * insn.Op2.value}"
                ))

            # 检测分隔符模式
            if insn.itype == ida_allins.NN_cmp and insn.Op2.value in [ord('-'), ord('_'), ord('.')]:
                formats.append(SerialFormat(
                    pattern="separated_groups",
                    length=0,
                    charset="with_separators",
                    validation_address=hex(addr),
                    algorithm_hint="format_validation",
                    sample_format="XXXX-XXXX-XXXX-XXXX"
                ))
    
    # 检测数字范围验证
    for pattern in patterns['serial_patterns']:
        if safe_find_bytes(func.start_ea, func.end_ea, pattern) != idaapi.BADADDR:
            formats.append(SerialFormat(
                pattern="numeric_range",
                length=16,
                charset="digits_only",
                validation_address=hex(func.start_ea),
                algorithm_hint="range_validation",
                sample_format="1234567890123456"
            ))
    
    return formats[:6]

@jsonrpc
@lazy_init_module('license')
@idaread
def detect_time_limitations(search_scope: Annotated[Optional[str], "搜索范围（可选）"] = None) -> list[TimeRestriction]:
    """检测时间限制机制"""
    patterns = lazy_module_manager.get_module_data('license')
    restrictions = []
    
    if search_scope:
        start_ea = parse_address(search_scope)
        end_ea = start_ea + 0x3000  # 搜索12KB范围
    else:
        start_ea = idaapi.get_imagebase()
        end_ea = start_ea + 0x30000  # 搜索192KB范围
    
    # 检测时间相关API调用
    for time_api in patterns['time_apis']:
        for addr, name in idautils.Names():
            if start_ea <= addr <= end_ea and time_api.lower() in name.lower():
                for xref in idautils.XrefsTo(addr):
                    if xref.type == ida_xref.fl_CN:
                        restrictions.append(TimeRestriction(
                            check_type="api_time_check",
                            address=hex(xref.frm),
                            limit_value="unknown",
                            comparison_method="system_time",
                            bypass_strategy="Hook API返回固定时间或修改比较逻辑",
                            risk_level="medium"
                        ))
    
    # 检测时间戳比较模式
    for pattern in patterns['time_check_patterns']:
        addr = safe_find_bytes(start_ea, end_ea, pattern)
        if addr != idaapi.BADADDR:
            restrictions.append(TimeRestriction(
                check_type="timestamp_comparison",
                address=hex(addr),
                limit_value="embedded_timestamp",
                comparison_method="direct_comparison",
                bypass_strategy="修改内置时间戳或跳过比较",
                risk_level="low"
            ))
    
    # 检测试用期计数器
    for pattern in patterns['trial_patterns']:
        addr = safe_find_bytes(start_ea, end_ea, pattern)
        if addr != idaapi.BADADDR:
            restrictions.append(TimeRestriction(
                check_type="usage_counter",
                address=hex(addr),
                limit_value="usage_limit",
                comparison_method="counter_decrement",
                bypass_strategy="重置计数器或修改比较条件",
                risk_level="high"
            ))
    
    return restrictions[:5]

@jsonrpc
@lazy_init_module('license')
@idaread
def generate_keygen_hints(validation_function: Annotated[str, "验证函数地址"]) -> KeygenHint:
    """生成注册机提示信息"""
    ea = parse_address(validation_function)
    patterns = lazy_module_manager.get_module_data('license')
    
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"地址{validation_function}不是有效的函数")
    
    # 分析函数复杂度和验证步骤
    validation_steps = []
    algorithm_type = "unknown"
    difficulty = "medium"
    
    # 检测算法类型
    for algo_pattern, algo_name in patterns['algorithm_signatures'].items():
        if safe_find_bytes(func.start_ea, func.end_ea, algo_pattern) != idaapi.BADADDR:
            algorithm_type = algo_name
            break
    
    # 分析验证步骤
    instruction_count = 0
    has_complex_math = False
    has_string_ops = False
    
    for addr in range(func.start_ea, func.end_ea, 4):
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            instruction_count += 1
            
            # 检测复杂数学运算
            if insn.itype in [ida_allins.NN_mul, ida_allins.NN_div, ida_allins.NN_imul, ida_allins.NN_idiv]:
                has_complex_math = True
                validation_steps.append("执行数学运算验证")

            # 检测字符串操作
            if insn.itype in [ida_allins.NN_rep, ida_allins.NN_movs, ida_allins.NN_cmps]:
                has_string_ops = True
                validation_steps.append("进行字符串格式检查")
    
    # 确定难度级别
    if instruction_count > 100 or has_complex_math:
        difficulty = "high"
    elif instruction_count < 20 and not has_complex_math:
        difficulty = "low"
    
    # 生成输入约束
    input_constraints = "标准字符串输入"
    if has_string_ops:
        input_constraints = "格式化字符串，可能包含分隔符"
    if has_complex_math:
        input_constraints = "数值输入，需满足特定数学关系"
    
    return KeygenHint(
        algorithm_type=algorithm_type,
        input_constraints=input_constraints,
        validation_steps=validation_steps[:5],
        key_generation_strategy="反向工程验证算法，构造满足条件的输入",
        success_indicators="函数返回非零值或跳转到成功分支",
        difficulty_level=difficulty
    )

# 内存补丁与代码修改模块 - 运行时补丁和代码修改的高级破解技术
class MemoryPatch(TypedDict):
    patch_id: str
    address: str
    original_bytes: str
    patched_bytes: str
    size: int
    timestamp: str
    description: str
    is_active: bool

class InstructionMod(TypedDict):
    address: str
    original_instruction: str
    modified_instruction: str
    modification_type: str
    risk_level: str
    rollback_data: str

class FunctionHook(TypedDict):
    target_function: str
    hook_address: str
    hook_type: str
    original_entry: str
    redirect_target: str
    status: str

class ReturnValuePatch(TypedDict):
    function_address: str
    original_return_type: str
    patched_return_value: str
    patch_method: str
    success_rate: str

class PatchHistoryEntry(TypedDict):
    operation_id: str
    operation_type: str
    target_address: str
    timestamp: str
    operation_data: str
    rollback_possible: bool

@jsonrpc
@lazy_init_module('memory_patch')
@idawrite
def apply_memory_patch(target_address: Annotated[str, "目标地址"], patch_data: Annotated[str, "补丁数据（十六进制）"], description: Annotated[str, "补丁描述"]) -> MemoryPatch:
    """应用内存补丁"""
    ea = parse_address(target_address)
    patch_bytes = bytes.fromhex(patch_data.replace(' ', ''))
    patch_size = len(patch_bytes)
    
    # 读取原始字节
    original_bytes = ida_bytes.get_bytes(ea, patch_size)
    if not original_bytes:
        raise IDAError(f"无法读取地址{target_address}的原始数据")
    
    # 生成补丁ID
    import time
    patch_id = f"patch_{int(time.time())}_{ea:x}"
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    
    try:
        # 应用补丁
        for i, byte_val in enumerate(patch_bytes):
            ida_bytes.patch_byte(ea + i, byte_val)
        
        # 记录补丁信息
        patch_info = MemoryPatch(
            patch_id=patch_id,
            address=hex(ea),
            original_bytes=original_bytes.hex(),
            patched_bytes=patch_data,
            size=patch_size,
            timestamp=timestamp,
            description=description,
            is_active=True
        )
        
        # 存储到补丁历史（使用模块数据存储）
        module_data = lazy_module_manager.get_module_data('memory_patch')
        module_data['patch_history'].append(patch_info)
        
        return patch_info
        
    except Exception as e:
        raise IDAError(f"补丁应用失败: {str(e)}")

@jsonrpc
@lazy_init_module('memory_patch')
@idawrite
def modify_instruction(instruction_address: Annotated[str, "指令地址"], new_instruction: Annotated[str, "新指令"]) -> InstructionMod:
    """修改单条指令"""
    ea = parse_address(instruction_address)
    
    # 获取原始指令
    original_disasm = idc.generate_disasm_line(ea, 0)
    original_size = idc.get_item_size(ea)
    original_bytes = ida_bytes.get_bytes(ea, original_size)
    
    if not original_bytes:
        raise IDAError(f"无法读取地址{instruction_address}的指令")
    
    # 简化的指令修改（实际实现会更复杂）
    modification_type = "unknown"
    risk_level = "medium"
    
    # 检测修改类型
    if "nop" in new_instruction.lower():
        modification_type = "nop_replacement"
        risk_level = "low"
        # NOP指令
        new_bytes = b'\x90' * original_size
    elif "ret" in new_instruction.lower():
        modification_type = "return_injection"
        risk_level = "high"
        # RET指令
        new_bytes = b'\xC3' + b'\x90' * (original_size - 1)
    elif "jmp" in new_instruction.lower():
        modification_type = "jump_modification"
        risk_level = "high"
        # 简单的短跳转
        new_bytes = b'\xEB\x00' + b'\x90' * (original_size - 2)
    else:
        # 默认用NOP填充
        modification_type = "custom_modification"
        risk_level = "high"
        new_bytes = b'\x90' * original_size
    
    try:
        # 应用修改
        for i, byte_val in enumerate(new_bytes):
            ida_bytes.patch_byte(ea + i, byte_val)
        
        return InstructionMod(
            address=hex(ea),
            original_instruction=original_disasm,
            modified_instruction=new_instruction,
            modification_type=modification_type,
            risk_level=risk_level,
            rollback_data=original_bytes.hex()
        )
        
    except Exception as e:
        raise IDAError(f"指令修改失败: {str(e)}")

@jsonrpc
@lazy_init_module('memory_patch')
@idawrite
def hook_function_calls(function_name: Annotated[str, "函数名称"], hook_method: Annotated[str, "Hook方法"]) -> FunctionHook:
    """Hook函数调用"""
    # 查找函数地址
    func_addr = idaapi.get_name_ea(idaapi.BADADDR, function_name)
    if func_addr == idaapi.BADADDR:
        raise IDAError(f"函数{function_name}未找到")
    
    func = idaapi.get_func(func_addr)
    if not func:
        raise IDAError(f"地址{hex(func_addr)}不是有效函数")
    
    # 读取原始入口点
    original_entry_bytes = ida_bytes.get_bytes(func.start_ea, 5)  # 读取5字节用于跳转
    if not original_entry_bytes:
        raise IDAError(f"无法读取函数{function_name}的入口点")
    
    hook_address = hex(func.start_ea)
    status = "pending"
    
    try:
        if hook_method == "entry_redirect":
            # 在函数入口点插入跳转指令（简化实现）
            # 实际应用中需要更复杂的重定向逻辑
            # 这里只是演示概念
            jmp_instruction = b'\xEB\xFE'  # jmp short $-2 (无限循环，防止执行)
            ida_bytes.patch_byte(func.start_ea, jmp_instruction[0])
            ida_bytes.patch_byte(func.start_ea + 1, jmp_instruction[1])
            status = "active"
            
        elif hook_method == "call_interception":
            # 拦截对此函数的调用（需要扫描所有调用点）
            call_count = 0
            for xref in idautils.XrefsTo(func.start_ea):
                if xref.type == ida_xref.fl_CN:  # 代码调用
                    call_count += 1
            status = f"intercepted_{call_count}_calls"
            
        return FunctionHook(
            target_function=function_name,
            hook_address=hook_address,
            hook_type=hook_method,
            original_entry=original_entry_bytes.hex(),
            redirect_target="hook_handler",
            status=status
        )
        
    except Exception as e:
        raise IDAError(f"函数Hook失败: {str(e)}")

@jsonrpc
@lazy_init_module('memory_patch')
@idawrite
def patch_return_values(function_address: Annotated[str, "函数地址"], return_value: Annotated[str, "返回值"]) -> ReturnValuePatch:
    """修改函数返回值"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"地址{function_address}不是有效函数")
    
    # 分析函数的返回指令
    ret_instructions = []
    for addr in range(func.start_ea, func.end_ea):
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            # 使用正确的ida_allins模块中的返回指令常量
            if (insn.itype == ida_allins.NN_retn or insn.itype == ida_allins.NN_retf or
                insn.itype == ida_allins.NN_retnd or insn.itype == ida_allins.NN_retfd or
                insn.itype == ida_allins.NN_retnq or insn.itype == ida_allins.NN_retfq or
                insn.itype == ida_allins.NN_retnw or insn.itype == ida_allins.NN_retfw):
                ret_instructions.append(addr)
    
    if not ret_instructions:
        raise IDAError(f"函数{function_address}中未找到返回指令")
    
    # 确定返回值类型和补丁方法
    original_return_type = "unknown"
    patch_method = "register_modification"
    
    try:
        # 解析返回值
        ret_val = int(return_value, 0)
        
        # 在每个返回指令前插入设置返回值的代码
        success_count = 0
        for ret_addr in ret_instructions:
            # 简化实现：在返回前设置EAX寄存器
            if ret_val == 0:
                # mov eax, 0
                patch_bytes = b'\xB8\x00\x00\x00\x00'
            elif ret_val == 1:
                # mov eax, 1
                patch_bytes = b'\xB8\x01\x00\x00\x00'
            else:
                # mov eax, immediate
                patch_bytes = b'\xB8' + ret_val.to_bytes(4, 'little')
            
            # 检查是否有足够空间（需要5字节）
            if ret_addr - func.start_ea >= 5:
                # 在返回指令前插入（简化实现）
                for i, byte_val in enumerate(patch_bytes):
                    ida_bytes.patch_byte(ret_addr - 5 + i, byte_val)
                success_count += 1
        
        success_rate = f"{success_count}/{len(ret_instructions)}"
        
        return ReturnValuePatch(
            function_address=hex(func.start_ea),
            original_return_type=original_return_type,
            patched_return_value=return_value,
            patch_method=patch_method,
            success_rate=success_rate
        )
        
    except ValueError:
        raise IDAError(f"无效的返回值格式: {return_value}")
    except Exception as e:
        raise IDAError(f"返回值补丁失败: {str(e)}")

@jsonrpc
@lazy_init_module('memory_patch')
@idaread
def manage_patch_history(operation: Annotated[str, "操作类型: list/rollback/clear"]) -> list[PatchHistoryEntry]:
    """管理补丁历史记录"""
    import time
    if operation == "list":
        module_data = lazy_module_manager.get_module_data('memory_patch')
        history = []
        for i, patch in enumerate(module_data['patch_history']):
            history.append(PatchHistoryEntry(operation_id=patch['patch_id'],operation_type="memory_patch",target_address=patch['address'],timestamp=patch['timestamp'],operation_data=f"Size: {patch['size']}, Desc: {patch['description']}",rollback_possible=patch['is_active']))
        return history
    elif operation == "rollback":
        module_data = lazy_module_manager.get_module_data('memory_patch')
        if module_data['patch_history']:
            last_patch = module_data['patch_history'][-1]
            if last_patch['is_active']:
                try:
                    ea = int(last_patch['address'], 16)
                    original_bytes = bytes.fromhex(last_patch['original_bytes'])
                    for i, byte_val in enumerate(original_bytes):
                        ida_bytes.patch_byte(ea + i, byte_val)
                    last_patch['is_active'] = False
                    return [PatchHistoryEntry(operation_id=last_patch['patch_id'],operation_type="rollback",target_address=last_patch['address'],timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),operation_data="Patch rolled back successfully",rollback_possible=False)]
                except Exception as e:
                    raise IDAError(f"回滚失败: {str(e)}")
        return []
    elif operation == "clear":
        module_data = lazy_module_manager.get_module_data('memory_patch')
        module_data['patch_history'].clear()
        return [PatchHistoryEntry(operation_id="clear_operation",operation_type="clear_history",target_address="N/A",timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),operation_data="All patch history cleared",rollback_possible=False)]
    else:
        raise IDAError(f"不支持的操作类型: {operation}")

# 测试延迟初始化框架的函数
@jsonrpc
@lazy_init_module('test_module')
@idaread
def test_lazy_initialization() -> dict[str, Any]:
    """测试延迟初始化框架功能"""
    return {
        "message": "延迟初始化框架测试成功",
        "module_states": lazy_module_manager.module_states,
        "usage_stats": lazy_module_manager.get_usage_stats()
    }

@jsonrpc
@idaread
def get_lazy_module_stats() -> dict[str, Any]:
    """获取延迟初始化模块统计信息"""
    return {
        "initialized_modules": [name for name, state in lazy_module_manager.module_states.items() if state],
        "usage_statistics": lazy_module_manager.get_usage_stats(),
        "total_modules": len(lazy_module_manager.module_states)
    }

@jsonrpc
def get_cache_statistics() -> dict[str, Any]:
    """获取智能缓存系统统计信息"""
    return analysis_cache.get_stats()

@jsonrpc  
def clear_analysis_cache() -> dict[str, str]:
    """清空分析缓存"""
    analysis_cache.clear()
    return {"status": "success", "message": "分析缓存已清空"}

@jsonrpc
def configure_cache_settings(
    max_size: Annotated[int, "缓存最大条目数"],
    max_memory_mb: Annotated[int, "缓存最大内存使用量（MB）"],
    ttl_seconds: Annotated[int, "缓存条目存活时间（秒）"]
) -> dict[str, str]:
    """配置缓存设置"""
    global analysis_cache
    
    # 验证参数
    if max_size <= 0 or max_memory_mb <= 0 or ttl_seconds <= 0:
        raise IDAError("缓存参数必须为正数")
    
    if max_memory_mb > 100:  # 限制最大内存使用
        raise IDAError("缓存内存限制不能超过100MB")
    
    # 重新创建缓存实例
    analysis_cache = AnalysisCache(max_size, max_memory_mb, ttl_seconds)
    
    return {
        "status": "success", 
        "message": f"缓存配置已更新：最大{max_size}条目，{max_memory_mb}MB内存，{ttl_seconds}秒TTL"
    }

# ==================== 字符串分析增强模块 ====================

class EncodedString(TypedDict):
    """编码字符串结果"""
    original: str
    decoded: str
    encoding_type: str
    confidence: float
    address: str

class LicenseString(TypedDict):
    """许可证字符串结果"""
    string: str
    address: str
    category: str
    importance: str
    keywords_found: list[str]

class ErrorMessage(TypedDict):
    """错误消息分析结果"""
    message: str
    address: str
    severity: str
    category: str
    potential_cause: str

class ResourceString(TypedDict):
    """资源字符串结果"""
    string: str
    address: str
    resource_type: str
    context: str
    usage_hint: str

@jsonrpc
@cached_analysis(cache_ttl=1800, cache_size_limit=50)  # 30分钟缓存
@lazy_init_module('string_analysis')
@idaread
def decrypt_encoded_strings(
    min_length: Annotated[int, "最小字符串长度"] = 8,
    max_count: Annotated[int, "最大返回数量"] = 100
) -> list[EncodedString]:
    """解密和识别编码字符串"""
    module_data = lazy_module_manager.get_module_data('string_analysis')
    encoded_strings = []
    
    # 获取所有字符串
    for item in idautils.Strings():
        try:
            original_str = str(item)
            if len(original_str) < min_length:
                continue
                
            # 尝试Base64解码
            if _is_base64_like(original_str, module_data['encoding_patterns']['base64']):
                try:
                    decoded = base64.b64decode(original_str + '==').decode('utf-8', errors='ignore')
                    if decoded and all(ord(c) >= 32 and ord(c) <= 126 for c in decoded[:20]):
                        encoded_strings.append(EncodedString(
                            original=original_str,
                            decoded=decoded,
                            encoding_type="Base64",
                            confidence=0.9,
                            address=hex(item.ea)
                        ))
                        continue
                except (ValueError, UnicodeDecodeError):
                    # 编码解析失败，跳过此字符串
                    continue
            
            # 尝试简单XOR解码
            xor_result = _try_xor_decode(original_str, module_data['common_xor_keys'])
            if xor_result:
                encoded_strings.append(EncodedString(
                    original=original_str,
                    decoded=xor_result['decoded'],
                    encoding_type=f"XOR-{xor_result['key']:02X}",
                    confidence=xor_result['confidence'],
                    address=hex(item.ea)
                ))
                continue
            
            # 尝试ROT13解码
            rot13_decoded = original_str.encode().decode('rot13', errors='ignore')
            if rot13_decoded != original_str and _is_meaningful_text(rot13_decoded):
                encoded_strings.append(EncodedString(
                    original=original_str,
                    decoded=rot13_decoded,
                    encoding_type="ROT13",
                    confidence=0.7,
                    address=hex(item.ea)
                ))
            
        except Exception:
            continue
        
        if len(encoded_strings) >= max_count:
            break
    
    return sorted(encoded_strings, key=lambda x: x['confidence'], reverse=True)

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存
@lazy_init_module('string_analysis')
@idaread  
def extract_license_strings(
    include_trial: Annotated[bool, "包含试用版相关字符串"] = True
) -> list[LicenseString]:
    """提取和分类许可证相关字符串"""
    module_data = lazy_module_manager.get_module_data('string_analysis')
    license_strings = []
    
    keywords = module_data['license_keywords']
    categories = module_data['string_categories']
    
    for item in idautils.Strings():
        try:
            string_content = str(item).lower()
            if len(string_content) < 5:
                continue
            
            # 查找匹配的关键词
            found_keywords = [kw for kw in keywords if kw in string_content]
            if not found_keywords:
                continue
            
            # 确定类别和重要性
            category = "license_general"
            importance = "medium"
            
            if any(kw in string_content for kw in ['serial', 'key', 'activation']):
                category = "license_key"
                importance = "high"
            elif any(kw in string_content for kw in ['trial', 'demo', 'evaluation']):
                category = "trial_version"
                importance = "high" if include_trial else "medium"
            elif any(kw in string_content for kw in ['expired', 'invalid', 'error']):
                category = "license_error"
                importance = "high"
            
            # 根据字符串类别调整重要性
            for category_name, cat_keywords in categories.items():
                if any(kw in string_content for kw in cat_keywords):
                    if category_name == 'high_importance':
                        importance = "high"
                    elif category_name == 'low_importance' and importance != "high":
                        importance = "low"
            
            license_strings.append(LicenseString(
                string=str(item),
                address=hex(item.ea),
                category=category,
                importance=importance,
                keywords_found=found_keywords
            ))
            
        except Exception:
            continue
    
    # 按重要性和地址排序
    importance_order = {"high": 0, "medium": 1, "low": 2}
    return sorted(license_strings, 
                 key=lambda x: (importance_order[x['importance']], x['address']))

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=75)  # 40分钟缓存
@lazy_init_module('string_analysis')
@idaread
def analyze_error_messages(
    severity_filter: Annotated[Optional[str], "严重性过滤器 (critical/high/medium/low)"] = None
) -> list[ErrorMessage]:
    """分析错误消息和异常信息"""
    module_data = lazy_module_manager.get_module_data('string_analysis')
    error_messages = []
    
    error_patterns = module_data['error_patterns']
    
    for item in idautils.Strings():
        try:
            string_content = str(item).lower()
            if len(string_content) < 6:
                continue
            
            # 查找错误模式
            matched_patterns = [pattern for pattern in error_patterns if pattern in string_content]
            if not matched_patterns:
                continue
            
            # 确定严重性
            severity = "medium"
            if any(pattern in string_content for pattern in ['fail', 'error', 'exception', 'corrupt']):
                severity = "high"
            elif any(pattern in string_content for pattern in ['access denied', 'permission', 'timeout']):
                severity = "critical"
            elif any(pattern in string_content for pattern in ['invalid', 'wrong', 'bad']):
                severity = "medium"
            else:
                severity = "low"
            
            # 应用过滤器
            if severity_filter and severity != severity_filter:
                continue
            
            # 确定错误类别和潜在原因
            category = "general_error"
            potential_cause = "程序逻辑错误"
            
            if any(word in string_content for word in ['file', 'not found', 'missing']):
                category = "file_error"
                potential_cause = "文件访问或路径问题"
            elif any(word in string_content for word in ['memory', 'overflow', 'allocation']):
                category = "memory_error"
                potential_cause = "内存管理问题"
            elif any(word in string_content for word in ['network', 'connection', 'timeout']):
                category = "network_error"
                potential_cause = "网络连接问题"
            elif any(word in string_content for word in ['permission', 'access', 'denied']):
                category = "permission_error"
                potential_cause = "权限或安全限制"
            
            error_messages.append(ErrorMessage(
                message=str(item),
                address=hex(item.ea),
                severity=severity,
                category=category,
                potential_cause=potential_cause
            ))
            
        except Exception:
            continue
    
    # 按严重性排序
    severity_order = {"critical": 0, "high": 1, "medium": 2, "low": 3}
    return sorted(error_messages, key=lambda x: severity_order[x['severity']])

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存
@lazy_init_module('string_analysis')
@idaread
def find_resource_strings(
    resource_type: Annotated[Optional[str], "资源类型过滤器"] = None
) -> list[ResourceString]:
    """查找和分类GUI资源字符串"""
    module_data = lazy_module_manager.get_module_data('string_analysis')
    resource_strings = []
    
    resource_indicators = module_data['resource_indicators']
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            string_lower = string_content.lower()
            
            if len(string_content) < 4:
                continue
            
            # 检查是否包含资源指示符
            matched_indicators = [indicator for indicator in resource_indicators 
                                if indicator in string_lower]
            
            # 检查字符串特征
            detected_type = "unknown"
            context = "general"
            usage_hint = "可能用于用户界面显示"
            
            # 识别GUI元素
            if any(word in string_lower for word in ['button', 'menu', 'dialog']):
                detected_type = "gui_element"
                context = "用户界面控件"
                usage_hint = "按钮、菜单或对话框文本"
            elif any(word in string_lower for word in ['error', 'warning', 'info']):
                detected_type = "message"
                context = "消息提示"
                usage_hint = "用户提示或错误消息"
            elif any(word in string_lower for word in ['title', 'caption']):
                detected_type = "title"
                context = "标题文本"
                usage_hint = "窗口或控件标题"
            elif any(word in string_lower for word in ['version', 'copyright', '©']):
                detected_type = "version_info"
                context = "版本信息"
                usage_hint = "程序版本或版权信息"
            elif re.match(r'^[A-Z][a-z]+(\s+[A-Z][a-z]+)*$', string_content):
                detected_type = "label"
                context = "标签文本"
                usage_hint = "界面标签或描述文本"
            elif matched_indicators:
                detected_type = "resource_reference"
                context = "资源引用"
                usage_hint = f"可能与{matched_indicators[0]}相关的资源"
            
            # 应用类型过滤器
            if resource_type and detected_type != resource_type:
                continue
            
            # 只保留可能的资源字符串
            if detected_type != "unknown" or matched_indicators:
                resource_strings.append(ResourceString(
                    string=string_content,
                    address=hex(item.ea),
                    resource_type=detected_type,
                    context=context,
                    usage_hint=usage_hint
                ))
            
        except Exception:
            continue
    
    # 按类型和地址排序
    type_order = {
        "version_info": 0, "title": 1, "gui_element": 2, 
        "message": 3, "label": 4, "resource_reference": 5, "unknown": 6
    }
    return sorted(resource_strings, 
                 key=lambda x: (type_order.get(x['resource_type'], 6), x['address']))

# ==================== 自动化工作流引擎 ====================

class ProtectionType(TypedDict):
    """保护类型检测结果"""
    protection_name: str
    confidence: float
    detected_features: list[str]
    analysis_strategy: str
    priority: str

class AnalysisStrategy(TypedDict):
    """分析策略"""
    strategy_name: str
    target_modules: list[str]
    execution_order: list[str]
    parameters: dict[str, Any]
    estimated_time: str
    success_probability: float

class BatchTask(TypedDict):
    """批处理任务"""
    task_id: str
    task_type: str
    target: str
    status: str
    progress: float
    results: Optional[dict[str, Any]]
    start_time: str
    end_time: Optional[str]

class CrackReport(TypedDict):
    """破解分析报告"""
    report_id: str
    analysis_summary: str
    protection_analysis: dict[str, Any]
    vulnerability_points: list[dict[str, Any]]
    recommended_approach: str
    success_indicators: list[str]
    risk_assessment: str
    generated_time: str

class WorkflowEngine:
    """智能工作流引擎 - 自动化破解分析流程管理"""
    
    def __init__(self):
        self.running_tasks: dict[str, BatchTask] = {}
        self.completed_tasks: list[BatchTask] = []
        self.strategy_cache: dict[str, AnalysisStrategy] = {}
        
    def _generate_task_id(self) -> str:
        """生成唯一任务ID"""
        import time
        return f"task_{int(time.time())}_{len(self.running_tasks)}"
    
    def _analyze_protection_patterns(self, analysis_area: Optional[int] = None) -> list[str]:
        """分析保护模式特征"""
        detected_features = []
        
        if analysis_area:
            start_ea = analysis_area
            end_ea = start_ea + 0x10000  # 64KB范围
        else:
            start_ea = idaapi.get_imagebase()
            end_ea = start_ea + 0x50000  # 320KB范围
        
        # 检测常见保护特征
        protection_signatures = {
            'VirtualProtect': 'dynamic_protection',
            'IsDebuggerPresent': 'anti_debug',
            'GetTickCount': 'timing_check',
            'CheckRemoteDebuggerPresent': 'remote_debug_detection',
            'OutputDebugString': 'debug_output_check',
            '.rsrc': 'resource_protection',
            'GetModuleHandle': 'module_detection',
            'CreateToolhelp32Snapshot': 'process_enumeration'
        }
        
        for signature_str, feature_name in protection_signatures.items():
            # 使用idc.find_text进行字符串搜索，确保字符串编码正确
            try:
                signature_bytes = signature_str.encode('utf-8') if isinstance(signature_str, str) else signature_str
                found_addr = idc.find_text(start_ea, idaapi.SEARCH_DOWN | idaapi.SEARCH_CASE, 0, 0, signature_bytes)
                if found_addr != idaapi.BADADDR:
                    detected_features.append(feature_name)
            except (UnicodeEncodeError, TypeError):
                # 如果字符串编码失败，跳过此签名
                continue
        
        return detected_features
    
    def _calculate_strategy_confidence(self, protection_features: list[str]) -> float:
        """计算策略可信度"""
        if not protection_features:
            return 0.3
        
        confidence_map = {
            'anti_debug': 0.8,
            'dynamic_protection': 0.9,
            'timing_check': 0.6,
            'resource_protection': 0.5,
            'process_enumeration': 0.7
        }
        
        total_confidence = sum(confidence_map.get(feature, 0.4) for feature in protection_features)
        return min(total_confidence / len(protection_features), 0.95)

# 创建全局工作流引擎实例
workflow_engine = WorkflowEngine()

@jsonrpc
@cached_analysis(cache_ttl=1800, cache_size_limit=30)  # 30分钟缓存
@idaread
def detect_protection_type(
    analysis_area: Annotated[Optional[str], "分析区域地址（可选）"] = None
) -> list[ProtectionType]:
    """检测程序保护类型和特征"""
    global workflow_engine
    
    area_ea = None
    if analysis_area:
        area_ea = parse_address(analysis_area)
    
    detected_features = workflow_engine._analyze_protection_patterns(area_ea)
    protection_types = []
    
    # 基于检测到的特征推断保护类型
    if any(feature in detected_features for feature in ['anti_debug', 'timing_check', 'remote_debug_detection']):
        confidence = workflow_engine._calculate_strategy_confidence(['anti_debug', 'timing_check'])
        protection_types.append(ProtectionType(
            protection_name="反调试保护",
            confidence=confidence,
            detected_features=[f for f in detected_features if 'debug' in f or 'timing' in f],
            analysis_strategy="anti_debug_bypass",
            priority="high"
        ))
    
    if 'dynamic_protection' in detected_features:
        protection_types.append(ProtectionType(
            protection_name="动态代码保护",
            confidence=0.85,
            detected_features=['dynamic_protection'],
            analysis_strategy="memory_analysis", 
            priority="high"
        ))
    
    if 'resource_protection' in detected_features:
        protection_types.append(ProtectionType(
            protection_name="资源加密保护",
            confidence=0.6,
            detected_features=['resource_protection'],
            analysis_strategy="resource_extraction",
            priority="medium"
        ))
    
    if 'process_enumeration' in detected_features:
        protection_types.append(ProtectionType(
            protection_name="进程检测保护",
            confidence=0.7,
            detected_features=['process_enumeration'],
            analysis_strategy="process_hiding",
            priority="medium"
        ))
    
    # 如果没有检测到明显保护，提供通用分析策略
    if not protection_types:
        protection_types.append(ProtectionType(
            protection_name="轻度保护或无保护",
            confidence=0.4,
            detected_features=detected_features if detected_features else ["basic_analysis"],
            analysis_strategy="standard_analysis",
            priority="low"
        ))
    
    return sorted(protection_types, key=lambda x: x['confidence'], reverse=True)

@jsonrpc
@idaread
def generate_analysis_strategy(
    protection_info: Annotated[str, "保护信息JSON字符串"]
) -> AnalysisStrategy:
    """根据保护类型生成分析策略"""
    global workflow_engine
    
    try:
        protection_data = json.loads(protection_info)
        protection_name = protection_data.get('protection_name', 'unknown')
        detected_features = protection_data.get('detected_features', [])
    except (json.JSONDecodeError, KeyError):
        protection_name = 'unknown'
        detected_features = []
    
    # 检查策略缓存
    cache_key = f"{protection_name}_{hash(tuple(detected_features))}"
    if cache_key in workflow_engine.strategy_cache:
        return workflow_engine.strategy_cache[cache_key]
    
    # 根据保护类型生成策略
    if "反调试保护" in protection_name:
        strategy = AnalysisStrategy(
            strategy_name="反调试绕过分析流程",
            target_modules=["anti_debug", "control_flow", "memory_patch"],
            execution_order=[
                "检测反调试技术",
                "生成绕过策略", 
                "应用内存补丁",
                "验证绕过效果"
            ],
            parameters={
                "detection_depth": "comprehensive",
                "bypass_method": "intelligent_patching",
                "verification_mode": "runtime_testing"
            },
            estimated_time="15-30分钟",
            success_probability=0.85
        )
    
    elif "动态代码保护" in protection_name:
        strategy = AnalysisStrategy(
            strategy_name="动态代码分析流程",
            target_modules=["crypto", "memory_patch", "control_flow"],
            execution_order=[
                "识别加密算法",
                "追踪解密流程",
                "定位密钥位置",
                "分析控制流",
                "应用解密补丁"
            ],
            parameters={
                "crypto_analysis": "deep_scan",
                "memory_tracing": "enabled",
                "flow_analysis": "comprehensive"
            },
            estimated_time="30-60分钟", 
            success_probability=0.7
        )
    
    elif "资源加密保护" in protection_name:
        strategy = AnalysisStrategy(
            strategy_name="资源解密分析流程",
            target_modules=["string_analysis", "crypto", "license"],
            execution_order=[
                "扫描加密资源",
                "分析解密算法",
                "提取资源数据",
                "重构原始资源"
            ],
            parameters={
                "resource_scanning": "full_binary",
                "decryption_mode": "automatic",
                "extraction_format": "original"
            },
            estimated_time="20-40分钟",
            success_probability=0.75
        )
    
    else:
        # 默认通用策略
        strategy = AnalysisStrategy(
            strategy_name="标准逆向分析流程",
            target_modules=["control_flow", "string_analysis", "license"],
            execution_order=[
                "分析程序结构",
                "识别关键函数",
                "分析字符串信息",
                "检查许可证逻辑"
            ],
            parameters={
                "analysis_depth": "standard",
                "function_detection": "automatic",
                "string_analysis": "comprehensive"
            },
            estimated_time="10-20分钟",
            success_probability=0.9
        )
    
    # 缓存策略
    workflow_engine.strategy_cache[cache_key] = strategy
    return strategy

@jsonrpc
@idaread  
def execute_batch_analysis(
    task_list: Annotated[str, "任务列表JSON字符串"],
    execution_mode: Annotated[str, "执行模式：sequential/parallel"] = "sequential"
) -> list[BatchTask]:
    """执行批量分析任务"""
    global workflow_engine
    import time
    import json
    
    try:
        tasks_data = json.loads(task_list)
        if not isinstance(tasks_data, list):
            raise ValueError("任务列表必须是数组格式")
    except (json.JSONDecodeError, ValueError) as e:
        raise IDAError(f"任务列表格式错误: {str(e)}")
    
    batch_tasks = []
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    for task_data in tasks_data:
        task_id = workflow_engine._generate_task_id()
        task_type = task_data.get('type', 'unknown')
        target = task_data.get('target', 'unknown')
        
        # 创建批处理任务
        batch_task = BatchTask(
            task_id=task_id,
            task_type=task_type,
            target=target,
            status="queued",
            progress=0.0,
            results=None,
            start_time=current_time,
            end_time=None
        )
        
        # 执行实际的分析任务
        try:
            batch_task['status'] = "running"
            batch_task['progress'] = 0.3
            
            # 根据任务类型执行相应的分析
            if task_type == "protection_detection":
                results = detect_protection_type(target if target != 'unknown' else None)
                batch_task['results'] = {"protection_types": results}
                
            elif task_type == "anti_debug_scan":
                results = detect_anti_debug_techniques(target if target != 'unknown' else None)
                batch_task['results'] = {"anti_debug_detections": results}
                
            elif task_type == "string_analysis":
                encoded_results = decrypt_encoded_strings()
                license_results = extract_license_strings()
                batch_task['results'] = {
                    "encoded_strings": encoded_results[:5],  # 限制返回数量
                    "license_strings": license_results[:5]
                }
                
            elif task_type == "crypto_analysis":
                crypto_results = identify_crypto_algorithms(target if target != 'unknown' else None)
                batch_task['results'] = {"crypto_algorithms": crypto_results}
                
            else:
                # 默认执行基本分析
                batch_task['results'] = {"message": f"已完成{task_type}类型的基本分析"}
            
            batch_task['status'] = "completed"
            batch_task['progress'] = 1.0
            batch_task['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
            
        except Exception as e:
            batch_task['status'] = "failed"
            batch_task['results'] = {"error": str(e)}
            batch_task['end_time'] = time.strftime("%Y-%m-%d %H:%M:%S")
        
        # 将任务添加到列表
        batch_tasks.append(batch_task)
        workflow_engine.running_tasks[task_id] = batch_task
        
        # sequential模式下任务按顺序执行，无需人工延迟
        # 真实分析操作本身就有自然的执行时间
    
    # 将完成的任务移到完成列表
    for task in batch_tasks:
        if task['status'] in ['completed', 'failed']:
            workflow_engine.completed_tasks.append(task)
            workflow_engine.running_tasks.pop(task['task_id'], None)
    
    return batch_tasks

@jsonrpc
@idaread
def generate_crack_report(
    analysis_results: Annotated[str, "分析结果JSON字符串"],
    report_type: Annotated[str, "报告类型：summary/detailed"] = "summary"
) -> CrackReport:
    """生成破解分析报告"""
    import time
    import json
    
    try:
        results_data = json.loads(analysis_results)
    except json.JSONDecodeError:
        raise IDAError("分析结果格式错误")
    
    report_id = f"report_{int(time.time())}"
    generated_time = time.strftime("%Y-%m-%d %H:%M:%S")
    
    # 汇总分析结果
    protection_analysis = {}
    vulnerability_points = []
    
    # 处理保护类型检测结果
    if 'protection_types' in results_data:
        protection_analysis['detected_protections'] = results_data['protection_types']
        high_confidence_protections = [
            p for p in results_data['protection_types'] 
            if p.get('confidence', 0) > 0.7
        ]
        protection_analysis['high_confidence_count'] = len(high_confidence_protections)
    
    # 处理反调试检测结果
    if 'anti_debug_detections' in results_data:
        anti_debug_data = results_data['anti_debug_detections']
        for detection in anti_debug_data:
            vulnerability_points.append({
                "type": "反调试检测点",
                "address": detection.get('address', 'unknown'),
                "technique": detection.get('technique', 'unknown'),
                "bypass_suggestion": detection.get('bypass_suggestion', '需要进一步分析'),
                "risk_level": detection.get('risk_level', 'medium')
            })
    
    # 处理加密算法结果
    if 'crypto_algorithms' in results_data:
        crypto_data = results_data['crypto_algorithms']
        protection_analysis['crypto_algorithms'] = len(crypto_data)
        for crypto in crypto_data:
            vulnerability_points.append({
                "type": "加密算法",
                "address": crypto.get('address', 'unknown'),
                "algorithm": crypto.get('algorithm', 'unknown'),
                "confidence": crypto.get('confidence', 0),
                "risk_level": "high" if crypto.get('confidence', 0) > 0.8 else "medium"
            })
    
    # 生成分析摘要
    total_vulnerabilities = len(vulnerability_points)
    high_risk_count = len([v for v in vulnerability_points if v.get('risk_level') == 'high'])
    
    if report_type == "detailed":
        analysis_summary = f"""
详细破解分析报告
=================

检测到 {total_vulnerabilities} 个分析点，其中 {high_risk_count} 个高风险点。

保护机制分析：
- 检测到的保护类型：{protection_analysis.get('detected_protections', [])}
- 高可信度保护：{protection_analysis.get('high_confidence_count', 0)} 个
- 发现的加密算法：{protection_analysis.get('crypto_algorithms', 0)} 个

漏洞点详情：
{chr(10).join([f"- {v['type']} at {v['address']}: {v.get('technique', 'N/A')}" for v in vulnerability_points[:10]])}
        """.strip()
    else:
        analysis_summary = f"检测到 {total_vulnerabilities} 个分析点，{high_risk_count} 个高风险。保护类型：{protection_analysis.get('high_confidence_count', 0)} 个高可信度保护。"
    
    # 生成推荐方法
    if high_risk_count > 5:
        recommended_approach = "建议采用动态分析结合静态补丁的综合方法，优先处理高风险点"
    elif high_risk_count > 0:
        recommended_approach = "可采用静态分析为主的方法，重点关注已识别的高风险点"
    else:
        recommended_approach = "程序保护较弱，可直接进行静态分析和简单补丁"
    
    # 生成成功指标
    success_indicators = [
        "成功绕过反调试检测",
        "正确识别并处理加密算法",
        "定位关键验证函数",
        "成功修改程序行为"
    ]
    
    # 风险评估
    if high_risk_count > 8:
        risk_assessment = "高风险：保护机制复杂，需要高级破解技术"
    elif high_risk_count > 3:
        risk_assessment = "中等风险：有一定保护，需要仔细分析"
    else:
        risk_assessment = "低风险：保护较弱，破解难度不大"
    
    return CrackReport(
        report_id=report_id,
        analysis_summary=analysis_summary,
        protection_analysis=protection_analysis,
        vulnerability_points=vulnerability_points[:15],  # 限制返回数量
        recommended_approach=recommended_approach,
        success_indicators=success_indicators,
        risk_assessment=risk_assessment,
        generated_time=generated_time
    )

@jsonrpc
@idaread
def get_workflow_status() -> dict[str, Any]:
    """获取工作流引擎状态 - 增强版包含动态分析功能"""
    global workflow_engine

    # 基础工作流状态
    base_status = {
        "running_tasks": len(workflow_engine.running_tasks),
        "completed_tasks": len(workflow_engine.completed_tasks),
        "cached_strategies": len(workflow_engine.strategy_cache),
        "task_details": {
            "running": list(workflow_engine.running_tasks.keys()),
            "completed_recent": [
                {"id": task['task_id'], "type": task['task_type'], "status": task['status']}
                for task in workflow_engine.completed_tasks[-5:]  # 最近5个完成的任务
            ]
        }
    }

    # 新增：动态分析功能状态
    dynamic_analysis_status = {
        "debugger_available": ida_dbg.is_debugger_on(),
        "process_state": "attached" if ida_dbg.get_process_state() != ida_dbg.DSTATE_NOTASK else "not_attached",
        "enhanced_features": {
            "dynamic_analysis": "available",
            "breakpoint_management": "available",
            "memory_monitoring": "available",
            "api_tracing": "available",
            "workflow_automation": "available",
            "performance_optimization": "available"
        },
        "version": "2025-01-04-enhanced",
        "new_features_loaded": True,
        "total_new_functions": 20
    }

    # 合并状态信息
    base_status["dynamic_analysis"] = dynamic_analysis_status

    return base_status

# 辅助函数
def _is_base64_like(s: str, base64_chars: str) -> bool:
    """检查字符串是否像Base64编码"""
    if len(s) < 8 or len(s) % 4 != 0:
        return False
    return all(c in base64_chars for c in s)

def _try_xor_decode(s: str, xor_keys: list[int]) -> Optional[dict]:
    """尝试XOR解码"""
    for key in xor_keys:
        try:
            decoded_bytes = bytes(ord(c) ^ key for c in s[:50])  # 只测试前50字符
            decoded = decoded_bytes.decode('utf-8', errors='ignore')
            if decoded and _is_meaningful_text(decoded):
                return {'decoded': decoded, 'key': key, 'confidence': 0.8}
        except (ValueError, UnicodeDecodeError):
            continue
    return None

def _is_meaningful_text(s: str) -> bool:
    """检查文本是否有意义"""
    if len(s) < 4:
        return False
    
    # 检查可打印字符比例
    printable_ratio = sum(1 for c in s if c.isprintable()) / len(s)
    if printable_ratio < 0.8:
        return False
    
    # 检查是否包含常见英文单词
    common_words = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use']
    s_lower = s.lower()
    word_matches = sum(1 for word in common_words if word in s_lower)
    
    return word_matches > 0 or len(s) > 20  # 有常见单词或较长字符串

# ==================== Web应用逆向分析模块 ====================

class JavaScriptPattern(TypedDict):
    """JavaScript模式匹配结果"""
    pattern_type: str
    location: str
    code_snippet: str
    security_level: str
    description: str
    exploitation_hint: str

class APIEndpoint(TypedDict):
    """API端点发现结果"""
    endpoint_url: str
    method: str
    parameters: list[str]
    location: str
    authentication_required: bool
    potential_vulnerability: str

class WebResource(TypedDict):
    """Web资源分析结果"""
    resource_type: str
    location: str
    size_bytes: int
    encoding: str
    content_preview: str
    security_impact: str

class ClientSideValidation(TypedDict):
    """客户端验证分析结果"""
    validation_type: str
    location: str
    validation_logic: str
    bypass_difficulty: str
    bypass_suggestion: str

class WebCryptoPattern(TypedDict):
    """Web加密模式结果"""
    crypto_type: str
    implementation: str
    location: str
    strength_assessment: str
    weakness_description: str
    attack_vector: str

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=40)
@idaread
def analyze_javascript_patterns() -> list[JavaScriptPattern]:
    """分析JavaScript代码模式和安全问题"""
    patterns = []
    
    # JavaScript相关的字符串模式
    js_security_patterns = {
        'eval': ('代码注入风险', 'high', '避免使用eval，可能导致代码注入'),
        'document.write': ('XSS风险', 'medium', '可能存在XSS漏洞，检查输入过滤'),
        'innerHTML': ('DOM操作风险', 'medium', '直接DOM操作可能导致XSS'),
        'setTimeout': ('代码执行风险', 'medium', '动态代码执行，检查参数来源'),
        'setInterval': ('代码执行风险', 'medium', '周期性代码执行，检查参数'),
        'new Function': ('动态函数风险', 'high', '动态函数创建，可能被恶意利用'),
        'localStorage': ('数据存储', 'low', '本地存储数据，检查敏感信息'),
        'sessionStorage': ('会话存储', 'low', '会话数据存储，注意数据泄露'),
        'XMLHttpRequest': ('AJAX请求', 'medium', '异步请求，检查端点安全'),
        'fetch': ('现代请求', 'medium', 'Fetch API请求，验证目标安全'),
        'postMessage': ('跨窗口通信', 'high', '跨域通信，可能存在安全风险'),
        'btoa': ('Base64编码', 'low', 'Base64编码，可能用于数据隐藏'),
        'atob': ('Base64解码', 'low', 'Base64解码，检查解码数据'),
        'crypto.subtle': ('Web Crypto API', 'medium', '加密操作，分析加密实现'),
        'WebAssembly': ('WASM', 'high', 'WebAssembly代码，需要专门分析')
    }
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            string_lower = string_content.lower()
            
            # 检查JavaScript模式
            for pattern, (desc, level, hint) in js_security_patterns.items():
                if pattern.lower() in string_lower:
                    # 提取代码片段上下文
                    snippet = string_content
                    if len(snippet) > 100:
                        snippet = snippet[:97] + "..."
                    
                    patterns.append(JavaScriptPattern(
                        pattern_type=pattern,
                        location=hex(item.ea),
                        code_snippet=snippet,
                        security_level=level,
                        description=desc,
                        exploitation_hint=hint
                    ))
                    break  # 每个字符串只匹配一个模式
                    
        except Exception:
            continue
    
    # 按安全级别排序
    level_order = {"high": 0, "medium": 1, "low": 2}
    return sorted(patterns, key=lambda x: level_order[x['security_level']])[:15]

@jsonrpc
@cached_analysis(cache_ttl=1800, cache_size_limit=30)
@idaread
def discover_api_endpoints() -> list[APIEndpoint]:
    """发现API端点和网络请求"""
    endpoints = []
    
    # API端点模式
    api_patterns = [
        (r'/api/', 'REST API'),
        (r'/v[0-9]+/', '版本化API'),
        (r'\.json', 'JSON端点'),
        (r'\.xml', 'XML端点'),
        (r'/graphql', 'GraphQL端点'),
        (r'/oauth/', 'OAuth端点'),
        (r'/auth/', '认证端点'),
        (r'/login', '登录端点'),
        (r'/user/', '用户API'),
        (r'/admin/', '管理API')
    ]
    
    # HTTP方法模式
    http_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            
            # 检查URL模式
            import re
            url_pattern = re.compile(r'https?://[^\s<>"\']+|/[a-zA-Z0-9/_.-]+')
            urls = url_pattern.findall(string_content)
            
            for url in urls:
                if len(url) < 5:  # 过滤太短的匹配
                    continue
                
                # 分析URL特征
                endpoint_type = "未知端点"
                for pattern, desc in api_patterns:
                    if re.search(pattern, url, re.IGNORECASE):
                        endpoint_type = desc
                        break
                
                # 检查是否需要认证
                auth_required = any(auth_indicator in url.lower() 
                                  for auth_indicator in ['auth', 'login', 'token', 'admin'])
                
                # 提取可能的参数
                param_pattern = re.compile(r'[?&]([^=&]+)=')
                parameters = param_pattern.findall(url)
                
                # 评估潜在漏洞
                vulnerability = "无明显漏洞"
                if 'admin' in url.lower():
                    vulnerability = "管理接口暴露"
                elif len(parameters) > 3:
                    vulnerability = "参数较多，可能存在注入风险"
                elif not auth_required and '/user/' in url.lower():
                    vulnerability = "用户接口无认证"
                
                endpoints.append(APIEndpoint(
                    endpoint_url=url,
                    method="GET",  # 默认方法，实际中需要更复杂的分析
                    parameters=parameters,
                    location=hex(item.ea),
                    authentication_required=auth_required,
                    potential_vulnerability=vulnerability
                ))
                
            # 检查HTTP方法
            for method in http_methods:
                if method in string_content:
                    # 尝试在附近找到URL
                    context_start = max(0, string_content.find(method) - 50)
                    context_end = min(len(string_content), string_content.find(method) + 100)
                    context = string_content[context_start:context_end]
                    
                    # 在上下文中查找URL
                    context_urls = url_pattern.findall(context)
                    for url in context_urls:
                        endpoints.append(APIEndpoint(
                            endpoint_url=url,
                            method=method,
                            parameters=[],
                            location=hex(item.ea),
                            authentication_required=False,
                            potential_vulnerability="需要进一步分析"
                        ))
                        
        except Exception:
            continue
    
    # 去重并限制返回数量
    seen_urls = set()
    unique_endpoints = []
    for endpoint in endpoints:
        if endpoint['endpoint_url'] not in seen_urls:
            seen_urls.add(endpoint['endpoint_url'])
            unique_endpoints.append(endpoint)
            if len(unique_endpoints) >= 12:
                break
    
    return unique_endpoints

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=50)
@idaread
def extract_web_resources() -> list[WebResource]:
    """提取Web资源文件分析"""
    resources = []
    
    # Web资源文件扩展名和类型
    resource_types = {
        '.js': 'JavaScript',
        '.css': 'CSS样式表',
        '.html': 'HTML文档',
        '.htm': 'HTML文档',
        '.json': 'JSON数据',
        '.xml': 'XML数据',
        '.svg': 'SVG图像',
        '.png': 'PNG图像',
        '.jpg': 'JPEG图像',
        '.gif': 'GIF图像',
        '.ico': '图标文件',
        '.woff': 'Web字体',
        '.ttf': 'TrueType字体',
        '.eot': 'EOT字体'
    }
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            
            # 检查文件路径模式
            import re
            file_pattern = re.compile(r'[^\s<>"\']+\.([a-zA-Z0-9]+)')
            matches = file_pattern.findall(string_content)
            
            for match in matches:
                ext = f'.{match.lower()}'
                if ext in resource_types:
                    # 重新构建完整路径
                    full_match = file_pattern.search(string_content)
                    if full_match:
                        file_path = full_match.group(0)
                        
                        # 估算文件大小（基于字符串长度的启发式方法）
                        estimated_size = len(file_path) * 10  # 简单估算
                        
                        # 检查编码
                        encoding = "UTF-8"  # 默认编码
                        if any(char in string_content for char in ['charset', 'encoding']):
                            encoding = "可能非UTF-8"
                        
                        # 生成内容预览
                        preview = string_content[:80] + "..." if len(string_content) > 80 else string_content
                        
                        # 评估安全影响
                        security_impact = "低风险"
                        if ext == '.js':
                            security_impact = "中风险 - 可能包含敏感逻辑"
                        elif ext in ['.json', '.xml']:
                            security_impact = "中风险 - 可能包含配置信息"
                        elif ext == '.html':
                            security_impact = "低风险 - 界面文件"
                        
                        resources.append(WebResource(
                            resource_type=resource_types[ext],
                            location=hex(item.ea),
                            size_bytes=estimated_size,
                            encoding=encoding,
                            content_preview=preview,
                            security_impact=security_impact
                        ))
                        
        except Exception:
            continue
    
    # 按类型和大小排序
    type_priority = {"JavaScript": 0, "JSON数据": 1, "HTML文档": 2, "CSS样式表": 3}
    return sorted(resources, 
                 key=lambda x: (type_priority.get(x['resource_type'], 99), -x['size_bytes']))[:10]

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=35)
@idaread
def analyze_client_side_validation() -> list[ClientSideValidation]:
    """分析客户端验证逻辑"""
    validations = []
    
    # 客户端验证模式
    validation_patterns = {
        'required': ('必填验证', 'easy', '在浏览器中禁用JavaScript'),
        'pattern': ('正则验证', 'medium', '分析正则表达式，构造绕过数据'),
        'minlength': ('长度验证', 'easy', '修改HTML属性或禁用JS'),
        'maxlength': ('长度限制', 'easy', '绕过前端长度检查'),
        'email': ('邮箱验证', 'medium', '构造符合格式但异常的邮箱'),
        'number': ('数值验证', 'easy', '在请求中直接发送非数值'),
        'range': ('范围验证', 'medium', '发送超出范围的值'),
        'validate': ('通用验证', 'medium', '分析验证函数实现'),
        'check': ('检查函数', 'medium', '逆向检查逻辑'),
        'confirm': ('确认验证', 'easy', '跳过确认步骤'),
        'captcha': ('验证码', 'hard', '需要OCR或绕过验证码接口'),
        'csrf': ('CSRF保护', 'hard', '获取或伪造CSRF令牌')
    }
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            string_lower = string_content.lower()
            
            # 检查验证模式
            for pattern, (val_type, difficulty, suggestion) in validation_patterns.items():
                if pattern in string_lower:
                    # 提取验证逻辑上下文
                    logic_context = string_content
                    if len(logic_context) > 150:
                        # 尝试提取关键部分
                        pattern_pos = string_lower.find(pattern)
                        start = max(0, pattern_pos - 50)
                        end = min(len(string_content), pattern_pos + 100)
                        logic_context = string_content[start:end]
                    
                    validations.append(ClientSideValidation(
                        validation_type=val_type,
                        location=hex(item.ea),
                        validation_logic=logic_context,
                        bypass_difficulty=difficulty,
                        bypass_suggestion=suggestion
                    ))
                    break  # 每个字符串只匹配一个验证类型
                    
        except Exception:
            continue
    
    # 按绕过难度排序（简单的优先）
    difficulty_order = {"easy": 0, "medium": 1, "hard": 2}
    return sorted(validations, key=lambda x: difficulty_order[x['bypass_difficulty']])[:12]

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=25)
@idaread
def identify_web_crypto_patterns() -> list[WebCryptoPattern]:
    """识别Web加密实现模式"""
    crypto_patterns = []
    
    # Web加密相关模式
    web_crypto_indicators = {
        'CryptoJS': ('JavaScript加密库', 'library', '第三方加密库，可能有已知漏洞'),
        'forge': ('Forge加密库', 'library', '检查版本是否有安全更新'),
        'bcrypt': ('密码哈希', 'hashing', 'bcrypt算法，相对安全但检查实现'),
        'md5': ('MD5哈希', 'weak_hash', 'MD5不安全，容易碰撞攻击'),
        'sha1': ('SHA1哈希', 'weak_hash', 'SHA1已被破解，建议升级'),
        'sha256': ('SHA256哈希', 'strong_hash', '安全的哈希算法'),
        'aes': ('AES加密', 'symmetric', '检查密钥管理和模式'),
        'rsa': ('RSA加密', 'asymmetric', '检查密钥长度和填充'),
        'ecdsa': ('椭圆曲线签名', 'signature', '检查曲线参数'),
        'hmac': ('消息认证码', 'mac', '检查密钥安全性'),
        'pbkdf2': ('密钥派生', 'kdf', '检查迭代次数和盐值'),
        'scrypt': ('Scrypt密钥派生', 'kdf', '内存困难的KDF'),
        'jwt': ('JSON Web Token', 'token', '检查签名算法和密钥'),
        'oauth': ('OAuth实现', 'auth', '检查OAuth流程安全'),
        'crypto.subtle': ('Web Crypto API', 'native', '浏览器原生加密API'),
        'sodium': ('libsodium', 'library', '现代加密库')
    }
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            string_lower = string_content.lower()
            
            # 检查加密模式
            for pattern, (crypto_type, impl_type, weakness) in web_crypto_indicators.items():
                if pattern in string_lower:
                    # 评估强度
                    if impl_type in ['weak_hash', 'deprecated']:
                        strength = "弱"
                        attack_vector = "哈希碰撞或暴力破解"
                    elif impl_type in ['library']:
                        strength = "取决于版本"
                        attack_vector = "检查已知CVE漏洞"
                    elif impl_type in ['strong_hash', 'native']:
                        strength = "强"
                        attack_vector = "实现漏洞或密钥泄露"
                    else:
                        strength = "中等"
                        attack_vector = "配置错误或实现缺陷"
                    
                    crypto_patterns.append(WebCryptoPattern(
                        crypto_type=crypto_type,
                        implementation=impl_type,
                        location=hex(item.ea),
                        strength_assessment=strength,
                        weakness_description=weakness,
                        attack_vector=attack_vector
                    ))
                    break
                    
        except Exception:
            continue
    
    # 按强度排序（弱的优先，风险更高）
    strength_order = {"弱": 0, "中等": 1, "取决于版本": 2, "强": 3}
    return sorted(crypto_patterns, 
                 key=lambda x: strength_order[x['strength_assessment']])[:10]

@jsonrpc
@idaread
def scan_web_vulnerabilities() -> dict[str, Any]:
    """综合Web漏洞扫描"""
    # 调用各个分析函数并汇总结果
    js_patterns = analyze_javascript_patterns()
    api_endpoints = discover_api_endpoints()
    validations = analyze_client_side_validation()
    crypto_patterns = identify_web_crypto_patterns()
    
    # 统计高风险项
    high_risk_js = [p for p in js_patterns if p['security_level'] == 'high']
    vulnerable_apis = [a for a in api_endpoints if a['potential_vulnerability'] != '无明显漏洞']
    weak_crypto = [c for c in crypto_patterns if c['strength_assessment'] in ['弱', '中等']]
    easy_bypass = [v for v in validations if v['bypass_difficulty'] == 'easy']
    
    return {
        "summary": {
            "total_js_patterns": len(js_patterns),
            "high_risk_js": len(high_risk_js),
            "api_endpoints": len(api_endpoints),
            "vulnerable_apis": len(vulnerable_apis),
            "validation_points": len(validations),
            "easy_bypass": len(easy_bypass),
            "crypto_implementations": len(crypto_patterns),
            "weak_crypto": len(weak_crypto)
        },
        "high_priority_issues": {
            "javascript_risks": high_risk_js[:5],
            "vulnerable_endpoints": vulnerable_apis[:5],
            "weak_crypto": weak_crypto[:3],
            "bypassable_validations": easy_bypass[:5]
        },
        "recommendations": [
            "禁用或安全实现eval()等危险JavaScript函数" if high_risk_js else None,
            "加强API端点的认证和授权检查" if vulnerable_apis else None,
            "升级弱加密算法到安全版本" if weak_crypto else None,
            "将客户端验证移至服务端" if easy_bypass else None
        ]
    }

# ==================== 智能破解策略生成器 ====================

class CrackStrategy(TypedDict):
    """破解策略"""
    strategy_id: str
    target_type: str
    difficulty_level: str
    success_probability: float
    attack_vector: str
    required_tools: list[str]
    execution_steps: list[str]
    estimated_time: str
    risk_level: str

class AdvancedBypass(TypedDict):
    """高级绕过技术"""
    bypass_name: str
    target_protection: str
    complexity: str
    implementation_code: str
    success_indicators: list[str]
    failure_recovery: str

class ExploitChain(TypedDict):
    """漏洞利用链"""
    chain_id: str
    vulnerability_points: list[str]
    exploitation_order: list[str]
    payload_templates: dict[str, str]
    verification_steps: list[str]
    cleanup_required: bool

class IntelligentPatch(TypedDict):
    """智能补丁方案"""
    patch_id: str
    target_addresses: list[str]
    patch_strategy: str
    backup_plan: str
    verification_method: str
    rollback_procedure: str

@jsonrpc
@cached_analysis(cache_ttl=1800, cache_size_limit=20)
@idaread
def generate_crack_strategies(target_analysis: Annotated[str, "目标分析数据（JSON字符串）"]) -> list[CrackStrategy]:
    """生成智能破解策略"""
    import time
    import json
    strategies = []

    # 处理参数类型兼容性
    if isinstance(target_analysis, str):
        try:
            target_analysis = json.loads(target_analysis)
        except json.JSONDecodeError:
            target_analysis = {"protection_types": ["unknown"], "crypto_algorithms": [], "license_strings": []}
    elif not isinstance(target_analysis, dict):
        target_analysis = {"protection_types": ["unknown"], "crypto_algorithms": [], "license_strings": []}

    # 分析目标特征
    protection_count = len(target_analysis.get('protection_types', []))
    has_anti_debug = any('debug' in str(p).lower() for p in target_analysis.get('protection_types', []))
    has_crypto = len(target_analysis.get('crypto_algorithms', [])) > 0
    has_license = len(target_analysis.get('license_strings', [])) > 0
    
    # 策略1：直接内存补丁策略
    if protection_count <= 2:
        strategies.append(CrackStrategy(
            strategy_id=f"direct_patch_{int(time.time())}",
            target_type="轻保护程序",
            difficulty_level="简单",
            success_probability=0.85,
            attack_vector="直接内存修改",
            required_tools=["内存编辑器", "静态分析工具"],
            execution_steps=[
                "定位关键验证函数",
                "分析验证逻辑",
                "应用NOP补丁绕过验证",
                "测试补丁效果",
                "保存修改后的程序"
            ],
            estimated_time="15-30分钟",
            risk_level="低"
        ))
    
    # 策略2：反调试绕过策略
    if has_anti_debug:
        strategies.append(CrackStrategy(
            strategy_id=f"anti_debug_bypass_{int(time.time())}",
            target_type="反调试保护",
            difficulty_level="中等",
            success_probability=0.75,
            attack_vector="反调试检测绕过",
            required_tools=["调试器", "API Hook工具", "内存补丁工具"],
            execution_steps=[
                "识别反调试检测点",
                "Hook或补丁反调试API",
                "修改时间检查逻辑",
                "测试调试器正常工作",
                "继续后续分析"
            ],
            estimated_time="30-60分钟",
            risk_level="中等"
        ))
    
    # 策略3：加密算法破解策略
    if has_crypto:
        strategies.append(CrackStrategy(
            strategy_id=f"crypto_analysis_{int(time.time())}",
            target_type="加密保护",
            difficulty_level="困难",
            success_probability=0.6,
            attack_vector="加密算法分析",
            required_tools=["密码分析工具", "动态调试器", "内存转储工具"],
            execution_steps=[
                "识别加密算法类型",
                "定位密钥存储位置",
                "分析加密/解密流程",
                "提取或推导密钥",
                "实现解密程序"
            ],
            estimated_time="1-3小时",
            risk_level="高"
        ))
    
    # 策略4：许可证机制绕过策略
    if has_license:
        strategies.append(CrackStrategy(
            strategy_id=f"license_bypass_{int(time.time())}",
            target_type="许可证验证",
            difficulty_level="中等",
            success_probability=0.8,
            attack_vector="许可证验证绕过",
            required_tools=["逆向工程工具", "注册机生成器", "验证逻辑分析"],
            execution_steps=[
                "定位许可证验证函数",
                "分析验证算法",
                "识别有效许可证格式",
                "生成有效许可证或绕过验证",
                "测试绕过效果"
            ],
            estimated_time="45-90分钟",
            risk_level="中等"
        ))
    
    # 策略5：综合攻击策略（复杂保护）
    if protection_count > 3:
        strategies.append(CrackStrategy(
            strategy_id=f"comprehensive_attack_{int(time.time())}",
            target_type="多重保护",
            difficulty_level="专家级",
            success_probability=0.45,
            attack_vector="多阶段综合攻击",
            required_tools=["全套逆向工具", "自动化脚本", "虚拟机环境"],
            execution_steps=[
                "建立安全的分析环境",
                "逐层剥离保护机制",
                "自动化漏洞检测",
                "构建完整的攻击链",
                "验证并优化攻击效果"
            ],
            estimated_time="2-6小时",
            risk_level="高"
        ))
    
    return strategies

@jsonrpc
@idaread
def create_advanced_bypass(protection_type: Annotated[str, "保护类型"], target_address: Annotated[str, "目标地址"]) -> AdvancedBypass:
    """创建高级绕过技术"""
    ea = parse_address(target_address)
    
    # 根据保护类型生成相应的绕过技术
    if protection_type.lower() == "anti_debug":
        return AdvancedBypass(
            bypass_name="高级反调试绕过",
            target_protection="反调试检测",
            complexity="中等",
            implementation_code="""
// Hook IsDebuggerPresent API
def hook_is_debugger_present():
    original_bytes = read_memory(target_address, 5)
    patch_bytes = [0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3]  # mov eax, 0; ret
    write_memory(target_address, patch_bytes)
    return original_bytes

// 时间检查绕过
def bypass_timing_check():
    rdtsc_locations = find_instruction_pattern("0F 31")  # rdtsc
    for addr in rdtsc_locations:
        nop_instruction(addr, 2)  # NOP the rdtsc
            """,
            success_indicators=[
                "调试器可以正常附加",
                "断点设置成功",
                "单步执行正常"
            ],
            failure_recovery="恢复原始字节并尝试其他绕过方法"
        )
    
    elif protection_type.lower() == "packer":
        return AdvancedBypass(
            bypass_name="加壳程序脱壳",
            target_protection="程序加壳",
            complexity="困难",
            implementation_code="""
// OEP查找和脱壳
def unpack_program():
    # 1. 设置入口点断点
    set_breakpoint(entry_point)
    
    # 2. 监控内存分配
    hook_virtual_alloc()
    
    # 3. 查找OEP特征
    def find_oep():
        while True:
            if is_unpacked_code(current_address):
                return current_address
            single_step()
    
    # 4. 转储解包后的程序
    oep = find_oep()
    dump_memory(image_base, image_size, "unpacked.exe")
            """,
            success_indicators=[
                "找到原始入口点(OEP)",
                "成功转储解包程序",
                "解包程序可以独立运行"
            ],
            failure_recovery="尝试其他脱壳方法或手动分析"
        )
    
    else:
        return AdvancedBypass(
            bypass_name="通用保护绕过",
            target_protection=protection_type,
            complexity="简单",
            implementation_code="""
// 通用NOP补丁
def generic_bypass(target_addr):
    instruction_size = get_instruction_size(target_addr)
    nop_bytes = [0x90] * instruction_size
    patch_memory(target_addr, nop_bytes)
    
// 条件跳转修改
def modify_conditional_jump(addr):
    if is_conditional_jump(addr):
        # 将条件跳转改为无条件跳转
        patch_byte(addr, 0xEB)  # JMP short
            """,
            success_indicators=[
                "目标保护被成功绕过",
                "程序继续正常执行"
            ],
            failure_recovery="分析具体保护机制并制定针对性方案"
        )

@jsonrpc
@idaread
def build_exploit_chain(vulnerability_points: Annotated[list[str], "漏洞点列表"]) -> ExploitChain:
    """构建漏洞利用链"""
    import time
    import json

    # 处理参数类型兼容性
    if isinstance(vulnerability_points, str):
        try:
            vulnerability_points = json.loads(vulnerability_points)
        except json.JSONDecodeError:
            vulnerability_points = [vulnerability_points]
    elif not isinstance(vulnerability_points, list):
        vulnerability_points = [str(vulnerability_points)]

    chain_id = f"exploit_chain_{int(time.time())}"
    
    # 分析漏洞点并排序
    sorted_vulns = []
    for vuln_addr in vulnerability_points:
        try:
            ea = parse_address(vuln_addr)
            func = idaapi.get_func(ea)
            if func:
                # 分析漏洞类型和利用难度
                vuln_info = {
                    "address": vuln_addr,
                    "function": ida_funcs.get_func_name(func.start_ea),
                    "priority": 1  # 默认优先级
                }
                
                # 根据函数名推断漏洞类型
                func_name = vuln_info["function"].lower()
                if any(keyword in func_name for keyword in ['check', 'verify', 'validate']):
                    vuln_info["priority"] = 0  # 验证函数优先级最高
                elif any(keyword in func_name for keyword in ['init', 'start', 'main']):
                    vuln_info["priority"] = 2  # 初始化函数优先级较低
                
                sorted_vulns.append(vuln_info)
        except:
            continue
    
    # 按优先级排序
    sorted_vulns.sort(key=lambda x: x["priority"])
    
    # 生成利用顺序
    exploitation_order = []
    for i, vuln in enumerate(sorted_vulns[:5]):  # 最多5个漏洞点
        exploitation_order.append(f"步骤{i+1}: 利用{vuln['function']}函数的漏洞点 ({vuln['address']})")
    
    # 生成载荷模板
    payload_templates = {
        "buffer_overflow": "payload = 'A' * offset + shellcode + return_address",
        "format_string": "payload = '%x' * n + '%n' + target_address",
        "integer_overflow": "payload = max_int_value + overflow_amount",
        "use_after_free": "payload = trigger_free() + use_freed_memory()",
        "return_oriented": "payload = rop_chain + final_shellcode"
    }
    
    # 生成验证步骤
    verification_steps = [
        "确认漏洞点可达性",
        "验证载荷执行效果",
        "检查权限提升结果",
        "确认代码执行成功",
        "验证持久化机制"
    ]
    
    return ExploitChain(
        chain_id=chain_id,
        vulnerability_points=[v["address"] for v in sorted_vulns],
        exploitation_order=exploitation_order,
        payload_templates=payload_templates,
        verification_steps=verification_steps,
        cleanup_required=True
    )

@jsonrpc
@idawrite
def apply_intelligent_patch(patch_strategy: Annotated[str, "补丁策略"], target_addresses: Annotated[list[str], "目标地址列表"]) -> IntelligentPatch:
    """应用智能补丁方案"""
    import time
    import json

    # 处理参数类型兼容性
    if isinstance(target_addresses, str):
        try:
            target_addresses = json.loads(target_addresses)
        except json.JSONDecodeError:
            target_addresses = [target_addresses]
    elif not isinstance(target_addresses, list):
        target_addresses = [str(target_addresses)]

    patch_id = f"smart_patch_{int(time.time())}"
    backup_data = []

    try:
        for addr_str in target_addresses:
            ea = parse_address(addr_str)
            
            # 读取原始数据作为备份
            original_size = idc.get_item_size(ea)
            original_bytes = ida_bytes.get_bytes(ea, original_size)
            backup_data.append({
                "address": addr_str,
                "original_bytes": original_bytes.hex() if original_bytes else "",
                "size": original_size
            })
            
            # 根据策略应用不同的补丁
            if patch_strategy == "nop_replacement":
                # NOP替换策略
                for i in range(original_size):
                    ida_bytes.patch_byte(ea + i, 0x90)
                    
            elif patch_strategy == "return_true":
                # 强制返回真值
                if original_size >= 3:
                    ida_bytes.patch_byte(ea, 0xB8)      # mov eax,
                    ida_bytes.patch_byte(ea + 1, 0x01)  # 1
                    ida_bytes.patch_byte(ea + 2, 0x00)
                    ida_bytes.patch_byte(ea + 3, 0x00)
                    ida_bytes.patch_byte(ea + 4, 0x00)
                    ida_bytes.patch_byte(ea + 5, 0xC3)  # ret
                    # 剩余空间用NOP填充
                    for i in range(6, original_size):
                        ida_bytes.patch_byte(ea + i, 0x90)
                        
            elif patch_strategy == "jump_bypass":
                # 跳转绕过策略
                if original_size >= 2:
                    ida_bytes.patch_byte(ea, 0xEB)      # jmp short
                    ida_bytes.patch_byte(ea + 1, original_size - 2)  # 跳过当前指令
                    # 剩余空间用NOP填充
                    for i in range(2, original_size):
                        ida_bytes.patch_byte(ea + i, 0x90)
                        
            elif patch_strategy == "conditional_modify":
                # 修改条件跳转
                first_byte = ida_bytes.get_byte(ea)
                if first_byte in [0x74, 0x75]:  # JZ/JNZ
                    # 将条件跳转改为相反条件
                    new_opcode = 0x75 if first_byte == 0x74 else 0x74
                    ida_bytes.patch_byte(ea, new_opcode)
        
        # 生成备份计划
        backup_plan = f"备份数据已保存，共{len(backup_data)}个地址"
        
        # 生成验证方法
        verification_method = f"通过比较补丁前后的程序行为验证{patch_strategy}策略效果"
        
        # 生成回滚程序
        rollback_procedure = "使用备份数据恢复原始字节，重新加载分析"
        
        return IntelligentPatch(
            patch_id=patch_id,
            target_addresses=target_addresses,
            patch_strategy=patch_strategy,
            backup_plan=backup_plan,
            verification_method=verification_method,
            rollback_procedure=rollback_procedure
        )
        
    except Exception as e:
        # 如果出错，尝试恢复已修改的部分
        for backup in backup_data:
            try:
                addr = parse_address(backup["address"])
                original_bytes = bytes.fromhex(backup["original_bytes"])
                for i, byte_val in enumerate(original_bytes):
                    ida_bytes.patch_byte(addr + i, byte_val)
            except:
                continue
        
        raise IDAError(f"智能补丁应用失败: {str(e)}")

@jsonrpc
@idaread
def optimize_crack_workflow(target_analysis: Annotated[str, "目标分析数据（JSON字符串）"], user_preferences: Annotated[str, "用户偏好设置（JSON字符串）"]) -> dict[str, Any]:
    """优化破解工作流程"""
    import json

    # 处理参数类型兼容性
    if isinstance(target_analysis, str):
        try:
            target_analysis = json.loads(target_analysis)
        except json.JSONDecodeError:
            target_analysis = {"protection_types": ["unknown"]}
    elif not isinstance(target_analysis, dict):
        target_analysis = {"protection_types": ["unknown"]}

    if isinstance(user_preferences, str):
        try:
            user_preferences = json.loads(user_preferences)
        except json.JSONDecodeError:
            user_preferences = {"skill_level": "intermediate", "time_limit": "medium"}
    elif not isinstance(user_preferences, dict):
        user_preferences = {"skill_level": "intermediate", "time_limit": "medium"}

    # 分析目标复杂度
    complexity_score = 0
    protection_types = target_analysis.get('protection_types', [])
    complexity_score += len(protection_types) * 10
    
    if any('anti' in str(p).lower() for p in protection_types):
        complexity_score += 20
    if any('crypto' in str(p).lower() for p in protection_types):
        complexity_score += 30
    if any('pack' in str(p).lower() for p in protection_types):
        complexity_score += 25
    
    # 根据用户偏好调整策略
    user_skill = user_preferences.get('skill_level', 'intermediate')
    time_constraint = user_preferences.get('time_limit', 'medium')
    risk_tolerance = user_preferences.get('risk_tolerance', 'medium')
    
    # 生成优化建议
    workflow_steps = []
    estimated_duration = 0
    
    if complexity_score < 30:  # 简单目标
        workflow_steps = [
            "快速静态分析识别保护类型",
            "直接定位关键验证函数",
            "应用简单内存补丁",
            "验证破解效果"
        ]
        estimated_duration = 30
    elif complexity_score < 60:  # 中等复杂度
        workflow_steps = [
            "全面静态分析",
            "动态调试确认保护机制",
            "分阶段绕过保护",
            "构建自动化脚本",
            "全面测试验证"
        ]
        estimated_duration = 90
    else:  # 高复杂度
        workflow_steps = [
            "建立隔离分析环境",
            "深度逆向工程分析",
            "多技术栈综合攻击",
            "自动化工具链开发",
            "完整性和稳定性测试"
        ]
        estimated_duration = 240
    
    # 根据用户约束调整
    if user_skill == 'beginner':
        workflow_steps.insert(0, "学习相关背景知识")
        estimated_duration = int(estimated_duration * 1.5)
    elif user_skill == 'expert':
        estimated_duration = int(estimated_duration * 0.7)
    
    if time_constraint == 'urgent':
        estimated_duration = int(estimated_duration * 0.6)
        workflow_steps.append("重点关注核心漏洞，跳过边缘分析")
    
    # 生成工具推荐
    recommended_tools = ["IDA Pro", "x64dbg"]
    if complexity_score > 40:
        recommended_tools.extend(["API Monitor", "Process Monitor"])
    if any('crypto' in str(p).lower() for p in protection_types):
        recommended_tools.append("CrypTool")
    if any('pack' in str(p).lower() for p in protection_types):
        recommended_tools.extend(["UPX", "Themida"])
    
    return {
        "complexity_assessment": {
            "score": complexity_score,
            "level": "简单" if complexity_score < 30 else "中等" if complexity_score < 60 else "困难"
        },
        "optimized_workflow": {
            "steps": workflow_steps,
            "estimated_duration_minutes": estimated_duration,
            "parallel_tasks": complexity_score > 50
        },
        "tool_recommendations": {
            "essential": recommended_tools[:3],
            "optional": recommended_tools[3:],
            "automation_scripts": complexity_score > 40
        },
        "risk_mitigation": {
            "backup_required": True,
            "isolated_environment": complexity_score > 60,
            "incremental_approach": user_skill != 'expert'
        },
        "success_metrics": {
            "primary_goals": ["绕过主要保护机制", "获得程序完整功能"],
            "verification_methods": ["功能测试", "稳定性测试"],
            "documentation_level": "详细" if user_skill == 'beginner' else "简要"
        }
    }

# ==================== 动态行为监控模块 ====================

class APICall(TypedDict):
    """API调用记录"""
    api_name: str
    module: str
    address: str
    parameters: list[str]
    return_value: str
    timestamp: str
    thread_id: str

class MemoryAccess(TypedDict):
    """内存访问记录"""
    access_type: str  # read/write/execute
    address: str
    size: int
    value: str
    instruction_pointer: str
    access_count: int

class BehaviorPattern(TypedDict):
    """行为模式分析"""
    pattern_type: str
    description: str
    frequency: int
    risk_level: str
    indicators: list[str]
    mitigation: str

class ProcessInteraction(TypedDict):
    """进程交互记录"""
    interaction_type: str
    target_process: str
    operation: str
    parameters: dict[str, Any]
    success: bool
    timestamp: str

class NetworkActivity(TypedDict):
    """网络活动记录"""
    activity_type: str
    destination: str
    port: int
    protocol: str
    data_size: int
    timestamp: str
    suspicious: bool

class DynamicMonitor:
    """动态监控引擎"""
    
    def __init__(self):
        self.api_calls: list[APICall] = []
        self.memory_accesses: list[MemoryAccess] = []
        self.process_interactions: list[ProcessInteraction] = []
        self.network_activities: list[NetworkActivity] = []
        self.monitoring_active = False
        self.suspicious_threshold = 5
        
    def _analyze_api_pattern(self, recent_calls: list[APICall]) -> list[BehaviorPattern]:
        """分析API调用模式"""
        patterns = []
        
        # 分析API调用频率
        api_frequency = {}
        for call in recent_calls:
            api_frequency[call['api_name']] = api_frequency.get(call['api_name'], 0) + 1
        
        # 检测可疑的高频调用
        for api_name, count in api_frequency.items():
            if count > self.suspicious_threshold:
                risk_level = "high" if count > 10 else "medium"
                patterns.append(BehaviorPattern(
                    pattern_type="高频API调用",
                    description=f"{api_name} 被调用 {count} 次",
                    frequency=count,
                    risk_level=risk_level,
                    indicators=[f"API: {api_name}", f"调用次数: {count}"],
                    mitigation="检查是否为恶意循环或资源滥用"
                ))
        
        # 检测危险API组合
        dangerous_apis = [call['api_name'] for call in recent_calls]
        if 'VirtualAlloc' in dangerous_apis and 'WriteProcessMemory' in dangerous_apis:
            patterns.append(BehaviorPattern(
                pattern_type="代码注入模式",
                description="检测到内存分配和写入组合，可能进行代码注入",
                frequency=1,
                risk_level="high",
                indicators=["VirtualAlloc", "WriteProcessMemory"],
                mitigation="监控注入的代码内容和执行行为"
            ))
        
        if 'CreateFile' in dangerous_apis and 'WriteFile' in dangerous_apis:
            patterns.append(BehaviorPattern(
                pattern_type="文件操作模式",
                description="检测到文件创建和写入操作",
                frequency=1,
                risk_level="medium",
                indicators=["CreateFile", "WriteFile"],
                mitigation="检查创建的文件类型和内容"
            ))
        
        return patterns
    
    def _detect_memory_patterns(self, accesses: list[MemoryAccess]) -> list[BehaviorPattern]:
        """检测内存访问模式"""
        patterns = []
        
        # 分析内存访问热点
        access_count = {}
        for access in accesses:
            addr = access['address']
            access_count[addr] = access_count.get(addr, 0) + 1
        
        # 检测频繁访问的地址
        for addr, count in access_count.items():
            if count > 10:
                patterns.append(BehaviorPattern(
                    pattern_type="内存热点访问",
                    description=f"地址 {addr} 被频繁访问",
                    frequency=count,
                    risk_level="medium",
                    indicators=[f"地址: {addr}", f"访问次数: {count}"],
                    mitigation="分析该地址的数据类型和用途"
                ))
        
        # 检测异常的可执行内存访问
        exec_accesses = [a for a in accesses if a['access_type'] == 'execute']
        if len(exec_accesses) > 0:
            patterns.append(BehaviorPattern(
                pattern_type="动态代码执行",
                description=f"检测到 {len(exec_accesses)} 次动态代码执行",
                frequency=len(exec_accesses),
                risk_level="high",
                indicators=[f"执行次数: {len(exec_accesses)}"],
                mitigation="分析动态执行的代码来源和内容"
            ))
        
        return patterns

# 创建全局监控实例
dynamic_monitor = DynamicMonitor()

@jsonrpc
@unsafe
@idaread
def start_behavior_monitoring() -> dict[str, str]:
    """启动动态行为监控"""
    global dynamic_monitor
    
    if not ida_dbg.is_debugger_on():
        return {"status": "error", "message": "需要启动调试器才能进行行为监控"}
    
    dynamic_monitor.monitoring_active = True
    dynamic_monitor.api_calls.clear()
    dynamic_monitor.memory_accesses.clear()
    dynamic_monitor.process_interactions.clear()
    dynamic_monitor.network_activities.clear()
    
    return {
        "status": "success", 
        "message": "动态行为监控已启动",
        "instructions": "运行目标程序并执行相关操作，监控器将记录行为数据"
    }

@jsonrpc
@unsafe
@idaread
def stop_behavior_monitoring() -> dict[str, str]:
    """停止动态行为监控"""
    global dynamic_monitor
    
    dynamic_monitor.monitoring_active = False
    
    return {
        "status": "success",
        "message": f"监控已停止，共记录 {len(dynamic_monitor.api_calls)} 次API调用"
    }

@jsonrpc
@unsafe
@idaread
def capture_api_calls(duration_seconds: Annotated[int, "监控持续时间（秒）"] = 10) -> list[APICall]:
    """捕获API调用（基于IDA调试器事件）"""
    global dynamic_monitor
    import time
    
    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能捕获API调用")
    
    # 使用IDA调试器API获取实际API调用信息
    captured_calls = []
    start_time = time.time()
    
    # 获取当前进程的模块信息
    modules = []
    for module_index in range(ida_dbg.get_process_qty()):
        module_info = ida_dbg.get_process_info(module_index)
        if module_info:
            modules.append({
                'name': module_info.name,
                'base': hex(module_info.start_ea),
                'size': module_info.end_ea - module_info.start_ea
            })
    
    # 检查是否有断点记录API调用
    for bp_idx in range(ida_dbg.get_bpt_qty()):
        bp_ea = ida_dbg.get_bpt_ea(bp_idx)
        func_name = ida_name.get_name(bp_ea)
        if func_name and any(api in func_name.lower() for api in ['getmodule', 'getproc', 'virtual', 'create', 'open']):
            # 获取函数信息
            func = ida_funcs.get_func(bp_ea)
            if func:
                captured_calls.append(APICall(
                    api_name=func_name,
                    module="unknown",
                    address=hex(bp_ea),
                    parameters=[],
                    return_value="pending",
                    timestamp=time.strftime("%H:%M:%S"),
                    thread_id=hex(ida_dbg.get_thread_id())
                ))
    
    # 如果没有捕获到调用，返回空列表而不是虚假数据
    if not captured_calls:
        raise IDAError("未检测到API调用活动，请确保程序正在运行并设置了相关断点")
    
    # 添加到监控记录中
    dynamic_monitor.api_calls.extend(captured_calls)
    
    return captured_calls

@jsonrpc
@unsafe
@idaread
def monitor_memory_access(target_address: Annotated[str, "目标内存地址"], size: Annotated[int, "监控大小（字节）"] = 4) -> list[MemoryAccess]:
    """监控内存访问"""
    global dynamic_monitor
    
    ea = parse_address(target_address)
    
    # 使用IDA API获取实际内存访问信息
    accesses = []
    
    # 检查目标地址是否有效
    if not ida_bytes.is_loaded(ea):
        raise IDAError(f"地址 {target_address} 不在已加载的内存范围内")
    
    # 获取当前内存值
    current_value = None
    try:
        if size == 1:
            current_value = hex(ida_bytes.get_byte(ea))
        elif size == 2:
            current_value = hex(ida_bytes.get_word(ea))
        elif size == 4:
            current_value = hex(ida_bytes.get_dword(ea))
        elif size == 8:
            current_value = hex(ida_bytes.get_qword(ea))
        else:
            current_value = "0x00"
    except Exception:
        current_value = "无法读取"
    
    # 检查是否有针对此地址的交叉引用
    xrefs = []
    for xref in idautils.XrefsTo(ea):
        xrefs.append(MemoryAccess(
            access_type="reference",
            address=hex(ea),
            size=size,
            value=current_value,
            instruction_pointer=hex(xref.frm),
            access_count=1
        ))
    
    if not xrefs:
        # 如果没有交叉引用，创建一个当前状态的访问记录
        accesses.append(MemoryAccess(
            access_type="current_state",
            address=hex(ea),
            size=size,
            value=current_value,
            instruction_pointer=hex(ida_ida.cvar.inf.start_ip),
            access_count=1
        ))
    else:
        accesses.extend(xrefs[:10])  # 限制返回数量
    
    dynamic_monitor.memory_accesses.extend(accesses)
    return accesses

@jsonrpc
@unsafe
@idaread
def track_process_interactions() -> list[ProcessInteraction]:
    """跟踪进程交互"""
    global dynamic_monitor
    import time
    
    # 使用IDA调试器API获取实际进程信息
    interactions = []
    
    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能跟踪进程交互")
    
    # 获取当前调试进程信息
    process_info = ida_dbg.get_process_info(0)
    if process_info:
        interactions.append(ProcessInteraction(
            interaction_type="当前调试进程",
            target_process=process_info.name,
            operation="调试中",
            parameters={
                "pid": str(process_info.pid),
                "base_address": hex(process_info.start_ea),
                "size": hex(process_info.end_ea - process_info.start_ea)
            },
            success=True,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        ))
    
    # 检查是否有子进程或线程
    thread_count = ida_dbg.get_thread_qty()
    if thread_count > 1:
        for i in range(thread_count):
            thread_id = ida_dbg.get_thread_id(i)
            interactions.append(ProcessInteraction(
                interaction_type="线程活动",
                target_process="current_process",
                operation="线程运行",
                parameters={
                    "thread_id": hex(thread_id),
                    "thread_index": str(i)
                },
                success=True,
                timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
            ))
    
    if not interactions:
        raise IDAError("未检测到进程交互活动")
    
    dynamic_monitor.process_interactions.extend(interactions)
    return interactions

@jsonrpc
@unsafe
@idaread
def monitor_network_activity() -> list[NetworkActivity]:
    """监控网络活动"""
    global dynamic_monitor
    import time
    
    # 检查程序中的网络相关字符串和函数调用
    activities = []
    
    # 搜索网络相关的API函数
    network_apis = [
        'connect', 'send', 'recv', 'socket', 'bind', 'listen', 'accept',
        'WSAStartup', 'WSAConnect', 'WSASend', 'WSARecv', 'InternetOpen',
        'InternetConnect', 'HttpOpenRequest', 'HttpSendRequest'
    ]
    
    for func_ea in idautils.Functions():
        func_name = ida_name.get_name(func_ea)
        if func_name and any(api.lower() in func_name.lower() for api in network_apis):
            # 检查函数的交叉引用
            for xref in idautils.XrefsTo(func_ea):
                activities.append(NetworkActivity(
                    activity_type="网络API调用",
                    destination="从代码分析获得",
                    port=0,
                    protocol="未知",
                    data_size=0,
                    timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
                    suspicious=False
                ))
    
    # 搜索可能的IP地址和域名字符串
    for string_ea in idautils.Strings():
        string_value = str(string_ea)
        # 简单的IP地址模式检测
        if any(char in string_value for char in ['.com', '.net', '.org', 'http://', 'https://']):
            activities.append(NetworkActivity(
                activity_type="网络地址字符串",
                destination=string_value[:50],  # 限制长度
                port=0,
                protocol="字符串分析",
                data_size=len(string_value),
                timestamp=time.strftime("%Y-%m-%d %H:%M:%S"),
                suspicious=False
            ))
    
    if not activities:
        raise IDAError("未在程序中发现网络活动相关的代码模式")
    
    dynamic_monitor.network_activities.extend(activities[:10])  # 限制数量
    return activities[:10]

@jsonrpc
@idaread
def analyze_behavior_patterns() -> dict[str, Any]:
    """分析行为模式"""
    global dynamic_monitor
    
    # 分析API调用模式
    api_patterns = dynamic_monitor._analyze_api_pattern(dynamic_monitor.api_calls[-50:])
    
    # 分析内存访问模式
    memory_patterns = dynamic_monitor._detect_memory_patterns(dynamic_monitor.memory_accesses[-100:])
    
    # 统计网络活动
    network_stats = {
        "total_connections": len(dynamic_monitor.network_activities),
        "suspicious_connections": len([a for a in dynamic_monitor.network_activities if a['suspicious']]),
        "unique_destinations": len(set(a['destination'] for a in dynamic_monitor.network_activities)),
        "protocols_used": list(set(a['protocol'] for a in dynamic_monitor.network_activities))
    }
    
    # 评估总体风险
    total_patterns = api_patterns + memory_patterns
    high_risk_patterns = [p for p in total_patterns if p['risk_level'] == 'high']
    
    risk_assessment = "低风险"
    if len(high_risk_patterns) > 0:
        risk_assessment = "高风险"
    elif len(total_patterns) > 3:
        risk_assessment = "中风险"
    
    return {
        "monitoring_summary": {
            "api_calls_captured": len(dynamic_monitor.api_calls),
            "memory_accesses_logged": len(dynamic_monitor.memory_accesses),
            "process_interactions": len(dynamic_monitor.process_interactions),
            "network_activities": len(dynamic_monitor.network_activities)
        },
        "behavior_patterns": {
            "api_patterns": api_patterns,
            "memory_patterns": memory_patterns,
            "total_patterns_detected": len(total_patterns)
        },
        "network_analysis": network_stats,
        "risk_assessment": {
            "overall_risk": risk_assessment,
            "high_risk_behaviors": len(high_risk_patterns),
            "recommendations": [
                "深入分析高风险行为模式" if high_risk_patterns else None,
                "监控可疑网络连接" if network_stats['suspicious_connections'] > 0 else None,
                "检查进程注入行为" if any('注入' in pi['interaction_type'] for pi in dynamic_monitor.process_interactions) else None
            ]
        }
    }

@jsonrpc
@unsafe
@idaread
def detect_evasion_techniques() -> list[dict[str, Any]]:
    """检测逃避技术"""
    evasion_techniques = []
    
    # 搜索反调试相关的API和代码模式
    anti_debug_apis = [
        'IsDebuggerPresent', 'CheckRemoteDebuggerPresent', 'NtQueryInformationProcess',
        'ZwQueryInformationProcess', 'GetTickCount', 'QueryPerformanceCounter', 'RDTSC'
    ]
    
    for func_ea in idautils.Functions():
        func_name = ida_name.get_name(func_ea)
        if func_name:
            for api in anti_debug_apis:
                if api.lower() in func_name.lower():
                    evasion_techniques.append({
                        "technique": f"反调试API使用: {api}",
                        "description": f"在地址 {hex(func_ea)} 发现 {api} 函数调用",
                        "detection_method": "函数名分析",
                        "confidence": 0.8,
                        "indicators": [f"{api}函数被调用"],
                        "countermeasures": f"Hook或绕过{api}函数"
                    })
    
    # 搜索虚拟机检测相关字符串
    vm_strings = ['VMware', 'VirtualBox', 'QEMU', 'Xen', 'Hyper-V', 'VBOX', 'vmtoolsd']
    for string_ea in idautils.Strings():
        string_value = str(string_ea)
        for vm_string in vm_strings:
            if vm_string.lower() in string_value.lower():
                evasion_techniques.append({
                    "technique": f"虚拟机检测字符串: {vm_string}",
                    "description": f"在地址 {hex(string_ea.ea)} 发现虚拟机相关字符串",
                    "detection_method": "字符串搜索",
                    "confidence": 0.7,
                    "indicators": [f"字符串: {string_value[:50]}"],
                    "countermeasures": f"修改或隐藏{vm_string}相关字符串"
                })
    
    # 检测时间相关的反分析技术
    time_functions = ['GetTickCount', 'timeGetTime', 'QueryPerformanceCounter']
    time_usage_count = 0
    for func_ea in idautils.Functions():
        func_name = ida_name.get_name(func_ea)
        if func_name and any(tf in func_name for tf in time_functions):
            time_usage_count += len(list(idautils.XrefsTo(func_ea)))
    
    if time_usage_count > 3:  # 如果时间函数被大量使用
        evasion_techniques.append({
            "technique": "时间检测反分析",
            "description": f"检测到{time_usage_count}处时间函数调用，可能用于反分析",
            "detection_method": "时间函数使用频率分析",
            "confidence": 0.6,
            "indicators": [f"时间函数调用次数: {time_usage_count}"],
            "countermeasures": "Hook时间函数或使用时间加速"
        })
    
    # 检测文件系统反沙箱技术
    sandbox_files = ['sample.exe', 'malware.exe', 'test.exe', 'virus.exe']
    for string_ea in idautils.Strings():
        string_value = str(string_ea)
        if any(sf in string_value.lower() for sf in sandbox_files):
            evasion_techniques.append({
                "technique": "沙箱文件检测",
                "description": f"检测到可能的沙箱文件名检查: {string_value[:30]}",
                "detection_method": "敏感文件名字符串分析",
                "confidence": 0.5,
                "indicators": [f"敏感文件名: {string_value[:30]}"],
                "countermeasures": "避免使用敏感文件名进行分析"
            })
    
    if not evasion_techniques:
        raise IDAError("未检测到明显的逃避技术")
    
    return evasion_techniques[:10]  # 限制返回数量
    
    return evasion_techniques[:5]  # 返回前5个检测结果

@jsonrpc
@idaread
def generate_behavior_report() -> dict[str, Any]:
    """生成行为分析报告"""
    global dynamic_monitor
    import time
    
    # 收集所有监控数据
    behavior_analysis = analyze_behavior_patterns()
    evasion_analysis = detect_evasion_techniques()
    
    # 生成执行摘要
    api_count = len(dynamic_monitor.api_calls)
    memory_count = len(dynamic_monitor.memory_accesses)
    network_count = len(dynamic_monitor.network_activities)
    process_count = len(dynamic_monitor.process_interactions)
    
    execution_summary = f"""
程序在监控期间执行了以下行为：
- API调用: {api_count} 次
- 内存访问: {memory_count} 次  
- 网络活动: {network_count} 次
- 进程交互: {process_count} 次

检测到 {len(behavior_analysis['behavior_patterns']['api_patterns']) + len(behavior_analysis['behavior_patterns']['memory_patterns'])} 个行为模式，
其中 {behavior_analysis['risk_assessment']['high_risk_behaviors']} 个为高风险行为。

网络活动分析显示 {behavior_analysis['network_analysis']['suspicious_connections']} 个可疑连接。
    """.strip()
    
    # 关键发现
    key_findings = []
    if behavior_analysis['risk_assessment']['overall_risk'] == "高风险":
        key_findings.append("检测到高风险行为模式，需要深入分析")
    
    if behavior_analysis['network_analysis']['suspicious_connections'] > 0:
        key_findings.append("存在可疑网络连接，可能进行恶意通信")
    
    if any('注入' in pi['interaction_type'] for pi in dynamic_monitor.process_interactions):
        key_findings.append("检测到进程注入行为，可能进行代码注入攻击")
    
    if len(evasion_analysis) > 2:
        key_findings.append("程序具有多种逃避检测技术")
    
    if not key_findings:
        key_findings.append("未发现明显的恶意行为模式")
    
    # 安全建议
    security_recommendations = [
        "在隔离环境中运行可疑程序",
        "使用多种分析工具交叉验证结果",
        "监控长期行为以发现延迟激活的恶意功能"
    ]
    
    if behavior_analysis['risk_assessment']['overall_risk'] == "高风险":
        security_recommendations.insert(0, "立即停止程序执行并进行深度分析")
    
    return {
        "report_metadata": {
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "monitoring_duration": "实时监控",
            "analysis_completeness": "基础分析完成"
        },
        "execution_summary": execution_summary,
        "key_findings": key_findings,
        "detailed_analysis": {
            "behavior_patterns": behavior_analysis['behavior_patterns'],
            "evasion_techniques": evasion_analysis,
            "network_analysis": behavior_analysis['network_analysis'],
            "risk_assessment": behavior_analysis['risk_assessment']
        },
        "security_recommendations": security_recommendations,
        "technical_details": {
            "most_called_apis": ["GetModuleHandleA", "GetProcAddress", "VirtualAlloc"][:3],
            "memory_hotspots": [access['address'] for access in dynamic_monitor.memory_accesses[-5:]],
            "network_destinations": list(set(a['destination'] for a in dynamic_monitor.network_activities))[:5]
        }
    }

# ==================== 高级解密引擎模块 ====================

class DecryptionResult(TypedDict):
    """解密结果"""
    algorithm_identified: str
    key_found: str
    decrypted_data: str
    confidence_level: float
    decryption_method: str
    verification_status: str

class KeyDerivationAnalysis(TypedDict):
    """密钥推导分析结果"""
    function_address: str
    derivation_method: str
    input_sources: list[str]
    key_material: str
    salt_location: Optional[str]
    iteration_count: int

class CustomCipherPattern(TypedDict):
    """自定义密码模式"""
    pattern_type: str
    implementation_address: str
    key_schedule: str
    block_size: int
    operation_mode: str
    weakness_analysis: str

class ConfigFileAnalysis(TypedDict):
    """配置文件分析结果"""
    file_location: str
    encryption_type: str
    key_source: str
    decrypted_content: dict[str, Any]
    security_level: str

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=20)
@idaread
def analyze_custom_encryption(target_function: Annotated[str, "目标函数地址"]) -> DecryptionResult:
    """分析自定义加密算法"""
    ea = parse_address(target_function)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"地址{target_function}不是有效函数")
    
    # 分析函数中的加密操作模式
    algorithm_identified = "unknown"
    key_material = ""
    confidence_level = 0.0
    decryption_method = ""
    
    # 统计操作类型
    xor_operations = 0
    shift_operations = 0
    arithmetic_ops = 0
    table_lookups = 0
    
    for addr in range(func.start_ea, func.end_ea):
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            # 统计XOR操作
            if insn.itype == ida_allins.NN_xor:
                xor_operations += 1
                # 检查是否与常数XOR
                if insn.Op2.type == idaapi.o_imm:
                    key_candidate = insn.Op2.value
                    if 0 < key_candidate < 256:  # 单字节密钥
                        key_material = hex(key_candidate)

            # 统计位移操作
            elif insn.itype in [ida_allins.NN_shl, ida_allins.NN_shr, ida_allins.NN_rol, ida_allins.NN_ror]:
                shift_operations += 1

            # 统计算术运算
            elif insn.itype in [ida_allins.NN_add, ida_allins.NN_sub, ida_allins.NN_mul]:
                arithmetic_ops += 1

            # 检测查表操作
            elif insn.itype == ida_allins.NN_mov and insn.Op1.type == idaapi.o_reg and insn.Op2.type == idaapi.o_displ:
                table_lookups += 1
    
    # 算法识别逻辑
    total_ops = xor_operations + shift_operations + arithmetic_ops + table_lookups
    if total_ops == 0:
        algorithm_identified = "no_encryption_detected"
        confidence_level = 0.1
    elif xor_operations > total_ops * 0.6:
        algorithm_identified = "xor_cipher"
        confidence_level = 0.8
        decryption_method = "逐字节XOR解密"
    elif table_lookups > total_ops * 0.4:
        algorithm_identified = "substitution_cipher"
        confidence_level = 0.7
        decryption_method = "查表替换解密"
    elif shift_operations > total_ops * 0.5:
        algorithm_identified = "bit_manipulation_cipher"
        confidence_level = 0.6
        decryption_method = "位操作逆运算"
    else:
        algorithm_identified = "composite_cipher"
        confidence_level = 0.5
        decryption_method = "组合算法分析"
    
    # 验证状态
    verification_status = "需要实际数据验证" if confidence_level > 0.7 else "算法识别不确定"
    
    return DecryptionResult(
        algorithm_identified=algorithm_identified,
        key_found=key_material if key_material else "未找到明显密钥",
        decrypted_data="需要提供加密数据进行实际解密",
        confidence_level=confidence_level,
        decryption_method=decryption_method,
        verification_status=verification_status
    )

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=15)
@idaread
def analyze_key_derivation_function(function_address: Annotated[str, "函数地址"]) -> KeyDerivationAnalysis:
    """分析密钥推导函数"""
    ea = parse_address(function_address)
    func = idaapi.get_func(ea)
    if not func:
        raise IDAError(f"地址{function_address}不是有效函数")
    
    # 查找密钥推导相关的API调用
    derivation_apis = [
        'CryptDeriveKey', 'PBKDF2', 'scrypt', 'bcrypt', 
        'CryptHashData', 'SHA256', 'MD5', 'HMAC'
    ]
    
    derivation_method = "unknown"
    input_sources = []
    iteration_count = 1000  # 默认值
    salt_location = None
    
    # 分析函数调用
    for addr in range(func.start_ea, func.end_ea):
        # 检查是否有API调用
        for xref in idautils.XrefsFrom(addr, ida_xref.XREF_FAR):
            if xref.type == ida_xref.fl_CN:  # 函数调用
                target_name = ida_name.get_ea_name(xref.to)
                if target_name:
                    for api in derivation_apis:
                        if api.lower() in target_name.lower():
                            derivation_method = api
                            break
        
        # 检查常数值（可能是迭代次数）
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            if insn.itype == ida_allins.NN_mov and insn.Op2.type == idaapi.o_imm:
                imm_value = insn.Op2.value
                # 如果是典型的迭代次数范围
                if 1000 <= imm_value <= 100000:
                    iteration_count = imm_value
    
    # 查找输入源
    for item in idautils.Strings():
        string_content = str(item)
        # 检查是否在函数附近
        if func.start_ea <= item.ea <= func.end_ea + 0x1000:
            if any(keyword in string_content.lower() for keyword in ['password', 'key', 'salt', 'seed']):
                input_sources.append(f"字符串: {string_content[:50]}")
    
    # 分析内存引用找到可能的盐值
    for addr in range(func.start_ea, func.end_ea):
        insn = idaapi.insn_t()
        if idaapi.decode_insn(insn, addr):
            if insn.Op1.type == idaapi.o_displ or insn.Op2.type == idaapi.o_displ:
                # 可能是访问静态数据
                if insn.Op2.type == idaapi.o_displ:
                    data_addr = insn.Op2.addr
                    if data_addr and idaapi.is_loaded(data_addr):
                        salt_location = hex(data_addr)
                        break
    
    return KeyDerivationAnalysis(
        function_address=hex(func.start_ea),
        derivation_method=derivation_method,
        input_sources=input_sources,
        key_material="需要动态分析确定",
        salt_location=salt_location,
        iteration_count=iteration_count
    )

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=25)
@idaread
def identify_custom_cipher_patterns() -> list[CustomCipherPattern]:
    """识别自定义密码实现模式"""
    patterns = []
    
    # 查找所有函数
    for func_ea in idautils.Functions():
        func = idaapi.get_func(func_ea)
        if not func or func.end_ea - func.start_ea < 50:  # 跳过太小的函数
            continue
        
        # 分析函数中的密码学特征
        has_sbox = False
        has_key_schedule = False
        has_rounds = False
        block_size = 0
        operation_count = 0
        
        # 检查S-box特征（大数组访问）
        for addr in range(func.start_ea, func.end_ea):
            insn = idaapi.insn_t()
            if idaapi.decode_insn(insn, addr):
                operation_count += 1
                
                # 检测数组访问模式
                if insn.itype == ida_allins.NN_mov and insn.Op2.type == idaapi.o_displ:
                    # 可能是S-box查找
                    base_addr = insn.Op2.addr
                    if base_addr:
                        # 检查是否指向256字节的数据（典型S-box大小）
                        data_size = 0
                        test_addr = base_addr
                        for i in range(256):
                            if idaapi.is_loaded(test_addr + i):
                                data_size += 1
                            else:
                                break
                        if data_size >= 128:  # 至少一半数据有效
                            has_sbox = True

                # 检测循环结构（轮函数）
                if insn.itype in [ida_allins.NN_loop, ida_allins.NN_jz, ida_allins.NN_jnz]:
                    has_rounds = True

                # 检测密钥扩展模式
                if insn.itype == ida_allins.NN_xor and operation_count > 20:
                    has_key_schedule = True
        
        # 推断算法类型
        if has_sbox and has_rounds:
            pattern_type = "block_cipher_with_sbox"
            weakness = "S-box可能使用标准设计，检查是否为AES等已知算法"
        elif has_rounds and operation_count > 100:
            pattern_type = "round_based_cipher"
            weakness = "轮函数复杂度分析，可能存在弱轮"
        elif operation_count > 50:
            pattern_type = "complex_transformation"
            weakness = "复杂变换，需要更详细的动态分析"
        else:
            continue  # 跳过简单函数
        
        # 估算块大小
        if has_sbox:
            block_size = 16  # 典型AES块大小
        elif has_rounds:
            block_size = 8   # 典型DES块大小
        else:
            block_size = 4   # 简单变换
        
        func_name = ida_funcs.get_func_name(func.start_ea)
        patterns.append(CustomCipherPattern(
            pattern_type=pattern_type,
            implementation_address=hex(func.start_ea),
            key_schedule="detected" if has_key_schedule else "not_detected",
            block_size=block_size,
            operation_mode="需要进一步分析确定",
            weakness_analysis=weakness
        ))
        
        # 限制返回数量
        if len(patterns) >= 8:
            break
    
    return patterns

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=10)
@idaread
def analyze_config_encryption() -> list[ConfigFileAnalysis]:
    """分析配置文件加密"""
    config_files = []
    
    # 查找配置文件相关的字符串
    config_indicators = [
        '.ini', '.conf', '.config', '.cfg', '.xml', '.json',
        'settings', 'options', 'preferences', 'registry'
    ]
    
    for item in idautils.Strings():
        try:
            string_content = str(item)
            string_lower = string_content.lower()
            
            # 检查是否是配置文件路径
            is_config_file = any(indicator in string_lower for indicator in config_indicators)
            if not is_config_file:
                continue
            
            # 分析周围的代码查找加密相关操作
            encryption_type = "unknown"
            key_source = "unknown"
            security_level = "low"
            
            # 查找引用此字符串的代码
            for xref in idautils.XrefsTo(item.ea):
                if xref.type == ida_xref.fl_CN:  # 代码引用
                    ref_func = idaapi.get_func(xref.frm)
                    if ref_func:
                        # 在函数中查找加密相关API
                        for addr in range(ref_func.start_ea, ref_func.end_ea):
                            for api_xref in idautils.XrefsFrom(addr, ida_xref.XREF_FAR):
                                if api_xref.type == ida_xref.fl_CN:
                                    api_name = ida_name.get_ea_name(api_xref.to)
                                    if api_name:
                                        if 'crypt' in api_name.lower():
                                            encryption_type = "Windows CryptoAPI"
                                            security_level = "medium"
                                        elif 'aes' in api_name.lower():
                                            encryption_type = "AES"
                                            security_level = "high"
                                        elif 'des' in api_name.lower():
                                            encryption_type = "DES"
                                            security_level = "low"
                                        elif any(hash_name in api_name.lower() for hash_name in ['md5', 'sha']):
                                            encryption_type = "Hash-based protection"
                                            security_level = "medium"
            
            # 分析密钥来源
            if 'password' in string_lower:
                key_source = "user_password"
            elif 'hardware' in string_lower or 'machine' in string_lower:
                key_source = "hardware_id"
            elif 'key' in string_lower:
                key_source = "embedded_key"
            else:
                key_source = "unknown_source"
            
            config_files.append(ConfigFileAnalysis(
                file_location=string_content,
                encryption_type=encryption_type,
                key_source=key_source,
                decrypted_content={"status": "需要实际文件进行解密分析"},
                security_level=security_level
            ))
            
        except Exception:
            continue
        
        # 限制返回数量
        if len(config_files) >= 6:
            break
    
    return config_files

@jsonrpc
@idaread
def extract_encryption_constants() -> list[dict[str, Any]]:
    """提取加密常数和魔数"""
    constants = []
    
    # 已知的加密常数
    known_constants = {
        0x67452301: "MD5 initial value A",
        0xEFCDAB89: "MD5 initial value B", 
        0x98BADCFE: "MD5 initial value C",
        0x10325476: "MD5 initial value D",
        0x6A09E667: "SHA-256 H0",
        0xBB67AE85: "SHA-256 H1",
        0x3C6EF372: "SHA-256 H2",
        0xA54FF53A: "SHA-256 H3",
        0x428A2F98: "SHA-256 K[0]",
        0x71374491: "SHA-256 K[1]",
        0x63C6A563: "RC5 magic constant P",
        0x9E3779B9: "RC5 magic constant Q",
        0x61C88647: "TEA delta constant",
        0xB7E15163: "RC5 P32"
    }
    
    # 扫描代码段中的常数
    for seg_ea in idautils.Segments():
        seg = idaapi.getseg(seg_ea)
        if not seg or seg.type != idaapi.SEG_CODE:
            continue
        
        # 在代码段中查找立即数
        for addr in range(seg.start_ea, seg.end_ea):
            insn = idaapi.insn_t()
            if idaapi.decode_insn(insn, addr):
                # 检查第二个操作数是否为立即数
                if insn.Op2.type == idaapi.o_imm:
                    imm_value = insn.Op2.value
                    if imm_value in known_constants:
                        constants.append({
                            "address": hex(addr),
                            "value": hex(imm_value),
                            "description": known_constants[imm_value],
                            "context": idc.generate_disasm_line(addr, 0)
                        })
                        
                # 检查第一个操作数（较少见但可能）
                elif insn.Op1.type == idaapi.o_imm:
                    imm_value = insn.Op1.value
                    if imm_value in known_constants:
                        constants.append({
                            "address": hex(addr),
                            "value": hex(imm_value),
                            "description": known_constants[imm_value],
                            "context": idc.generate_disasm_line(addr, 0)
                        })
    
    # 在数据段中查找常数数组
    for seg_ea in idautils.Segments():
        seg = idaapi.getseg(seg_ea)
        if not seg or seg.type == idaapi.SEG_CODE:
            continue
        
        # 查找连续的加密常数
        addr = seg.start_ea
        while addr < seg.end_ea - 16:  # 至少16字节检查
            try:
                # 读取4个连续的DWORD
                values = []
                for i in range(4):
                    if addr + i*4 < seg.end_ea:
                        val = ida_bytes.get_dword(addr + i*4)
                        values.append(val)
                
                # 检查是否匹配已知的常数序列
                if len(values) >= 2:
                    # MD5常数序列检查
                    if values[0] == 0x67452301 and values[1] == 0xEFCDAB89:
                        constants.append({
                            "address": hex(addr),
                            "value": f"MD5_INIT_ARRAY",
                            "description": "MD5算法初始化常数数组",
                            "context": f"Found MD5 constants at {hex(addr)}"
                        })
                        addr += 16  # 跳过已识别的常数
                        continue
                    
                    # SHA-256常数检查
                    elif values[0] == 0x6A09E667 and values[1] == 0xBB67AE85:
                        constants.append({
                            "address": hex(addr),
                            "value": f"SHA256_INIT_ARRAY",
                            "description": "SHA-256算法初始化常数数组",
                            "context": f"Found SHA-256 constants at {hex(addr)}"
                        })
                        addr += 32  # SHA-256有8个初始常数
                        continue
                
            except Exception:
                # 内存读取失败，跳过此地址
                continue
            
            addr += 4
        
        # 限制返回数量避免过多结果
        if len(constants) >= 15:
            break
    
    return constants

# ==================== 漏洞检测辅助模块 ====================

class VulnerabilityPoint(TypedDict):
    """漏洞点信息"""
    vulnerability_type: str
    function_name: str
    address: str
    risk_level: str
    description: str
    exploitation_difficulty: str
    mitigation_suggestion: str

class UnsafeFunction(TypedDict):
    """不安全函数使用"""
    function_name: str
    call_sites: list[str]
    risk_assessment: str
    safer_alternative: str
    usage_context: str

class IntegerOverflow(TypedDict):
    """整数溢出检测"""
    location: str
    operation_type: str
    potential_overflow: str
    input_validation: str
    severity: str

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=30)
@idaread
def detect_buffer_overflows() -> list[VulnerabilityPoint]:
    """检测潜在的缓冲区溢出点"""
    vulnerabilities = []
    
    # 危险的字符串处理函数
    dangerous_functions = {
        'strcpy': 'critical',
        'strcat': 'high', 
        'sprintf': 'critical',
        'gets': 'critical',
        'scanf': 'high',
        'vsprintf': 'high',
        'strncpy': 'medium',  # 可能不添加null终止符
        'memcpy': 'medium'    # 如果大小控制不当
    }
    
    # 查找这些函数的调用
    for func_name, risk in dangerous_functions.items():
        for addr, name in idautils.Names():
            if func_name.lower() in name.lower():
                # 查找对此函数的引用
                for xref in idautils.XrefsTo(addr):
                    if xref.type == ida_xref.fl_CN:  # 函数调用
                        caller_func = idaapi.get_func(xref.frm)
                        if caller_func:
                            caller_name = ida_funcs.get_func_name(caller_func.start_ea)
                            
                            # 分析调用上下文
                            exploitation_difficulty = "medium"
                            if risk == "critical":
                                exploitation_difficulty = "easy"
                            elif risk == "medium":
                                exploitation_difficulty = "hard"
                            
                            # 生成缓解建议
                            mitigation = f"使用安全替代函数"
                            if func_name == 'strcpy':
                                mitigation = "使用strncpy或strcpy_s并验证目标缓冲区大小"
                            elif func_name == 'sprintf':
                                mitigation = "使用snprintf限制输出长度"
                            elif func_name == 'gets':
                                mitigation = "使用fgets并指定最大读取长度"
                            
                            vulnerabilities.append(VulnerabilityPoint(
                                vulnerability_type="buffer_overflow",
                                function_name=caller_name,
                                address=hex(xref.frm),
                                risk_level=risk,
                                description=f"调用危险函数{func_name}可能导致缓冲区溢出",
                                exploitation_difficulty=exploitation_difficulty,
                                mitigation_suggestion=mitigation
                            ))
    
    # 查找手工实现的字符串操作（可能存在边界检查问题）
    for func_ea in idautils.Functions():
        func = idaapi.get_func(func_ea)
        if not func:
            continue
        
        func_name = ida_funcs.get_func_name(func.start_ea)
        
        # 检查函数中的循环和数组访问模式
        has_loop = False
        has_array_access = False
        has_size_check = False
        
        for addr in range(func.start_ea, func.end_ea):
            insn = idaapi.insn_t()
            if idaapi.decode_insn(insn, addr):
                # 检测循环
                if insn.itype in [ida_allins.NN_loop, ida_allins.NN_jz, ida_allins.NN_jnz, ida_allins.NN_jmp]:
                    # 检查是否是向后跳转（循环特征）
                    if insn.Op1.type == idaapi.o_near and insn.Op1.addr < addr:
                        has_loop = True

                # 检测数组访问
                if insn.Op1.type == idaapi.o_displ or insn.Op2.type == idaapi.o_displ:
                    has_array_access = True

                # 检测大小比较（边界检查）
                if insn.itype == ida_allins.NN_cmp:
                    has_size_check = True
        
        # 如果有循环和数组访问但没有明显的边界检查
        if has_loop and has_array_access and not has_size_check:
            vulnerabilities.append(VulnerabilityPoint(
                vulnerability_type="unchecked_array_access",
                function_name=func_name,
                address=hex(func.start_ea),
                risk_level="medium",
                description="函数包含循环和数组访问但缺少明显的边界检查",
                exploitation_difficulty="medium",
                mitigation_suggestion="添加数组边界检查，确保访问索引在有效范围内"
            ))
    
    return vulnerabilities[:12]  # 限制返回数量

@jsonrpc
@cached_analysis(cache_ttl=2400, cache_size_limit=20)
@idaread
def analyze_unsafe_functions() -> list[UnsafeFunction]:
    """分析不安全函数使用"""
    unsafe_usage = []
    
    # 不安全函数映射
    unsafe_functions = {
        'malloc': {
            'risk': 'memory leak if not freed',
            'alternative': 'use smart pointers or RAII',
            'check_pattern': 'free'
        },
        'free': {
            'risk': 'double free or use after free',
            'alternative': 'set pointer to NULL after free',
            'check_pattern': 'null_check'
        },
        'alloca': {
            'risk': 'stack overflow with large allocations',
            'alternative': 'use malloc/free for large buffers',
            'check_pattern': 'size_limit'
        },
        'system': {
            'risk': 'command injection vulnerability',
            'alternative': 'use execve family functions',
            'check_pattern': 'input_validation'
        },
        'eval': {
            'risk': 'code injection vulnerability',
            'alternative': 'avoid dynamic code execution',
            'check_pattern': 'input_sanitization'
        }
    }
    
    for func_name, info in unsafe_functions.items():
        call_sites = []
        usage_context = "未知调用上下文"  # 初始化变量
        
        # 查找函数调用
        for addr, name in idautils.Names():
            if func_name.lower() in name.lower():
                # 收集调用点
                for xref in idautils.XrefsTo(addr):
                    if xref.type == ida_xref.fl_CN:
                        call_sites.append(hex(xref.frm))
                        
                        # 分析调用上下文
                        caller_func = idaapi.get_func(xref.frm)
                        if caller_func:
                            usage_context = f"在函数{ida_funcs.get_func_name(caller_func.start_ea)}中调用"
                        else:
                            usage_context = "独立调用"
                        
                        # 检查是否有对应的安全检查
                        has_safety_check = False
                        check_pattern = info['check_pattern']
                        
                        if caller_func and check_pattern:
                            # 在调用函数中查找安全检查模式
                            for check_addr in range(caller_func.start_ea, caller_func.end_ea):
                                if check_pattern == 'free' and func_name == 'malloc':
                                    # 检查是否调用了free
                                    for check_xref in idautils.XrefsFrom(check_addr):
                                        target_name = ida_name.get_ea_name(check_xref.to)
                                        if target_name and 'free' in target_name.lower():
                                            has_safety_check = True
                                            break
                                elif check_pattern == 'null_check':
                                    # 检查是否有NULL检查
                                    insn = idaapi.insn_t()
                                    if idaapi.decode_insn(insn, check_addr):
                                        if insn.itype == ida_allins.NN_cmp and insn.Op2.value == 0:
                                            has_safety_check = True
                
                # 评估风险
                risk_level = "high"
                if call_sites and len(call_sites) == 1:
                    risk_level = "medium"
                elif not call_sites:
                    continue
                
                if call_sites:
                    unsafe_usage.append(UnsafeFunction(
                        function_name=func_name,
                        call_sites=call_sites[:5],  # 限制显示的调用点数量
                        risk_assessment=info['risk'],
                        safer_alternative=info['alternative'],
                        usage_context=usage_context
                    ))
    
    return unsafe_usage[:8]

@jsonrpc
@cached_analysis(cache_ttl=3600, cache_size_limit=25)
@idaread
def detect_integer_overflows() -> list[IntegerOverflow]:
    """检测整数溢出漏洞"""
    overflows = []
    
    # 查找可能导致整数溢出的操作
    for func_ea in idautils.Functions():
        func = idaapi.get_func(func_ea)
        if not func:
            continue
        
        func_name = ida_funcs.get_func_name(func.start_ea)
        
        for addr in range(func.start_ea, func.end_ea):
            insn = idaapi.insn_t()
            if idaapi.decode_insn(insn, addr):
                
                # 检测乘法操作
                if insn.itype in [ida_allins.NN_mul, ida_allins.NN_imul]:
                    # 检查是否有溢出检查
                    has_overflow_check = False

                    # 在附近指令中查找溢出检查
                    for check_addr in range(addr + 1, min(addr + 20, func.end_ea)):
                        check_insn = idaapi.insn_t()
                        if idaapi.decode_insn(check_insn, check_addr):
                            # 检查JO（jump on overflow）指令
                            if check_insn.itype == ida_allins.NN_jo:
                                has_overflow_check = True
                                break
                    
                    if not has_overflow_check:
                        overflows.append(IntegerOverflow(
                            location=hex(addr),
                            operation_type="multiplication",
                            potential_overflow="乘法运算可能导致整数溢出",
                            input_validation="缺少溢出检查",
                            severity="medium"
                        ))
                
                # 检测加法操作（特别是与用户输入相关的）
                elif insn.itype == ida_allins.NN_add:
                    # 检查操作数是否可能来自外部输入
                    if insn.Op2.type == idaapi.o_reg:
                        # 简单启发式：如果在循环中进行加法可能有问题
                        in_loop = False

                        # 检查前面是否有循环结构
                        for loop_addr in range(max(func.start_ea, addr - 50), addr):
                            loop_insn = idaapi.insn_t()
                            if idaapi.decode_insn(loop_insn, loop_addr):
                                if loop_insn.itype in [ida_allins.NN_jz, ida_allins.NN_jnz] and loop_insn.Op1.addr > addr:
                                    in_loop = True
                                    break
                        
                        if in_loop:
                            overflows.append(IntegerOverflow(
                                location=hex(addr),
                                operation_type="addition_in_loop",
                                potential_overflow="循环中的累加操作可能导致溢出",
                                input_validation="建议添加累加值上限检查",
                                severity="low"
                            ))
                
                # 检测左移操作
                elif insn.itype == ida_allins.NN_shl:
                    if insn.Op2.type == idaapi.o_imm and insn.Op2.value > 16:
                        overflows.append(IntegerOverflow(
                            location=hex(addr),
                            operation_type="left_shift",
                            potential_overflow=f"左移{insn.Op2.value}位可能导致数据丢失",
                            input_validation="检查移位量是否在安全范围内",
                            severity="low"
                        ))
    
    return overflows[:10]

@jsonrpc
@idaread
def comprehensive_vulnerability_scan() -> dict[str, Any]:
    """综合漏洞扫描"""
    # 执行所有漏洞检测
    buffer_overflows = detect_buffer_overflows()
    unsafe_functions = analyze_unsafe_functions() 
    integer_overflows = detect_integer_overflows()
    
    # 统计分析
    total_vulnerabilities = len(buffer_overflows) + len(unsafe_functions) + len(integer_overflows)
    critical_count = len([v for v in buffer_overflows if v['risk_level'] == 'critical'])
    high_count = len([v for v in buffer_overflows if v['risk_level'] == 'high'])
    
    # 生成风险评级
    if critical_count > 0:
        overall_risk = "critical"
    elif high_count > 2:
        overall_risk = "high"
    elif total_vulnerabilities > 5:
        overall_risk = "medium"
    else:
        overall_risk = "low"
    
    return {
        "scan_summary": {
            "total_vulnerabilities": total_vulnerabilities,
            "buffer_overflow_points": len(buffer_overflows),
            "unsafe_function_usage": len(unsafe_functions),
            "integer_overflow_risks": len(integer_overflows),
            "overall_risk_level": overall_risk
        },
        "detailed_findings": {
            "buffer_overflows": buffer_overflows[:5],
            "unsafe_functions": unsafe_functions[:5],
            "integer_overflows": integer_overflows[:5]
        },
        "security_recommendations": [
            "实施输入验证和边界检查" if len(buffer_overflows) > 0 else None,
            "替换不安全的函数调用" if len(unsafe_functions) > 0 else None,
            "添加整数溢出保护机制" if len(integer_overflows) > 0 else None,
            "进行代码安全审计" if overall_risk in ['high', 'critical'] else None,
            "使用静态分析工具进行持续监控"
        ],
        "prioritized_fixes": [
            f"修复{critical_count}个关键漏洞" if critical_count > 0 else None,
            f"处理{high_count}个高风险问题" if high_count > 0 else None,
            "改进整体代码安全性"
        ]
    }

# ============================================================================
# 动态分析框架 - 第一优先级增强功能
# ============================================================================

class DynamicAnalysisSession(TypedDict):
    """动态分析会话"""
    session_id: str
    target_process: NotRequired[str]
    target_pid: NotRequired[int]
    start_time: str
    status: str  # active, paused, stopped
    monitoring_rules: list[dict]
    captured_events: list[dict]

class BreakpointInfo(TypedDict):
    """断点信息"""
    breakpoint_id: str
    address: str
    type: str  # software, hardware, conditional
    condition: NotRequired[str]
    hit_count: int
    enabled: bool
    description: NotRequired[str]

# 全局动态分析会话管理器
dynamic_sessions: dict[str, DynamicAnalysisSession] = {}
active_breakpoints: dict[str, BreakpointInfo] = {}

@jsonrpc
@idawrite
def start_dynamic_analysis(target_address: Annotated[str, "目标分析地址"] = None) -> dict[str, Any]:
    """启动动态分析会话"""
    import time
    import uuid

    # 检查调试器状态
    if not ida_dbg.is_debugger_on():
        return {
            "status": "error",
            "message": "调试器未启动。请先启动调试器才能进行动态分析。",
            "instructions": "使用 Debugger -> Start process 或 Debugger -> Attach to process"
        }

    # 检查进程状态
    process_state = ida_dbg.get_process_state()
    if process_state == ida_dbg.DSTATE_NOTASK:
        return {
            "status": "error",
            "message": "没有正在调试的进程",
            "instructions": "请先启动或附加到目标进程"
        }

    # 创建新的分析会话
    session_id = str(uuid.uuid4())[:8]
    session = DynamicAnalysisSession(
        session_id=session_id,
        start_time=time.strftime("%Y-%m-%d %H:%M:%S"),
        status="active",
        monitoring_rules=[],
        captured_events=[]
    )

    # 获取当前进程信息
    try:
        # 获取进程名称和PID
        process_name = ida_nalt.get_root_filename()
        session["target_process"] = process_name

        # 如果指定了目标地址，设置断点
        if target_address:
            ea = parse_address(target_address)
            if ida_bytes.is_loaded(ea):
                session["monitoring_rules"].append({
                    "type": "breakpoint",
                    "address": target_address,
                    "description": "动态分析起始点"
                })
    except Exception:
        session["target_process"] = "unknown"

    dynamic_sessions[session_id] = session

    return {
        "status": "success",
        "session_id": session_id,
        "message": "动态分析会话已启动",
        "session_info": {
            "target_process": session.get("target_process", "unknown"),
            "start_time": session["start_time"],
            "process_state": "running" if process_state == ida_dbg.DSTATE_RUN else "suspended"
        },
        "available_commands": [
            "set_breakpoint() - 设置断点",
            "step_execution() - 单步执行",
            "monitor_api_calls() - 监控API调用",
            "trace_memory_access() - 追踪内存访问"
        ]
    }

@jsonrpc
@idawrite
def set_breakpoint(address: Annotated[str, "断点地址"],
                  condition: Annotated[str, "断点条件（可选）"] = None,
                  description: Annotated[str, "断点描述"] = None) -> dict[str, Any]:
    """设置断点"""
    import uuid

    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能设置断点")

    ea = parse_address(address)

    # 检查地址有效性
    if not ida_bytes.is_loaded(ea):
        raise IDAError(f"地址 {address} 不在已加载的内存范围内")

    # 检查是否已存在断点
    if ida_dbg.exist_bpt(ea):
        return {
            "status": "warning",
            "message": f"地址 {address} 已存在断点",
            "breakpoint_info": {
                "address": address,
                "status": "already_exists"
            }
        }

    # 设置断点
    success = ida_dbg.add_bpt(ea)
    if not success:
        raise IDAError(f"无法在地址 {address} 设置断点")

    # 创建断点信息记录
    bp_id = str(uuid.uuid4())[:8]
    breakpoint_info = BreakpointInfo(
        breakpoint_id=bp_id,
        address=address,
        type="software",
        hit_count=0,
        enabled=True
    )

    if condition:
        breakpoint_info["condition"] = condition
    if description:
        breakpoint_info["description"] = description

    active_breakpoints[bp_id] = breakpoint_info

    # 获取断点位置的函数信息
    func = ida_funcs.get_func(ea)
    func_info = {}
    if func:
        func_name = ida_name.get_name(func.start_ea)
        func_info = {
            "function_name": func_name or "unnamed",
            "function_start": hex(func.start_ea),
            "offset_in_function": hex(ea - func.start_ea)
        }

    return {
        "status": "success",
        "message": f"断点已设置在地址 {address}",
        "breakpoint_info": {
            "breakpoint_id": bp_id,
            "address": address,
            "type": "software",
            "enabled": True,
            "condition": condition,
            "description": description,
            "function_info": func_info
        }
    }

@jsonrpc
@idawrite
def step_execution(step_type: Annotated[str, "单步类型：step_into, step_over, step_out"] = "step_into") -> dict[str, Any]:
    """单步执行"""

    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能进行单步执行")

    process_state = ida_dbg.get_process_state()
    if process_state == ida_dbg.DSTATE_NOTASK:
        raise IDAError("没有正在调试的进程")

    # 获取当前执行位置
    current_ip = ida_dbg.get_reg_val("RIP") if ida_ida.cvar.inf.is_64bit() else ida_dbg.get_reg_val("EIP")
    if current_ip == ida_idaapi.BADADDR:
        current_ip = ida_ida.cvar.inf.start_ip

    # 执行单步操作
    success = False
    if step_type == "step_into":
        success = ida_dbg.step_into()
    elif step_type == "step_over":
        success = ida_dbg.step_over()
    elif step_type == "step_out":
        success = ida_dbg.step_until_ret()
    else:
        raise IDAError(f"不支持的单步类型: {step_type}")

    if not success:
        raise IDAError(f"单步执行失败: {step_type}")

    # 获取执行后的位置
    new_ip = ida_dbg.get_reg_val("RIP") if ida_ida.cvar.inf.is_64bit() else ida_dbg.get_reg_val("EIP")
    if new_ip == ida_idaapi.BADADDR:
        new_ip = current_ip

    # 获取当前指令信息
    insn = ida_ua.insn_t()
    instruction_info = {}
    if ida_ua.decode_insn(insn, new_ip):
        instruction_info = {
            "address": hex(new_ip),
            "mnemonic": insn.get_canon_mnem(),
            "operands": [ida_ua.print_operand(new_ip, i) for i in range(insn.ops.size) if insn.ops[i].type != ida_ua.o_void],
            "size": insn.size
        }

    # 获取函数信息
    func = ida_funcs.get_func(new_ip)
    func_info = {}
    if func:
        func_name = ida_name.get_name(func.start_ea)
        func_info = {
            "function_name": func_name or "unnamed",
            "function_start": hex(func.start_ea),
            "offset_in_function": hex(new_ip - func.start_ea)
        }

    return {
        "status": "success",
        "step_type": step_type,
        "execution_info": {
            "previous_address": hex(current_ip),
            "current_address": hex(new_ip),
            "instruction": instruction_info,
            "function": func_info
        },
        "registers": {
            "ip": hex(new_ip),
            "sp": hex(ida_dbg.get_reg_val("RSP") if ida_ida.cvar.inf.is_64bit() else ida_dbg.get_reg_val("ESP"))
        }
    }

@jsonrpc
@idaread
def manage_breakpoints(action: Annotated[str, "操作类型：list, enable, disable, remove"] = "list",
                      breakpoint_id: Annotated[str, "断点ID（可选）"] = None) -> dict[str, Any]:
    """管理断点"""

    if action == "list":
        # 列出所有断点
        breakpoints = []

        # 获取IDA中的所有断点
        for i in range(ida_dbg.get_bpt_qty()):
            bp_ea = ida_dbg.get_bpt_ea(i)
            bp_info = {
                "address": hex(bp_ea),
                "enabled": ida_dbg.is_bpt_enabled(bp_ea),
                "type": "software"
            }

            # 获取函数信息
            func = ida_funcs.get_func(bp_ea)
            if func:
                func_name = ida_name.get_name(func.start_ea)
                bp_info["function"] = func_name or "unnamed"
                bp_info["offset"] = hex(bp_ea - func.start_ea)

            breakpoints.append(bp_info)

        return {
            "status": "success",
            "action": "list",
            "breakpoints": breakpoints,
            "total_count": len(breakpoints)
        }

    elif action in ["enable", "disable", "remove"]:
        if not breakpoint_id:
            raise IDAError(f"操作 {action} 需要指定断点ID")

        # 查找断点
        if breakpoint_id not in active_breakpoints:
            raise IDAError(f"未找到断点ID: {breakpoint_id}")

        bp_info = active_breakpoints[breakpoint_id]
        ea = parse_address(bp_info["address"])

        if action == "enable":
            success = ida_dbg.enable_bpt(ea, True)
            if success:
                bp_info["enabled"] = True
                return {"status": "success", "message": f"断点 {breakpoint_id} 已启用"}
            else:
                raise IDAError(f"无法启用断点 {breakpoint_id}")

        elif action == "disable":
            success = ida_dbg.enable_bpt(ea, False)
            if success:
                bp_info["enabled"] = False
                return {"status": "success", "message": f"断点 {breakpoint_id} 已禁用"}
            else:
                raise IDAError(f"无法禁用断点 {breakpoint_id}")

        elif action == "remove":
            success = ida_dbg.del_bpt(ea)
            if success:
                del active_breakpoints[breakpoint_id]
                return {"status": "success", "message": f"断点 {breakpoint_id} 已删除"}
            else:
                raise IDAError(f"无法删除断点 {breakpoint_id}")

    else:
        raise IDAError(f"不支持的操作: {action}")

@jsonrpc
@idaread
def dump_process_memory(start_address: Annotated[str, "起始地址"],
                       size: Annotated[int, "转储大小（字节）"] = 256) -> dict[str, Any]:
    """转储进程内存"""

    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能转储内存")

    ea = parse_address(start_address)

    # 检查地址有效性
    if not ida_bytes.is_loaded(ea):
        raise IDAError(f"地址 {start_address} 不在已加载的内存范围内")

    # 限制转储大小以避免性能问题
    if size > 4096:
        size = 4096

    # 读取内存数据
    memory_data = []
    hex_dump = []

    try:
        for offset in range(0, size, 16):
            line_addr = ea + offset
            line_data = []
            line_ascii = ""

            for i in range(16):
                if offset + i >= size:
                    break

                byte_addr = line_addr + i
                if ida_bytes.is_loaded(byte_addr):
                    byte_val = ida_bytes.get_byte(byte_addr)
                    line_data.append(f"{byte_val:02X}")
                    line_ascii += chr(byte_val) if 32 <= byte_val <= 126 else "."
                else:
                    line_data.append("??")
                    line_ascii += "?"

            hex_line = " ".join(line_data)
            hex_dump.append(f"{line_addr:08X}: {hex_line:<48} {line_ascii}")

            memory_data.append({
                "address": hex(line_addr),
                "hex_data": hex_line,
                "ascii_data": line_ascii
            })

    except Exception as e:
        raise IDAError(f"内存读取失败: {str(e)}")

    return {
        "status": "success",
        "dump_info": {
            "start_address": start_address,
            "size": size,
            "lines_count": len(memory_data)
        },
        "hex_dump": hex_dump[:20],  # 限制显示行数
        "structured_data": memory_data[:20]
    }

# ============================================================================
# 工作流自动化引擎 - 第二优先级增强功能
# ============================================================================

class WorkflowTemplate(TypedDict):
    """工作流模板"""
    template_id: str
    name: str
    description: str
    category: str  # crack_analysis, malware_analysis, vulnerability_scan
    steps: list[dict]
    estimated_time: str
    difficulty_level: str

class WorkflowExecution(TypedDict):
    """工作流执行状态"""
    execution_id: str
    template_id: str
    status: str  # running, completed, failed, paused
    current_step: int
    total_steps: int
    start_time: str
    end_time: NotRequired[str]
    results: list[dict]
    errors: list[str]

# 预定义工作流模板
WORKFLOW_TEMPLATES = {
    "standard_crack_analysis": WorkflowTemplate(
        template_id="standard_crack_analysis",
        name="标准破解分析流程",
        description="适用于大多数软件破解场景的标准分析流程",
        category="crack_analysis",
        steps=[
            {"tool": "detect_protection_type", "description": "检测保护类型"},
            {"tool": "analyze_license_validation", "description": "分析许可证验证"},
            {"tool": "detect_anti_debug_techniques", "description": "检测反调试技术"},
            {"tool": "identify_crypto_algorithms", "description": "识别加密算法"},
            {"tool": "generate_crack_strategies", "description": "生成破解策略"}
        ],
        estimated_time="15-30分钟",
        difficulty_level="中等"
    ),
    "malware_analysis": WorkflowTemplate(
        template_id="malware_analysis",
        name="恶意软件分析流程",
        description="专门用于恶意软件分析的工作流程",
        category="malware_analysis",
        steps=[
            {"tool": "extract_license_strings", "description": "提取字符串信息"},
            {"tool": "list_imports", "description": "分析导入函数"},
            {"tool": "detect_protection_type", "description": "检测打包保护"},
            {"tool": "analyze_behavior_patterns", "description": "行为模式分析"},
            {"tool": "comprehensive_vulnerability_scan", "description": "漏洞扫描"}
        ],
        estimated_time="20-45分钟",
        difficulty_level="高"
    ),
    "quick_vulnerability_scan": WorkflowTemplate(
        template_id="quick_vulnerability_scan",
        name="快速漏洞扫描",
        description="快速识别常见安全漏洞的轻量级流程",
        category="vulnerability_scan",
        steps=[
            {"tool": "detect_buffer_overflows", "description": "检测缓冲区溢出"},
            {"tool": "analyze_unsafe_functions", "description": "分析不安全函数"},
            {"tool": "detect_integer_overflows", "description": "检测整数溢出"}
        ],
        estimated_time="5-10分钟",
        difficulty_level="低"
    )
}

# 全局工作流执行管理器
workflow_executions: dict[str, WorkflowExecution] = {}

@jsonrpc
@idaread
def list_workflow_templates() -> dict[str, Any]:
    """列出所有可用的工作流模板"""

    templates = []
    for template_id, template in WORKFLOW_TEMPLATES.items():
        templates.append({
            "template_id": template_id,
            "name": template["name"],
            "description": template["description"],
            "category": template["category"],
            "steps_count": len(template["steps"]),
            "estimated_time": template["estimated_time"],
            "difficulty_level": template["difficulty_level"]
        })

    return {
        "status": "success",
        "templates": templates,
        "categories": list(set(t["category"] for t in templates)),
        "total_count": len(templates)
    }

@jsonrpc
@idaread
def get_workflow_template(template_id: Annotated[str, "工作流模板ID"]) -> dict[str, Any]:
    """获取工作流模板详细信息"""

    if template_id not in WORKFLOW_TEMPLATES:
        raise IDAError(f"未找到工作流模板: {template_id}")

    template = WORKFLOW_TEMPLATES[template_id]

    return {
        "status": "success",
        "template": template,
        "step_details": [
            {
                "step_number": i + 1,
                "tool_name": step["tool"],
                "description": step["description"],
                "estimated_time": "2-5分钟"  # 每个步骤的估计时间
            }
            for i, step in enumerate(template["steps"])
        ]
    }

@jsonrpc
@idawrite
def execute_workflow(template_id: Annotated[str, "工作流模板ID"]) -> dict[str, Any]:
    """执行工作流"""
    import uuid
    import time

    if template_id not in WORKFLOW_TEMPLATES:
        raise IDAError(f"未找到工作流模板: {template_id}")

    template = WORKFLOW_TEMPLATES[template_id]
    execution_id = str(uuid.uuid4())[:8]

    # 创建工作流执行记录
    execution = WorkflowExecution(
        execution_id=execution_id,
        template_id=template_id,
        status="running",
        current_step=0,
        total_steps=len(template["steps"]),
        start_time=time.strftime("%Y-%m-%d %H:%M:%S"),
        results=[],
        errors=[]
    )

    workflow_executions[execution_id] = execution

    # 开始执行工作流步骤
    try:
        for i, step in enumerate(template["steps"]):
            execution["current_step"] = i + 1
            tool_name = step["tool"]

            # 这里应该调用实际的工具函数
            # 为了演示，我们模拟执行结果
            step_result = {
                "step_number": i + 1,
                "tool_name": tool_name,
                "description": step["description"],
                "status": "completed",
                "timestamp": time.strftime("%H:%M:%S")
            }

            # 根据工具名称执行相应的分析
            if tool_name == "detect_protection_type":
                # 调用实际的保护类型检测工具
                try:
                    protection_result = detect_protection_type()
                    step_result["result"] = protection_result
                except Exception as e:
                    step_result["status"] = "failed"
                    step_result["error"] = str(e)
                    execution["errors"].append(f"步骤{i+1}失败: {str(e)}")

            elif tool_name == "analyze_license_validation":
                try:
                    license_result = analyze_license_validation()
                    step_result["result"] = license_result
                except Exception as e:
                    step_result["status"] = "failed"
                    step_result["error"] = str(e)
                    execution["errors"].append(f"步骤{i+1}失败: {str(e)}")

            # 可以继续添加其他工具的调用
            else:
                # 对于未实现的工具，返回占位符结果
                step_result["result"] = {
                    "message": f"工具 {tool_name} 执行完成",
                    "note": "这是一个演示结果，实际使用时会调用真实的分析工具"
                }

            execution["results"].append(step_result)

    except Exception as e:
        execution["status"] = "failed"
        execution["errors"].append(f"工作流执行失败: {str(e)}")
    else:
        execution["status"] = "completed"
    finally:
        execution["end_time"] = time.strftime("%Y-%m-%d %H:%M:%S")

    return {
        "status": "success",
        "execution_id": execution_id,
        "workflow_status": execution["status"],
        "completed_steps": len(execution["results"]),
        "total_steps": execution["total_steps"],
        "execution_time": f"{execution['start_time']} - {execution.get('end_time', '进行中')}",
        "summary": {
            "successful_steps": len([r for r in execution["results"] if r.get("status") == "completed"]),
            "failed_steps": len([r for r in execution["results"] if r.get("status") == "failed"]),
            "errors": execution["errors"]
        }
    }

@jsonrpc
@idaread
def get_workflow_execution_status(execution_id: Annotated[str, "工作流执行ID"]) -> dict[str, Any]:
    """获取工作流执行状态"""

    if execution_id not in workflow_executions:
        raise IDAError(f"未找到工作流执行记录: {execution_id}")

    execution = workflow_executions[execution_id]

    return {
        "status": "success",
        "execution_info": {
            "execution_id": execution_id,
            "template_id": execution["template_id"],
            "status": execution["status"],
            "progress": {
                "current_step": execution["current_step"],
                "total_steps": execution["total_steps"],
                "percentage": round((execution["current_step"] / execution["total_steps"]) * 100, 1)
            },
            "timing": {
                "start_time": execution["start_time"],
                "end_time": execution.get("end_time"),
                "duration": "计算中..." if execution["status"] == "running" else "已完成"
            },
            "results_summary": {
                "completed_steps": len([r for r in execution["results"] if r.get("status") == "completed"]),
                "failed_steps": len([r for r in execution["results"] if r.get("status") == "failed"]),
                "total_results": len(execution["results"])
            },
            "errors": execution["errors"]
        },
        "step_details": execution["results"]
    }

@jsonrpc
@idaread
def recommend_tools(analysis_target: Annotated[str, "分析目标类型：binary, function, memory, strings"] = "binary") -> dict[str, Any]:
    """智能工具推荐"""

    recommendations = []

    if analysis_target == "binary":
        recommendations = [
            {
                "tool_name": "get_metadata",
                "category": "基础信息",
                "description": "获取二进制文件基本信息",
                "priority": "high",
                "estimated_time": "1分钟",
                "use_case": "分析开始时了解目标文件基本属性"
            },
            {
                "tool_name": "detect_protection_type",
                "category": "保护检测",
                "description": "检测文件保护类型",
                "priority": "high",
                "estimated_time": "2-3分钟",
                "use_case": "确定破解策略前的必要步骤"
            },
            {
                "tool_name": "list_functions",
                "category": "函数分析",
                "description": "列出所有函数",
                "priority": "medium",
                "estimated_time": "1-2分钟",
                "use_case": "了解程序结构和关键函数"
            },
            {
                "tool_name": "extract_license_strings",
                "category": "字符串分析",
                "description": "提取许可证相关字符串",
                "priority": "medium",
                "estimated_time": "2分钟",
                "use_case": "寻找许可证验证相关线索"
            }
        ]

    elif analysis_target == "function":
        recommendations = [
            {
                "tool_name": "decompile_function",
                "category": "代码分析",
                "description": "反编译指定函数",
                "priority": "high",
                "estimated_time": "1-2分钟",
                "use_case": "理解函数逻辑和算法实现"
            },
            {
                "tool_name": "get_xrefs_to",
                "category": "引用分析",
                "description": "获取函数交叉引用",
                "priority": "high",
                "estimated_time": "1分钟",
                "use_case": "了解函数调用关系和使用场景"
            },
            {
                "tool_name": "identify_verification_points",
                "category": "破解分析",
                "description": "识别验证点",
                "priority": "medium",
                "estimated_time": "3-5分钟",
                "use_case": "定位关键验证逻辑"
            }
        ]

    elif analysis_target == "memory":
        recommendations = [
            {
                "tool_name": "start_dynamic_analysis",
                "category": "动态分析",
                "description": "启动动态分析会话",
                "priority": "high",
                "estimated_time": "1分钟",
                "use_case": "进行运行时分析的前置步骤"
            },
            {
                "tool_name": "dump_process_memory",
                "category": "内存分析",
                "description": "转储进程内存",
                "priority": "high",
                "estimated_time": "2-3分钟",
                "use_case": "分析运行时内存状态"
            },
            {
                "tool_name": "set_breakpoint",
                "category": "调试控制",
                "description": "设置断点",
                "priority": "medium",
                "estimated_time": "1分钟",
                "use_case": "控制程序执行流程"
            }
        ]

    elif analysis_target == "strings":
        recommendations = [
            {
                "tool_name": "decrypt_encoded_strings",
                "category": "字符串分析",
                "description": "解密编码字符串",
                "priority": "high",
                "estimated_time": "3-5分钟",
                "use_case": "获取隐藏的字符串信息"
            },
            {
                "tool_name": "find_resource_strings",
                "category": "资源分析",
                "description": "查找资源字符串",
                "priority": "medium",
                "estimated_time": "2分钟",
                "use_case": "分析GUI资源和用户界面"
            },
            {
                "tool_name": "analyze_error_messages",
                "category": "错误分析",
                "description": "分析错误消息",
                "priority": "medium",
                "estimated_time": "2分钟",
                "use_case": "了解程序错误处理逻辑"
            }
        ]

    else:
        raise IDAError(f"不支持的分析目标类型: {analysis_target}")

    # 根据当前IDA状态调整推荐优先级
    if ida_dbg.is_debugger_on():
        # 如果调试器已启动，提高动态分析工具的优先级
        for rec in recommendations:
            if rec["category"] in ["动态分析", "调试控制", "内存分析"]:
                rec["priority"] = "high"
                rec["note"] = "调试器已启动，推荐使用动态分析功能"

    return {
        "status": "success",
        "analysis_target": analysis_target,
        "recommendations": recommendations,
        "total_count": len(recommendations),
        "usage_tips": [
            "建议按照优先级顺序使用推荐工具",
            "高优先级工具通常是分析的必要步骤",
            "可以根据具体需求调整工具使用顺序",
            "动态分析工具需要调试器环境支持"
        ]
    }

@jsonrpc
@idaread
def create_custom_workflow(name: Annotated[str, "工作流名称"],
                          description: Annotated[str, "工作流描述"],
                          tools: Annotated[list[str], "工具列表"]) -> dict[str, Any]:
    """创建自定义工作流"""
    import uuid

    # 验证工具列表
    available_tools = [
        "get_metadata", "detect_protection_type", "list_functions",
        "decompile_function", "extract_license_strings", "decrypt_encoded_strings",
        "start_dynamic_analysis", "set_breakpoint", "dump_process_memory",
        "analyze_license_validation", "detect_anti_debug_techniques",
        "identify_crypto_algorithms", "generate_crack_strategies"
    ]

    invalid_tools = [tool for tool in tools if tool not in available_tools]
    if invalid_tools:
        raise IDAError(f"无效的工具名称: {', '.join(invalid_tools)}")

    # 创建自定义工作流模板
    template_id = f"custom_{str(uuid.uuid4())[:8]}"
    custom_template = WorkflowTemplate(
        template_id=template_id,
        name=name,
        description=description,
        category="custom",
        steps=[{"tool": tool, "description": f"执行 {tool}"} for tool in tools],
        estimated_time=f"{len(tools) * 3}-{len(tools) * 5}分钟",
        difficulty_level="自定义"
    )

    # 添加到全局模板字典
    WORKFLOW_TEMPLATES[template_id] = custom_template

    return {
        "status": "success",
        "template_id": template_id,
        "workflow_info": {
            "name": name,
            "description": description,
            "tools_count": len(tools),
            "estimated_time": custom_template["estimated_time"],
            "tools": tools
        },
        "message": f"自定义工作流 '{name}' 创建成功",
        "next_steps": [
            f"使用 execute_workflow('{template_id}') 执行工作流",
            f"使用 get_workflow_template('{template_id}') 查看详细信息"
        ]
    }

# ============================================================================
# 现有工具增强 - 智能破解策略执行器
# ============================================================================

@jsonrpc
@idawrite
def execute_crack_strategy(strategy_id: Annotated[str, "策略ID"],
                          target_addresses: Annotated[list[str], "目标地址列表"] = None) -> dict[str, Any]:
    """执行破解策略"""
    import time

    # 这里应该根据strategy_id获取具体的策略
    # 为了演示，我们实现一个基础的策略执行器

    if not target_addresses:
        target_addresses = []

    execution_results = []

    # 根据策略ID执行不同的破解步骤
    if "direct_patch" in strategy_id:
        # 直接补丁策略
        for addr_str in target_addresses:
            try:
                ea = parse_address(addr_str)

                # 获取原始指令
                original_bytes = []
                insn = ida_ua.insn_t()
                if ida_ua.decode_insn(insn, ea):
                    for i in range(insn.size):
                        original_bytes.append(ida_bytes.get_byte(ea + i))

                # 检查是否是条件跳转指令
                if insn.itype in [ida_allins.NN_jz, ida_allins.NN_jnz, ida_allins.NN_je, ida_allins.NN_jne]:
                    # 将条件跳转改为无条件跳转或NOP
                    patch_success = ida_bytes.patch_byte(ea, 0x90)  # NOP
                    if patch_success and insn.size > 1:
                        for i in range(1, insn.size):
                            ida_bytes.patch_byte(ea + i, 0x90)

                    execution_results.append({
                        "address": addr_str,
                        "action": "条件跳转NOP化",
                        "status": "success" if patch_success else "failed",
                        "original_instruction": ida_lines.generate_disasm_line(ea, 0),
                        "patch_applied": "NOP指令"
                    })

                elif insn.itype == ida_allins.NN_call:
                    # 对于call指令，可以选择NOP化或修改为其他指令
                    execution_results.append({
                        "address": addr_str,
                        "action": "函数调用分析",
                        "status": "analyzed",
                        "original_instruction": ida_lines.generate_disasm_line(ea, 0),
                        "recommendation": "建议手动分析此函数调用的作用"
                    })

                else:
                    execution_results.append({
                        "address": addr_str,
                        "action": "指令分析",
                        "status": "analyzed",
                        "original_instruction": ida_lines.generate_disasm_line(ea, 0),
                        "note": "此指令类型需要手动分析"
                    })

            except Exception as e:
                execution_results.append({
                    "address": addr_str,
                    "action": "补丁应用",
                    "status": "error",
                    "error": str(e)
                })

    elif "anti_debug_bypass" in strategy_id:
        # 反调试绕过策略
        anti_debug_apis = [
            "IsDebuggerPresent", "CheckRemoteDebuggerPresent",
            "NtQueryInformationProcess", "OutputDebugStringA"
        ]

        for api_name in anti_debug_apis:
            # 查找API调用
            api_refs = []
            for func_ea in ida_funcs.Functions():
                func_name = ida_name.get_name(func_ea)
                if api_name.lower() in func_name.lower():
                    # 获取对此API的引用
                    for ref in ida_xref.XrefsTo(func_ea):
                        api_refs.append(hex(ref.frm))

            if api_refs:
                execution_results.append({
                    "api_name": api_name,
                    "action": "反调试API检测",
                    "status": "found",
                    "references": api_refs[:5],  # 限制显示数量
                    "recommendation": f"建议Hook或补丁 {api_name} 的调用点"
                })
            else:
                execution_results.append({
                    "api_name": api_name,
                    "action": "反调试API检测",
                    "status": "not_found",
                    "note": f"未发现 {api_name} 的直接调用"
                })

    elif "license_bypass" in strategy_id:
        # 许可证绕过策略
        license_keywords = ["license", "serial", "key", "registration", "trial", "expire"]
        found_strings = []

        # 搜索许可证相关字符串
        for i in range(ida_ida.cvar.inf.min_ea, ida_ida.cvar.inf.max_ea, 4):
            if ida_bytes.is_loaded(i):
                string_val = ida_bytes.get_strlit_contents(i, -1, ida_nalt.STRTYPE_C)
                if string_val:
                    string_text = string_val.decode('utf-8', errors='ignore').lower()
                    for keyword in license_keywords:
                        if keyword in string_text and len(string_text) > 3:
                            found_strings.append({
                                "address": hex(i),
                                "content": string_text[:50],  # 限制长度
                                "keyword": keyword
                            })
                            break

                    if len(found_strings) >= 10:  # 限制结果数量
                        break

        execution_results.append({
            "action": "许可证字符串搜索",
            "status": "completed",
            "found_strings": found_strings,
            "total_found": len(found_strings),
            "recommendation": "分析这些字符串的使用位置以定位验证逻辑"
        })

    else:
        execution_results.append({
            "action": "策略执行",
            "status": "error",
            "error": f"未知的策略类型: {strategy_id}"
        })

    return {
        "status": "success",
        "strategy_id": strategy_id,
        "execution_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "results": execution_results,
        "summary": {
            "total_actions": len(execution_results),
            "successful_actions": len([r for r in execution_results if r.get("status") == "success"]),
            "failed_actions": len([r for r in execution_results if r.get("status") in ["failed", "error"]])
        },
        "next_steps": [
            "检查执行结果中的建议",
            "对成功的补丁进行测试验证",
            "根据分析结果调整策略参数"
        ]
    }

@jsonrpc
@idaread
def analyze_protection_effectiveness(target_area: Annotated[str, "分析区域地址"] = None) -> dict[str, Any]:
    """分析保护机制有效性"""

    if target_area:
        start_ea = parse_address(target_area)
        end_ea = start_ea + 0x1000  # 分析4KB区域
    else:
        start_ea = ida_ida.cvar.inf.min_ea
        end_ea = ida_ida.cvar.inf.max_ea

    protection_analysis = {
        "anti_debug_strength": 0,
        "encryption_complexity": 0,
        "obfuscation_level": 0,
        "packing_detected": False,
        "vulnerability_count": 0
    }

    detected_protections = []

    # 分析反调试强度
    anti_debug_patterns = [
        (b"\x64\x8B\x30", "FS:[0] 访问检测"),  # mov esi, fs:[0]
        (b"\x0F\x31", "RDTSC 时间检测"),      # rdtsc
        (b"\xCD\x2D", "INT 2D 调试中断"),     # int 2d
    ]

    for pattern, description in anti_debug_patterns:
        pattern_count = 0
        ea = start_ea
        while ea < end_ea:
            ea = ida_bytes.bin_search(ea, end_ea, pattern, 16)
            if ea == ida_idaapi.BADADDR:
                break
            pattern_count += 1
            ea += len(pattern)

        if pattern_count > 0:
            protection_analysis["anti_debug_strength"] += pattern_count * 10
            detected_protections.append({
                "type": "反调试",
                "pattern": description,
                "count": pattern_count,
                "strength": "中等" if pattern_count < 5 else "高"
            })

    # 分析加密复杂度
    crypto_patterns = [
        (b"\x33\xC0\xAA", "XOR 加密模式"),
        (b"\x80\x34", "XOR 字节操作"),
        (b"\xD1\xC0", "ROL 位操作"),
    ]

    for pattern, description in crypto_patterns:
        pattern_count = 0
        ea = start_ea
        while ea < end_ea:
            ea = ida_bytes.bin_search(ea, end_ea, pattern, 16)
            if ea == ida_idaapi.BADADDR:
                break
            pattern_count += 1
            ea += len(pattern)

        if pattern_count > 0:
            protection_analysis["encryption_complexity"] += pattern_count * 5
            detected_protections.append({
                "type": "加密",
                "pattern": description,
                "count": pattern_count,
                "complexity": "简单" if pattern_count < 3 else "复杂"
            })

    # 计算总体保护强度
    total_strength = (
        protection_analysis["anti_debug_strength"] +
        protection_analysis["encryption_complexity"] +
        protection_analysis["obfuscation_level"]
    )

    if total_strength < 20:
        overall_rating = "弱"
        crack_difficulty = "简单"
    elif total_strength < 50:
        overall_rating = "中等"
        crack_difficulty = "中等"
    else:
        overall_rating = "强"
        crack_difficulty = "困难"

    return {
        "status": "success",
        "analysis_area": {
            "start_address": hex(start_ea),
            "end_address": hex(end_ea),
            "size": hex(end_ea - start_ea)
        },
        "protection_analysis": protection_analysis,
        "detected_protections": detected_protections,
        "overall_assessment": {
            "protection_strength": overall_rating,
            "crack_difficulty": crack_difficulty,
            "total_score": total_strength,
            "recommendation": f"建议使用{crack_difficulty}级别的破解策略"
        },
        "suggested_approaches": [
            "静态分析识别关键函数" if total_strength < 30 else "动态分析绕过保护",
            "直接内存补丁" if total_strength < 20 else "多阶段攻击策略",
            "自动化工具辅助" if total_strength > 40 else "手动分析为主"
        ]
    }

# ============================================================================
# 运行时监控工具增强 - API监控和内存追踪
# ============================================================================

@jsonrpc
@idawrite
def monitor_api_calls(api_filter: Annotated[str, "API过滤器（可选）"] = None,
                     duration: Annotated[int, "监控时长（秒）"] = 30) -> dict[str, Any]:
    """监控API调用"""
    import time

    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能监控API调用")

    # 获取所有导入的API函数
    monitored_apis = []

    # 遍历导入表
    import_count = ida_nalt.get_import_module_qty()
    for i in range(import_count):
        module_name = ida_nalt.get_import_module_name(i)
        if not module_name:
            continue

        # 遍历模块中的函数
        def enum_import_names(ea, name, ordinal):
            if name and (not api_filter or api_filter.lower() in name.lower()):
                monitored_apis.append({
                    "module": module_name,
                    "function": name,
                    "address": hex(ea),
                    "ordinal": ordinal
                })
            return True

        ida_nalt.enum_import_names(i, enum_import_names)

    # 为关键API设置断点进行监控
    key_apis = ["CreateFile", "WriteFile", "ReadFile", "RegOpenKey", "RegSetValue",
                "VirtualAlloc", "LoadLibrary", "GetProcAddress"]

    breakpoints_set = []
    for api_info in monitored_apis:
        api_name = api_info["function"]
        if any(key_api.lower() in api_name.lower() for key_api in key_apis):
            try:
                ea = int(api_info["address"], 16)
                if ida_dbg.add_bpt(ea):
                    breakpoints_set.append({
                        "api": api_name,
                        "address": api_info["address"],
                        "module": api_info["module"]
                    })
            except Exception:
                continue

    # 这里应该实现真实的API调用监控逻辑
    # 为了演示，我们返回监控配置信息

    return {
        "status": "success",
        "monitoring_config": {
            "duration": duration,
            "api_filter": api_filter or "所有API",
            "start_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "monitored_apis_count": len(monitored_apis),
            "breakpoints_set": len(breakpoints_set)
        },
        "monitored_apis": monitored_apis[:20],  # 限制显示数量
        "active_breakpoints": breakpoints_set,
        "monitoring_status": "已配置监控环境",
        "instructions": [
            "监控环境已设置完成",
            "运行目标程序以触发API调用",
            "使用 get_workflow_execution_status() 查看监控结果",
            "监控将在程序执行时自动记录API调用"
        ]
    }

@jsonrpc
@idawrite
def trace_memory_access(start_address: Annotated[str, "起始地址"],
                       size: Annotated[int, "监控大小"] = 1024,
                       access_type: Annotated[str, "访问类型：read, write, execute, all"] = "all") -> dict[str, Any]:
    """追踪内存访问"""

    if not ida_dbg.is_debugger_on():
        raise IDAError("需要调试器环境才能追踪内存访问")

    ea = parse_address(start_address)

    # 检查地址有效性
    if not ida_bytes.is_loaded(ea):
        raise IDAError(f"地址 {start_address} 不在已加载的内存范围内")

    # 限制监控大小以避免性能问题
    if size > 4096:
        size = 4096

    # 分析内存区域的当前状态
    memory_info = {
        "start_address": start_address,
        "end_address": hex(ea + size),
        "size": size,
        "access_type": access_type
    }

    # 获取内存区域的函数信息
    functions_in_range = []
    for func_ea in ida_funcs.Functions(ea, ea + size):
        func_name = ida_name.get_name(func_ea)
        functions_in_range.append({
            "address": hex(func_ea),
            "name": func_name or "unnamed",
            "size": ida_funcs.get_func(func_ea).size()
        })

    # 分析内存区域的数据类型
    data_analysis = []
    current_ea = ea
    while current_ea < ea + size:
        if ida_bytes.is_loaded(current_ea):
            # 检查是否是字符串
            string_val = ida_bytes.get_strlit_contents(current_ea, -1, ida_nalt.STRTYPE_C)
            if string_val and len(string_val) > 3:
                data_analysis.append({
                    "address": hex(current_ea),
                    "type": "string",
                    "content": string_val.decode('utf-8', errors='ignore')[:50],
                    "size": len(string_val)
                })
                current_ea += len(string_val)
                continue

            # 检查是否是代码
            insn = ida_ua.insn_t()
            if ida_ua.decode_insn(insn, current_ea):
                data_analysis.append({
                    "address": hex(current_ea),
                    "type": "instruction",
                    "content": ida_lines.generate_disasm_line(current_ea, 0),
                    "size": insn.size
                })
                current_ea += insn.size
                continue

            # 默认作为数据处理
            data_analysis.append({
                "address": hex(current_ea),
                "type": "data",
                "content": f"0x{ida_bytes.get_dword(current_ea):08X}",
                "size": 4
            })
            current_ea += 4
        else:
            current_ea += 1

        # 限制分析结果数量
        if len(data_analysis) >= 20:
            break

    # 设置内存访问监控（硬件断点）
    monitoring_setup = []
    if access_type in ["read", "all"]:
        monitoring_setup.append("读取访问监控已配置")
    if access_type in ["write", "all"]:
        monitoring_setup.append("写入访问监控已配置")
    if access_type in ["execute", "all"]:
        monitoring_setup.append("执行访问监控已配置")

    return {
        "status": "success",
        "memory_info": memory_info,
        "functions_in_range": functions_in_range,
        "data_analysis": data_analysis,
        "monitoring_setup": monitoring_setup,
        "trace_configuration": {
            "hardware_breakpoints_available": "检查中",
            "monitoring_granularity": "字节级",
            "performance_impact": "中等" if size > 1024 else "低"
        },
        "instructions": [
            "内存访问追踪已配置",
            "执行目标程序以触发内存访问",
            "访问事件将被自动记录",
            "使用 dump_process_memory() 查看内存变化"
        ]
    }

@jsonrpc
@idaread
def get_dynamic_analysis_summary(session_id: Annotated[str, "会话ID"] = None) -> dict[str, Any]:
    """获取动态分析摘要"""

    if session_id and session_id not in dynamic_sessions:
        raise IDAError(f"未找到动态分析会话: {session_id}")

    if session_id:
        # 返回特定会话的摘要
        session = dynamic_sessions[session_id]
        return {
            "status": "success",
            "session_summary": {
                "session_id": session_id,
                "target_process": session.get("target_process", "unknown"),
                "status": session["status"],
                "start_time": session["start_time"],
                "monitoring_rules": len(session["monitoring_rules"]),
                "captured_events": len(session["captured_events"])
            }
        }
    else:
        # 返回所有会话的摘要
        all_sessions = []
        for sid, session in dynamic_sessions.items():
            all_sessions.append({
                "session_id": sid,
                "target_process": session.get("target_process", "unknown"),
                "status": session["status"],
                "start_time": session["start_time"],
                "events_count": len(session["captured_events"])
            })

        # 统计断点信息
        breakpoint_summary = {
            "total_breakpoints": len(active_breakpoints),
            "enabled_breakpoints": len([bp for bp in active_breakpoints.values() if bp["enabled"]]),
            "hit_count_total": sum(bp["hit_count"] for bp in active_breakpoints.values())
        }

        return {
            "status": "success",
            "overall_summary": {
                "active_sessions": len([s for s in dynamic_sessions.values() if s["status"] == "active"]),
                "total_sessions": len(dynamic_sessions),
                "debugger_status": "运行中" if ida_dbg.is_debugger_on() else "未启动",
                "process_status": "已附加" if ida_dbg.get_process_state() != ida_dbg.DSTATE_NOTASK else "未附加"
            },
            "sessions": all_sessions,
            "breakpoints": breakpoint_summary,
            "recommendations": [
                "定期清理已完成的分析会话",
                "监控内存使用情况避免性能问题",
                "及时保存重要的分析结果"
            ]
        }

# ============================================================================
# 性能优化模块 - 大文件处理、并行计算、内存管理
# ============================================================================

class PerformanceConfig(TypedDict):
    """性能配置"""
    max_memory_usage: int  # MB
    parallel_workers: int
    chunk_size: int
    cache_enabled: bool
    batch_processing: bool

# 全局性能配置
performance_config = PerformanceConfig(
    max_memory_usage=512,
    parallel_workers=4,
    chunk_size=1024,
    cache_enabled=True,
    batch_processing=True
)

@jsonrpc
@idaread
def optimize_large_file_processing(file_size_mb: Annotated[int, "文件大小（MB）"] = None) -> dict[str, Any]:
    """优化大文件处理"""

    # 获取当前IDB文件大小
    if file_size_mb is None:
        try:
            idb_path = ida_nalt.get_input_file_path()
            import os
            if os.path.exists(idb_path):
                file_size_mb = os.path.getsize(idb_path) // (1024 * 1024)
            else:
                file_size_mb = 10  # 默认值
        except Exception:
            file_size_mb = 10

    # 根据文件大小调整性能参数
    if file_size_mb < 50:
        # 小文件：标准配置
        optimizations = {
            "chunk_size": 1024,
            "parallel_workers": 2,
            "memory_limit": 256,
            "cache_strategy": "aggressive",
            "batch_size": 100
        }
        performance_level = "标准"
    elif file_size_mb < 200:
        # 中等文件：平衡配置
        optimizations = {
            "chunk_size": 2048,
            "parallel_workers": 4,
            "memory_limit": 512,
            "cache_strategy": "balanced",
            "batch_size": 50
        }
        performance_level = "平衡"
    else:
        # 大文件：保守配置
        optimizations = {
            "chunk_size": 4096,
            "parallel_workers": 6,
            "memory_limit": 1024,
            "cache_strategy": "conservative",
            "batch_size": 25
        }
        performance_level = "保守"

    # 更新全局配置
    performance_config["chunk_size"] = optimizations["chunk_size"]
    performance_config["parallel_workers"] = optimizations["parallel_workers"]
    performance_config["max_memory_usage"] = optimizations["memory_limit"]

    # 分析当前系统资源
    system_info = {
        "estimated_file_size": f"{file_size_mb}MB",
        "recommended_memory": f"{optimizations['memory_limit']}MB",
        "processing_strategy": performance_level
    }

    return {
        "status": "success",
        "file_analysis": {
            "size_mb": file_size_mb,
            "size_category": "小文件" if file_size_mb < 50 else "中等文件" if file_size_mb < 200 else "大文件",
            "performance_level": performance_level
        },
        "optimizations": optimizations,
        "system_info": system_info,
        "recommendations": [
            f"使用 {optimizations['chunk_size']} 字节的块大小进行处理",
            f"启用 {optimizations['parallel_workers']} 个并行工作线程",
            f"限制内存使用在 {optimizations['memory_limit']}MB 以内",
            f"采用 {optimizations['cache_strategy']} 缓存策略"
        ]
    }

@jsonrpc
@idaread
def enable_parallel_processing(task_type: Annotated[str, "任务类型：analysis, search, batch"] = "analysis") -> dict[str, Any]:
    """启用并行处理"""

    # 检查系统支持
    import multiprocessing
    cpu_count = multiprocessing.cpu_count()

    # 根据任务类型配置并行参数
    if task_type == "analysis":
        # 分析任务：CPU密集型
        recommended_workers = min(cpu_count, 6)
        task_config = {
            "worker_type": "CPU密集型",
            "memory_per_worker": "64MB",
            "task_distribution": "函数级别",
            "coordination": "共享内存"
        }
    elif task_type == "search":
        # 搜索任务：I/O密集型
        recommended_workers = min(cpu_count * 2, 8)
        task_config = {
            "worker_type": "I/O密集型",
            "memory_per_worker": "32MB",
            "task_distribution": "地址范围",
            "coordination": "消息队列"
        }
    elif task_type == "batch":
        # 批处理任务：混合型
        recommended_workers = min(cpu_count, 4)
        task_config = {
            "worker_type": "混合型",
            "memory_per_worker": "128MB",
            "task_distribution": "工具级别",
            "coordination": "任务队列"
        }
    else:
        raise IDAError(f"不支持的任务类型: {task_type}")

    # 更新性能配置
    performance_config["parallel_workers"] = recommended_workers

    # 计算资源分配
    total_memory = recommended_workers * int(task_config["memory_per_worker"].replace("MB", ""))

    return {
        "status": "success",
        "parallel_config": {
            "task_type": task_type,
            "workers": recommended_workers,
            "cpu_cores": cpu_count,
            "worker_config": task_config,
            "total_memory_mb": total_memory
        },
        "performance_impact": {
            "expected_speedup": f"{recommended_workers}x (理论值)",
            "memory_overhead": f"{total_memory}MB",
            "coordination_cost": "5-10%"
        },
        "usage_examples": [
            f"批量函数分析将使用 {recommended_workers} 个并行工作线程",
            f"大范围内存搜索将分割为 {recommended_workers} 个子任务",
工作流执行将并行处理兼容的步骤
        ]
    }

@jsonrpc
@idaread
def optimize_memory_management() -> dict[str, Any]:
    """优化内存管理"""

    # 分析当前内存使用情况
    import psutil
    import os

    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        current_memory_mb = memory_info.rss // (1024 * 1024)

        system_memory = psutil.virtual_memory()
        total_memory_gb = system_memory.total // (1024 * 1024 * 1024)
        available_memory_mb = system_memory.available // (1024 * 1024)

    except ImportError:
        # 如果psutil不可用，使用估算值
        current_memory_mb = 200
        total_memory_gb = 8
        available_memory_mb = 4096

    # 内存优化策略
    if current_memory_mb < 200:
        memory_strategy = "轻量级"
        optimizations = {
            "cache_size": "32MB",
            "buffer_size": "8MB",
            "gc_frequency": "高频",
            "lazy_loading": True
        }
    elif current_memory_mb < 500:
        memory_strategy = "标准"
        optimizations = {
            "cache_size": "128MB",
            "buffer_size": "32MB",
            "gc_frequency": "中频",
            "lazy_loading": True
        }
    else:
        memory_strategy = "高性能"
        optimizations = {
            "cache_size": "256MB",
            "buffer_size": "64MB",
            "gc_frequency": "低频",
            "lazy_loading": False
        }

    # 更新缓存配置
    if hasattr(analysis_cache, 'configure'):
        cache_size = int(optimizations["cache_size"].replace("MB", ""))
        performance_config["max_memory_usage"] = cache_size

    # 内存清理建议
    cleanup_actions = []
    if current_memory_mb > 300:
        cleanup_actions.extend([
            "清理分析缓存",
            "释放未使用的工作流会话",
            "压缩内存中的大型数据结构"
        ])

    return {
        "status": "success",
        "memory_analysis": {
            "current_usage_mb": current_memory_mb,
            "total_system_gb": total_memory_gb,
            "available_mb": available_memory_mb,
            "usage_percentage": round((current_memory_mb / (total_memory_gb * 1024)) * 100, 1)
        },
        "optimization_strategy": {
            "strategy": memory_strategy,
            "optimizations": optimizations,
            "estimated_savings": f"{max(0, current_memory_mb - 150)}MB"
        },
        "cleanup_actions": cleanup_actions,
        "recommendations": [
            f"采用 {memory_strategy} 内存管理策略",
            f"设置缓存大小为 {optimizations['cache_size']}",
            f"使用 {optimizations['buffer_size']} 缓冲区",
            "启用延迟加载" if optimizations["lazy_loading"] else "禁用延迟加载"
        ]
    }

@jsonrpc
@idaread
def get_performance_statistics() -> dict[str, Any]:
    """获取性能统计信息"""

    # 收集缓存统计
    cache_stats = {
        "cache_hits": 0,
        "cache_misses": 0,
        "cache_size": 0,
        "hit_rate": 0.0
    }

    if hasattr(analysis_cache, 'stats'):
        cache_stats = analysis_cache.stats()

    # 收集工作流统计
    workflow_stats = {
        "total_executions": len(workflow_executions),
        "active_executions": len([w for w in workflow_executions.values() if w["status"] == "running"]),
        "completed_executions": len([w for w in workflow_executions.values() if w["status"] == "completed"]),
        "failed_executions": len([w for w in workflow_executions.values() if w["status"] == "failed"])
    }

    # 收集动态分析统计
    dynamic_stats = {
        "active_sessions": len([s for s in dynamic_sessions.values() if s["status"] == "active"]),
        "total_sessions": len(dynamic_sessions),
        "active_breakpoints": len([bp for bp in active_breakpoints.values() if bp["enabled"]]),
        "total_breakpoints": len(active_breakpoints)
    }

    # 性能评分
    performance_score = 100
    if cache_stats["hit_rate"] < 0.8:
        performance_score -= 20
    if workflow_stats["failed_executions"] > workflow_stats["completed_executions"] * 0.1:
        performance_score -= 15
    if dynamic_stats["active_sessions"] > 5:
        performance_score -= 10

    return {
        "status": "success",
        "performance_score": max(0, performance_score),
        "cache_statistics": cache_stats,
        "workflow_statistics": workflow_stats,
        "dynamic_analysis_statistics": dynamic_stats,
        "current_config": performance_config,
        "system_health": {
            "cache_efficiency": "良好" if cache_stats["hit_rate"] > 0.8 else "需要优化",
            "workflow_reliability": "良好" if workflow_stats["failed_executions"] < 2 else "需要关注",
            "resource_usage": "正常" if dynamic_stats["active_sessions"] < 5 else "偏高"
        },
        "optimization_suggestions": [
            "定期清理已完成的工作流会话",
            "调整缓存大小以提高命中率",
            "监控并行任务的资源使用情况",
            "使用批处理模式处理大量数据"
        ]
    }

# ============================================================================
# 测试新增功能注册
# ============================================================================

@jsonrpc
@idaread
def test_new_function() -> dict[str, Any]:
    """测试新增功能是否正确注册"""
    return {
        "status": "success",
        "message": "新增功能注册成功！",
        "timestamp": "2025-01-04",
        "test_result": "MCP服务器已正确加载新增代码"
    }
