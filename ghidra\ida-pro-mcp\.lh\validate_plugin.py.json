{"sourceFile": "validate_plugin.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754225368556, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754225368556, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\n简化的IDA Pro MCP插件功能验证脚本\r\n验证文件大小、函数数量、模块结构等关键指标\r\n\"\"\"\r\n\r\nimport os\r\nimport re\r\nimport sys\r\nfrom pathlib import Path\r\n\r\ndef get_file_size(file_path):\r\n    \"\"\"获取文件大小（KB）\"\"\"\r\n    size_bytes = os.path.getsize(file_path)\r\n    return size_bytes / 1024\r\n\r\ndef count_lines(file_path):\r\n    \"\"\"统计文件行数\"\"\"\r\n    with open(file_path, 'r', encoding='utf-8') as f:\r\n        return len(f.readlines())\r\n\r\ndef count_functions(file_path):\r\n    \"\"\"统计@jsonrpc函数数量\"\"\"\r\n    with open(file_path, 'r', encoding='utf-8') as f:\r\n        content = f.read()\r\n    \r\n    # 统计@jsonrpc装饰器\r\n    jsonrpc_count = len(re.findall(r'@jsonrpc', content))\r\n    \r\n    # 统计函数定义\r\n    func_count = len(re.findall(r'^def\\s+\\w+\\s*\\(', content, re.MULTILINE))\r\n    \r\n    # 统计类定义\r\n    class_count = len(re.findall(r'^class\\s+\\w+', content, re.MULTILINE))\r\n    \r\n    return jsonrpc_count, func_count, class_count\r\n\r\ndef check_imports(file_path):\r\n    \"\"\"检查导入语句\"\"\"\r\n    with open(file_path, 'r', encoding='utf-8') as f:\r\n        content = f.read()\r\n    \r\n    # 统计导入语句\r\n    import_count = len(re.findall(r'^(import|from)\\s+', content, re.MULTILINE))\r\n    \r\n    # 检查关键导入\r\n    key_imports = [\r\n        'json', 'time', 'threading', 'queue', 'hashlib', \r\n        'collections', 'typing', 'functools'\r\n    ]\r\n    \r\n    missing_imports = []\r\n    for imp in key_imports:\r\n        if imp not in content:\r\n            missing_imports.append(imp)\r\n    \r\n    return import_count, missing_imports\r\n\r\ndef check_module_structure(file_path):\r\n    \"\"\"检查模块结构\"\"\"\r\n    with open(file_path, 'r', encoding='utf-8') as f:\r\n        content = f.read()\r\n    \r\n    components = {\r\n        'LazyModuleManager': 'LazyModuleManager' in content,\r\n        'AnalysisCache': 'AnalysisCache' in content,\r\n        'WorkflowEngine': 'WorkflowEngine' in content,\r\n        'RPCRegistry': 'RPCRegistry' in content,\r\n        'JSONRPCRequestHandler': 'JSONRPCRequestHandler' in content,\r\n        'cached_analysis': '@cached_analysis' in content,\r\n        'lazy_init_module': '@lazy_init_module' in content,\r\n    }\r\n    \r\n    return components\r\n\r\ndef check_api_functions(file_path):\r\n    \"\"\"检查关键API函数\"\"\"\r\n    with open(file_path, 'r', encoding='utf-8') as f:\r\n        content = f.read()\r\n    \r\n    # 检查破解分析模块的关键函数\r\n    crack_functions = [\r\n        'detect_protection_type',\r\n        'generate_analysis_strategy', \r\n        'execute_batch_analysis',\r\n        'generate_crack_report',\r\n        'detect_anti_debug_techniques',\r\n        'identify_crypto_algorithms',\r\n        'analyze_license_validation',\r\n        'apply_memory_patch',\r\n        'decrypt_encoded_strings'\r\n    ]\r\n    \r\n    found_functions = []\r\n    missing_functions = []\r\n    \r\n    for func in crack_functions:\r\n        if func in content:\r\n            found_functions.append(func)\r\n        else:\r\n            missing_functions.append(func)\r\n    \r\n    return found_functions, missing_functions\r\n\r\ndef validate_syntax(file_path):\r\n    \"\"\"验证Python语法\"\"\"\r\n    try:\r\n        with open(file_path, 'r', encoding='utf-8') as f:\r\n            source = f.read()\r\n        compile(source, file_path, 'exec')\r\n        return True, None\r\n    except SyntaxError as e:\r\n        return False, str(e)\r\n\r\ndef main():\r\n    \"\"\"主验证函数\"\"\"\r\n    print(\"IDA Pro MCP 插件功能验证\")\r\n    print(\"=\" * 50)\r\n    \r\n    plugin_file = \"src/ida_pro_mcp/mcp-plugin.py\"\r\n    \r\n    if not os.path.exists(plugin_file):\r\n        print(f\"❌ 插件文件不存在: {plugin_file}\")\r\n        return False\r\n    \r\n    # 1. 文件大小检查\r\n    file_size_kb = get_file_size(plugin_file)\r\n    print(f\"📁 文件大小: {file_size_kb:.2f} KB\")\r\n    \r\n    # 2. 代码行数统计\r\n    line_count = count_lines(plugin_file)\r\n    print(f\"📊 代码行数: {line_count}\")\r\n    \r\n    # 3. 语法验证\r\n    syntax_ok, syntax_error = validate_syntax(plugin_file)\r\n    if syntax_ok:\r\n        print(\"✅ Python语法检查: 通过\")\r\n    else:\r\n        print(f\"❌ Python语法错误: {syntax_error}\")\r\n        return False\r\n    \r\n    # 4. 函数统计\r\n    jsonrpc_count, func_count, class_count = count_functions(plugin_file)\r\n    print(f\"🔧 @jsonrpc函数: {jsonrpc_count} 个\")\r\n    print(f\"🔧 总函数数量: {func_count} 个\")\r\n    print(f\"🏗️  类定义数量: {class_count} 个\")\r\n    \r\n    # 5. 导入检查\r\n    import_count, missing_imports = check_imports(plugin_file)\r\n    print(f\"📦 导入语句: {import_count} 个\")\r\n    if missing_imports:\r\n        print(f\"⚠️  缺失关键导入: {missing_imports}\")\r\n    else:\r\n        print(\"✅ 关键导入: 完整\")\r\n    \r\n    # 6. 模块结构检查\r\n    components = check_module_structure(plugin_file)\r\n    print(\"\\n🏗️  核心组件检查:\")\r\n    for component, exists in components.items():\r\n        status = \"✅\" if exists else \"❌\"\r\n        print(f\"   {status} {component}\")\r\n    \r\n    # 7. API函数检查\r\n    found_funcs, missing_funcs = check_api_functions(plugin_file)\r\n    print(f\"\\n🎯 破解分析API: {len(found_funcs)}/{len(found_funcs) + len(missing_funcs)} 个\")\r\n    if missing_funcs:\r\n        print(f\"   ❌ 缺失: {missing_funcs}\")\r\n    else:\r\n        print(\"   ✅ 所有关键API已实现\")\r\n    \r\n    # 8. 综合评估\r\n    print(\"\\n\" + \"=\" * 50)\r\n    print(\"综合评估结果:\")\r\n    \r\n    issues = []\r\n    \r\n    # 检查各项指标\r\n    if not syntax_ok:\r\n        issues.append(\"语法错误\")\r\n    \r\n    if jsonrpc_count < 30:\r\n        issues.append(f\"API函数过少({jsonrpc_count}<30)\")\r\n    \r\n    if len([c for c in components.values() if c]) < 6:\r\n        issues.append(\"核心组件不完整\")\r\n    \r\n    if missing_funcs:\r\n        issues.append(\"关键API缺失\")\r\n    \r\n    if issues:\r\n        print(\"❌ 存在问题:\")\r\n        for issue in issues:\r\n            print(f\"   - {issue}\")\r\n        print(\"\\n建议进行修复后再进行性能测试。\")\r\n        return False\r\n    else:\r\n        print(\"🎉 所有功能验证通过！\")\r\n        print(\"\\n插件特性:\")\r\n        print(f\"   - {jsonrpc_count} 个JSON-RPC API函数\")\r\n        print(f\"   - {len([c for c in components.values() if c])} 个核心组件\")\r\n        print(f\"   - {len(found_funcs)} 个破解分析功能\")\r\n        print(f\"   - {file_size_kb:.1f} KB 紧凑代码\")\r\n        print(f\"   - {line_count} 行高质量代码\")\r\n        \r\n        print(\"\\n✅ 准备就绪，可进行性能优化和部署！\")\r\n        return True\r\n\r\nif __name__ == \"__main__\":\r\n    success = main()\r\n    sys.exit(0 if success else 1)\r\n"}]}