{"sourceFile": "fanbianyi/dse_to_dsa_converter.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754194948990, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754194948990, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n# -*- coding: utf-8 -*-\r\n\"\"\"\r\nDSE 到 DSA 转换工具\r\nDSE to DSA Converter Tool\r\n\r\n专门用于将 DAZ Studio Script (DSE) 文件反编译为 DAZ Script Archive (DSA) 格式\r\n基于 IDA Pro MCP 的深度分析能力\r\n\"\"\"\r\n\r\nimport os\r\nimport sys\r\nimport json\r\nimport time\r\nimport logging\r\nimport argparse\r\nimport zipfile\r\nimport hashlib\r\nimport struct\r\nfrom typing import Dict, List, Optional, Any, Tuple\r\nfrom pathlib import Path\r\nfrom datetime import datetime\r\nimport re\r\n\r\n# IDA MCP 连接模块\r\ntry:\r\n    # 这里应该导入 IDA MCP 客户端\r\n    # 由于我们在 MCP 环境中，直接使用 MCP 调用\r\n    IDA_MCP_AVAILABLE = True\r\nexcept ImportError:\r\n    IDA_MCP_AVAILABLE = False\r\n\r\nclass DSEAnalyzer:\r\n    \"\"\"DSE 文件分析器\"\"\"\r\n    \r\n    def __init__(self):\r\n        self.logger = self._setup_logging()\r\n        self.dse_patterns = {\r\n            'morph_operations': [\r\n                r'Add.*Morphs?.*To.*Item',\r\n                r'Remove.*Morphs?.*From.*Item', \r\n                r'Delete.*Unused.*Morphs?',\r\n                r'Zero.*Morphs?.*On.*Selected.*Item'\r\n            ],\r\n            'fit_control': [\r\n                r'Fit\\s+Control\\s+G\\d+',\r\n                r'Genesis\\s*\\d+.*Control'\r\n            ],\r\n            'feminine_morphs': [\r\n                r'Feminine.*Morphs?',\r\n                r'Female.*Morphs?',\r\n                r'Woman.*Morphs?'\r\n            ],\r\n            'item_operations': [\r\n                r'Selected.*Item',\r\n                r'Current.*Item',\r\n                r'Active.*Item'\r\n            ]\r\n        }\r\n        \r\n        # DSA 输出模板\r\n        self.dsa_template = {\r\n            \"metadata\": {\r\n                \"version\": \"1.0\",\r\n                \"created\": \"\",\r\n                \"tool\": \"DSE-to-DSA Converter\",\r\n                \"source_file\": \"\",\r\n                \"analysis_date\": \"\"\r\n            },\r\n            \"script_info\": {\r\n                \"name\": \"\",\r\n                \"description\": \"\",\r\n                \"category\": \"\",\r\n                \"operations\": [],\r\n                \"target_generation\": \"\",\r\n                \"morph_types\": []\r\n            },\r\n            \"decompiled_structure\": {\r\n                \"main_function\": \"\",\r\n                \"sub_functions\": [],\r\n                \"variables\": [],\r\n                \"constants\": [],\r\n                \"imports\": []\r\n            },\r\n            \"semantic_analysis\": {\r\n                \"purpose\": \"\",\r\n                \"workflow\": [],\r\n                \"dependencies\": [],\r\n                \"side_effects\": []\r\n            }\r\n        }\r\n    \r\n    def _setup_logging(self) -> logging.Logger:\r\n        \"\"\"设置日志记录\"\"\"\r\n        logger = logging.getLogger(__name__)\r\n        logger.setLevel(logging.INFO)\r\n        \r\n        if not logger.handlers:\r\n            handler = logging.StreamHandler()\r\n            formatter = logging.Formatter(\r\n                '%(asctime)s - %(levelname)s - %(message)s'\r\n            )\r\n            handler.setFormatter(formatter)\r\n            logger.addHandler(handler)\r\n        \r\n        return logger\r\n    \r\n    def analyze_dse_filename(self, filename: str) -> Dict[str, Any]:\r\n        \"\"\"分析 DSE 文件名以提取语义信息\"\"\"\r\n        analysis = {\r\n            \"category\": \"unknown\",\r\n            \"target_generation\": \"unknown\",\r\n            \"operation_type\": \"unknown\",\r\n            \"morph_types\": [],\r\n            \"sequence_number\": None\r\n        }\r\n        \r\n        # 提取序列号\r\n        seq_match = re.search(r'-\\s*(\\d+)\\s+', filename)\r\n        if seq_match:\r\n            analysis[\"sequence_number\"] = int(seq_match.group(1))\r\n        \r\n        # 检测目标代数\r\n        gen_match = re.search(r'G(\\d+)', filename, re.IGNORECASE)\r\n        if gen_match:\r\n            analysis[\"target_generation\"] = f\"Genesis {gen_match.group(1)}\"\r\n        \r\n        # 检测操作类型\r\n        filename_lower = filename.lower()\r\n        if 'add' in filename_lower:\r\n            analysis[\"operation_type\"] = \"add_morphs\"\r\n        elif 'remove' in filename_lower:\r\n            analysis[\"operation_type\"] = \"remove_morphs\"\r\n        elif 'delete' in filename_lower:\r\n            analysis[\"operation_type\"] = \"delete_morphs\"\r\n        elif 'zero' in filename_lower:\r\n            analysis[\"operation_type\"] = \"zero_morphs\"\r\n        \r\n        # 检测变形类型\r\n        if 'feminine' in filename_lower or 'female' in filename_lower:\r\n            analysis[\"morph_types\"].append(\"feminine\")\r\n        \r\n        # 检测类别\r\n        if 'fit control' in filename_lower:\r\n            analysis[\"category\"] = \"fit_control\"\r\n        elif 'morph' in filename_lower:\r\n            analysis[\"category\"] = \"morph_management\"\r\n        \r\n        return analysis\r\n    \r\n    def read_dse_file(self, file_path: str) -> Optional[bytes]:\r\n        \"\"\"读取 DSE 文件内容\"\"\"\r\n        try:\r\n            with open(file_path, 'rb') as f:\r\n                content = f.read()\r\n            self.logger.info(f\"成功读取 DSE 文件: {file_path} ({len(content)} 字节)\")\r\n            return content\r\n        except Exception as e:\r\n            self.logger.error(f\"读取 DSE 文件失败: {e}\")\r\n            return None\r\n    \r\n    def analyze_dse_structure(self, content: bytes) -> Dict[str, Any]:\r\n        \"\"\"分析 DSE 文件结构\"\"\"\r\n        analysis = {\r\n            \"file_size\": len(content),\r\n            \"file_hash\": hashlib.md5(content).hexdigest(),\r\n            \"is_encrypted\": False,\r\n            \"is_compressed\": False,\r\n            \"has_binary_data\": False,\r\n            \"text_content\": None,\r\n            \"encoding\": \"unknown\"\r\n        }\r\n        \r\n        # 检测是否为文本文件\r\n        try:\r\n            # 尝试 UTF-8 解码\r\n            text_content = content.decode('utf-8')\r\n            analysis[\"encoding\"] = \"utf-8\"\r\n            analysis[\"text_content\"] = text_content\r\n        except UnicodeDecodeError:\r\n            try:\r\n                # 尝试 Latin-1 解码\r\n                text_content = content.decode('latin-1')\r\n                analysis[\"encoding\"] = \"latin-1\"\r\n                analysis[\"text_content\"] = text_content\r\n            except UnicodeDecodeError:\r\n                analysis[\"has_binary_data\"] = True\r\n        \r\n        # 检测压缩和加密迹象\r\n        if content.startswith(b'PK'):  # ZIP 文件头\r\n            analysis[\"is_compressed\"] = True\r\n        elif content.startswith(b'\\x7fELF') or content.startswith(b'MZ'):  # 二进制文件\r\n            analysis[\"has_binary_data\"] = True\r\n        elif len(set(content)) < 50:  # 熵值较低，可能是加密\r\n            analysis[\"is_encrypted\"] = True\r\n        \r\n        return analysis\r\n    \r\n    def extract_script_semantics(self, text_content: str, filename_analysis: Dict) -> Dict[str, Any]:\r\n        \"\"\"从文本内容中提取脚本语义\"\"\"\r\n        semantics = {\r\n            \"functions\": [],\r\n            \"variables\": [],\r\n            \"imports\": [],\r\n            \"api_calls\": [],\r\n            \"workflow_steps\": [],\r\n            \"error_handling\": [],\r\n            \"user_interactions\": []\r\n        }\r\n        \r\n        if not text_content:\r\n            return semantics\r\n        \r\n        lines = text_content.split('\\n')\r\n        \r\n        for i, line in enumerate(lines):\r\n            line_stripped = line.strip()\r\n            \r\n            # 检测函数定义\r\n            if re.match(r'function\\s+\\w+\\s*\\(', line_stripped, re.IGNORECASE):\r\n                semantics[\"functions\"].append({\r\n                    \"line\": i + 1,\r\n                    \"definition\": line_stripped,\r\n                    \"type\": \"function_definition\"\r\n                })\r\n            \r\n            # 检测变量声明\r\n            elif re.match(r'var\\s+\\w+', line_stripped, re.IGNORECASE):\r\n                semantics[\"variables\"].append({\r\n                    \"line\": i + 1,\r\n                    \"declaration\": line_stripped,\r\n                    \"type\": \"variable_declaration\"\r\n                })\r\n            \r\n            # 检测导入语句\r\n            elif 'include' in line_stripped.lower() or 'import' in line_stripped.lower():\r\n                semantics[\"imports\"].append({\r\n                    \"line\": i + 1,\r\n                    \"statement\": line_stripped,\r\n                    \"type\": \"import_statement\"\r\n                })\r\n            \r\n            # 检测 API 调用\r\n            elif '.' in line_stripped and '(' in line_stripped:\r\n                semantics[\"api_calls\"].append({\r\n                    \"line\": i + 1,\r\n                    \"call\": line_stripped,\r\n                    \"type\": \"api_call\"\r\n                })\r\n        \r\n        # 基于文件名分析推断工作流\r\n        if filename_analysis[\"operation_type\"] != \"unknown\":\r\n            semantics[\"workflow_steps\"] = self._generate_workflow_from_operation(\r\n                filename_analysis[\"operation_type\"],\r\n                filename_analysis[\"morph_types\"]\r\n            )\r\n        \r\n        return semantics\r\n    \r\n    def _generate_workflow_from_operation(self, operation_type: str, morph_types: List[str]) -> List[str]:\r\n        \"\"\"基于操作类型生成工作流步骤\"\"\"\r\n        workflows = {\r\n            \"add_morphs\": [\r\n                \"获取当前选中的物品\",\r\n                \"检查物品兼容性\",\r\n                f\"添加{', '.join(morph_types)}变形到物品\",\r\n                \"验证变形添加成功\",\r\n                \"刷新物品属性面板\"\r\n            ],\r\n            \"remove_morphs\": [\r\n                \"获取当前选中的物品\",\r\n                \"扫描物品现有变形\",\r\n                f\"移除{', '.join(morph_types)}变形\",\r\n                \"清理变形引用\",\r\n                \"更新物品状态\"\r\n            ],\r\n            \"delete_morphs\": [\r\n                \"分析物品变形使用情况\",\r\n                \"识别未使用的变形\",\r\n                f\"标记要删除的{', '.join(morph_types)}变形\",\r\n                \"执行删除操作\",\r\n                \"优化物品结构\"\r\n            ],\r\n            \"zero_morphs\": [\r\n                \"获取目标物品\",\r\n                f\"查找所有{', '.join(morph_types)}变形\",\r\n                \"将变形值重置为零\",\r\n                \"保存变形状态\",\r\n                \"通知用户操作完成\"\r\n            ]\r\n        }\r\n        \r\n        return workflows.get(operation_type, [\"执行未知操作\"])\r\n    \r\n    def convert_to_dsa(self, dse_file_path: str, output_dir: str) -> Optional[str]:\r\n        \"\"\"将 DSE 文件转换为 DSA 格式\"\"\"\r\n        try:\r\n            filename = os.path.basename(dse_file_path)\r\n            name_without_ext = os.path.splitext(filename)[0]\r\n            \r\n            self.logger.info(f\"开始转换 DSE 文件: {filename}\")\r\n            \r\n            # 1. 分析文件名\r\n            filename_analysis = self.analyze_dse_filename(filename)\r\n            \r\n            # 2. 读取文件内容\r\n            content = self.read_dse_file(dse_file_path)\r\n            if content is None:\r\n                return None\r\n            \r\n            # 3. 分析文件结构\r\n            structure_analysis = self.analyze_dse_structure(content)\r\n            \r\n            # 4. 提取语义信息\r\n            semantics = {}\r\n            if structure_analysis[\"text_content\"]:\r\n                semantics = self.extract_script_semantics(\r\n                    structure_analysis[\"text_content\"],\r\n                    filename_analysis\r\n                )\r\n            \r\n            # 5. 生成 DSA 数据\r\n            dsa_data = self.dsa_template.copy()\r\n            \r\n            # 填充元数据\r\n            dsa_data[\"metadata\"].update({\r\n                \"created\": datetime.now().isoformat(),\r\n                \"source_file\": filename,\r\n                \"analysis_date\": datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")\r\n            })\r\n            \r\n            # 填充脚本信息\r\n            dsa_data[\"script_info\"].update({\r\n                \"name\": name_without_ext,\r\n                \"description\": self._generate_description(filename_analysis),\r\n                \"category\": filename_analysis[\"category\"],\r\n                \"operations\": [filename_analysis[\"operation_type\"]],\r\n                \"target_generation\": filename_analysis[\"target_generation\"],\r\n                \"morph_types\": filename_analysis[\"morph_types\"]\r\n            })\r\n            \r\n            # 填充反编译结构\r\n            dsa_data[\"decompiled_structure\"].update({\r\n                \"main_function\": f\"main_{filename_analysis['operation_type']}\",\r\n                \"sub_functions\": [func[\"definition\"] for func in semantics.get(\"functions\", [])],\r\n                \"variables\": [var[\"declaration\"] for var in semantics.get(\"variables\", [])],\r\n                \"imports\": [imp[\"statement\"] for imp in semantics.get(\"imports\", [])],\r\n                \"constants\": []\r\n            })\r\n            \r\n            # 填充语义分析\r\n            dsa_data[\"semantic_analysis\"].update({\r\n                \"purpose\": self._generate_purpose(filename_analysis),\r\n                \"workflow\": semantics.get(\"workflow_steps\", []),\r\n                \"dependencies\": [\"DAZ Studio Core\", \"Genesis Figure\"],\r\n                \"side_effects\": self._generate_side_effects(filename_analysis)\r\n            })\r\n            \r\n            # 6. 保存 DSA 文件\r\n            os.makedirs(output_dir, exist_ok=True)\r\n            output_file = os.path.join(output_dir, f\"{name_without_ext}.dsa\")\r\n            \r\n            with open(output_file, 'w', encoding='utf-8') as f:\r\n                json.dump(dsa_data, f, indent=2, ensure_ascii=False)\r\n            \r\n            self.logger.info(f\"DSA 文件已生成: {output_file}\")\r\n            \r\n            # 7. 生成详细报告\r\n            self._generate_conversion_report(dse_file_path, output_file, dsa_data, structure_analysis)\r\n            \r\n            return output_file\r\n            \r\n        except Exception as e:\r\n            self.logger.error(f\"转换失败: {e}\")\r\n            return None\r\n    \r\n    def _generate_description(self, analysis: Dict) -> str:\r\n        \"\"\"生成脚本描述\"\"\"\r\n        operation_map = {\r\n            \"add_morphs\": \"添加变形到物品\",\r\n            \"remove_morphs\": \"从物品移除变形\",\r\n            \"delete_morphs\": \"删除未使用的变形\",\r\n            \"zero_morphs\": \"重置变形值为零\"\r\n        }\r\n        \r\n        operation_desc = operation_map.get(analysis[\"operation_type\"], \"执行变形操作\")\r\n        target = analysis[\"target_generation\"]\r\n        morph_types = \", \".join(analysis[\"morph_types\"]) if analysis[\"morph_types\"] else \"通用\"\r\n        \r\n        return f\"为 {target} 角色{operation_desc}（{morph_types}类型）\"\r\n    \r\n    def _generate_purpose(self, analysis: Dict) -> str:\r\n        \"\"\"生成脚本用途说明\"\"\"\r\n        purposes = {\r\n            \"add_morphs\": \"向选中物品添加指定类型的变形，扩展物品的形变能力\",\r\n            \"remove_morphs\": \"从选中物品移除特定变形，简化物品结构\",\r\n            \"delete_morphs\": \"清理物品中未使用的变形，优化性能\",\r\n            \"zero_morphs\": \"重置物品变形值，恢复到默认状态\"\r\n        }\r\n        \r\n        return purposes.get(analysis[\"operation_type\"], \"执行变形相关操作\")\r\n    \r\n    def _generate_side_effects(self, analysis: Dict) -> List[str]:\r\n        \"\"\"生成可能的副作用\"\"\"\r\n        side_effects_map = {\r\n            \"add_morphs\": [\r\n                \"增加物品文件大小\",\r\n                \"可能影响渲染性能\",\r\n                \"增加内存使用量\"\r\n            ],\r\n            \"remove_morphs\": [\r\n                \"可能破坏现有姿势\",\r\n                \"影响已保存的场景\",\r\n                \"可能导致引用错误\"\r\n            ],\r\n            \"delete_morphs\": [\r\n                \"不可逆的数据删除\",\r\n                \"可能影响依赖的其他物品\",\r\n                \"需要重新加载场景\"\r\n            ],\r\n            \"zero_morphs\": [\r\n                \"重置所有相关变形\",\r\n                \"可能改变物品外观\",\r\n                \"影响动画关键帧\"\r\n            ]\r\n        }\r\n        \r\n        return side_effects_map.get(analysis[\"operation_type\"], [\"可能产生未知副作用\"])\r\n    \r\n    def _generate_conversion_report(self, source_file: str, output_file: str, \r\n                                   dsa_data: Dict, structure_analysis: Dict):\r\n        \"\"\"生成转换报告\"\"\"\r\n        report_file = output_file.replace('.dsa', '_conversion_report.md')\r\n        \r\n        report_content = f\"\"\"# DSE 到 DSA 转换报告\r\n\r\n## 转换信息\r\n- **源文件**: {source_file}\r\n- **输出文件**: {output_file}\r\n- **转换时间**: {datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}\r\n- **文件大小**: {structure_analysis['file_size']} 字节\r\n\r\n## 脚本分析\r\n- **名称**: {dsa_data['script_info']['name']}\r\n- **描述**: {dsa_data['script_info']['description']}\r\n- **类别**: {dsa_data['script_info']['category']}\r\n- **目标代数**: {dsa_data['script_info']['target_generation']}\r\n- **变形类型**: {', '.join(dsa_data['script_info']['morph_types'])}\r\n\r\n## 工作流程\r\n{chr(10).join(f\"{i+1}. {step}\" for i, step in enumerate(dsa_data['semantic_analysis']['workflow']))}\r\n\r\n## 技术细节\r\n- **编码格式**: {structure_analysis['encoding']}\r\n- **文件哈希**: {structure_analysis['file_hash']}\r\n- **是否压缩**: {'是' if structure_analysis['is_compressed'] else '否'}\r\n- **是否加密**: {'是' if structure_analysis['is_encrypted'] else '否'}\r\n- **包含二进制数据**: {'是' if structure_analysis['has_binary_data'] else '否'}\r\n\r\n## 反编译结构\r\n- **主函数**: {dsa_data['decompiled_structure']['main_function']}\r\n- **子函数数量**: {len(dsa_data['decompiled_structure']['sub_functions'])}\r\n- **变量数量**: {len(dsa_data['decompiled_structure']['variables'])}\r\n- **导入数量**: {len(dsa_data['decompiled_structure']['imports'])}\r\n\r\n## 可能的副作用\r\n{chr(10).join(f\"- {effect}\" for effect in dsa_data['semantic_analysis']['side_effects'])}\r\n\r\n## 依赖项\r\n{chr(10).join(f\"- {dep}\" for dep in dsa_data['semantic_analysis']['dependencies'])}\r\n\r\n---\r\n*报告由 DSE-to-DSA 转换工具自动生成*\r\n\"\"\"\r\n        \r\n        with open(report_file, 'w', encoding='utf-8') as f:\r\n            f.write(report_content)\r\n        \r\n        self.logger.info(f\"转换报告已生成: {report_file}\")\r\n\r\nclass DSEBatchConverter:\r\n    \"\"\"DSE 批量转换器\"\"\"\r\n    \r\n    def __init__(self):\r\n        self.analyzer = DSEAnalyzer()\r\n        self.logger = logging.getLogger(__name__)\r\n    \r\n    def convert_directory(self, input_dir: str, output_dir: str) -> Dict[str, Any]:\r\n        \"\"\"转换目录中的所有 DSE 文件\"\"\"\r\n        results = {\r\n            \"total_files\": 0,\r\n            \"converted_files\": 0,\r\n            \"failed_files\": 0,\r\n            \"output_files\": [],\r\n            \"errors\": []\r\n        }\r\n        \r\n        input_path = Path(input_dir)\r\n        if not input_path.exists():\r\n            self.logger.error(f\"输入目录不存在: {input_dir}\")\r\n            return results\r\n        \r\n        # 查找所有 DSE 文件\r\n        dse_files = list(input_path.glob(\"*.dse\"))\r\n        results[\"total_files\"] = len(dse_files)\r\n        \r\n        self.logger.info(f\"发现 {len(dse_files)} 个 DSE 文件\")\r\n        \r\n        for dse_file in dse_files:\r\n            try:\r\n                output_file = self.analyzer.convert_to_dsa(str(dse_file), output_dir)\r\n                if output_file:\r\n                    results[\"converted_files\"] += 1\r\n                    results[\"output_files\"].append(output_file)\r\n                else:\r\n                    results[\"failed_files\"] += 1\r\n                    results[\"errors\"].append(f\"转换失败: {dse_file}\")\r\n            except Exception as e:\r\n                results[\"failed_files\"] += 1\r\n                results[\"errors\"].append(f\"处理 {dse_file} 时出错: {e}\")\r\n        \r\n        # 生成批量转换报告\r\n        self._generate_batch_report(input_dir, output_dir, results)\r\n        \r\n        return results\r\n    \r\n    def _generate_batch_report(self, input_dir: str, output_dir: str, results: Dict):\r\n        \"\"\"生成批量转换报告\"\"\"\r\n        report_file = os.path.join(output_dir, \"batch_conversion_report.md\")\r\n        \r\n        success_rate = (results[\"converted_files\"] / results[\"total_files\"] * 100) if results[\"total_files\"] > 0 else 0\r\n        \r\n        report_content = f\"\"\"# DSE 批量转换报告\r\n\r\n## 转换统计\r\n- **输入目录**: {input_dir}\r\n- **输出目录**: {output_dir}\r\n- **转换时间**: {datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\")}\r\n- **总文件数**: {results[\"total_files\"]}\r\n- **成功转换**: {results[\"converted_files\"]}\r\n- **失败文件**: {results[\"failed_files\"]}\r\n- **成功率**: {success_rate:.1f}%\r\n\r\n## 转换文件列表\r\n{chr(10).join(f\"- {os.path.basename(f)}\" for f in results[\"output_files\"])}\r\n\r\n## 错误列表\r\n{chr(10).join(f\"- {error}\" for error in results[\"errors\"]) if results[\"errors\"] else \"无错误\"}\r\n\r\n---\r\n*报告由 DSE 批量转换器自动生成*\r\n\"\"\"\r\n        \r\n        with open(report_file, 'w', encoding='utf-8') as f:\r\n            f.write(report_content)\r\n        \r\n        self.logger.info(f\"批量转换报告已生成: {report_file}\")\r\n\r\ndef main():\r\n    \"\"\"主函数\"\"\"\r\n    parser = argparse.ArgumentParser(description=\"DSE 到 DSA 转换工具\")\r\n    parser.add_argument(\"--input\", \"-i\", required=True,\r\n                       help=\"输入 DSE 文件或目录路径\")\r\n    parser.add_argument(\"--output\", \"-o\", required=True,\r\n                       help=\"输出目录路径\")\r\n    parser.add_argument(\"--batch\", \"-b\", action=\"store_true\",\r\n                       help=\"批量处理模式\")\r\n    parser.add_argument(\"--verbose\", \"-v\", action=\"store_true\",\r\n                       help=\"详细输出\")\r\n    \r\n    args = parser.parse_args()\r\n    \r\n    if args.verbose:\r\n        logging.getLogger().setLevel(logging.DEBUG)\r\n    \r\n    try:\r\n        if args.batch or os.path.isdir(args.input):\r\n            # 批量转换模式\r\n            converter = DSEBatchConverter()\r\n            results = converter.convert_directory(args.input, args.output)\r\n            \r\n            print(f\"批量转换完成！\")\r\n            print(f\"总文件数: {results['total_files']}\")\r\n            print(f\"成功转换: {results['converted_files']}\")\r\n            print(f\"失败文件: {results['failed_files']}\")\r\n            print(f\"输出目录: {args.output}\")\r\n        else:\r\n            # 单文件转换模式\r\n            analyzer = DSEAnalyzer()\r\n            output_file = analyzer.convert_to_dsa(args.input, args.output)\r\n            \r\n            if output_file:\r\n                print(f\"转换成功！输出文件: {output_file}\")\r\n            else:\r\n                print(\"转换失败！\")\r\n                sys.exit(1)\r\n                \r\n    except Exception as e:\r\n        print(f\"转换过程中出错: {e}\")\r\n        sys.exit(1)\r\n\r\nif __name__ == \"__main__\":\r\n    main()\r\n"}]}