{"sourceFile": "test_new_features.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754235364289, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754235364289, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\n测试IDA Pro MCP新功能\r\n\"\"\"\r\nimport json\r\nimport subprocess\r\nimport sys\r\n\r\ndef test_mcp_functions():\r\n    \"\"\"测试MCP功能\"\"\"\r\n    \r\n    # 要测试的新功能列表\r\n    test_functions = [\r\n        \"get_cache_statistics\",\r\n        \"get_lazy_module_stats\", \r\n        \"test_lazy_initialization\",\r\n        \"clear_analysis_cache\",\r\n        \"get_workflow_status\",\r\n        \"detect_protection_type\",\r\n        \"analyze_javascript_patterns\",\r\n        \"discover_api_endpoints\",\r\n        \"extract_web_resources\"\r\n    ]\r\n    \r\n    print(\"🧪 测试IDA Pro MCP新功能...\")\r\n    \r\n    for func in test_functions:\r\n        print(f\"\\n📋 测试功能: {func}\")\r\n        \r\n        # 创建JSON-RPC请求\r\n        request = {\r\n            \"jsonrpc\": \"2.0\",\r\n            \"id\": 1,\r\n            \"method\": func,\r\n            \"params\": []\r\n        }\r\n        \r\n        try:\r\n            # 通过stdio调用MCP服务器\r\n            proc = subprocess.Popen([\r\n                \".venv/Scripts/python.exe\", \"-m\", \"ida_pro_mcp.server\", \"--transport\", \"stdio\"\r\n            ], stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)\r\n            \r\n            # 发送请求\r\n            response, error = proc.communicate(input=json.dumps(request) + \"\\n\", timeout=5)\r\n            \r\n            if error:\r\n                print(f\"  ❌ 错误: {error}\")\r\n            else:\r\n                try:\r\n                    result = json.loads(response)\r\n                    if \"error\" in result:\r\n                        print(f\"  ⚠️  API错误: {result['error']['message']}\")\r\n                    else:\r\n                        print(f\"  ✅ 功能可用\")\r\n                except json.JSONDecodeError:\r\n                    print(f\"  ❌ 无效响应: {response}\")\r\n                    \r\n        except subprocess.TimeoutExpired:\r\n            print(f\"  ❌ 超时\")\r\n        except Exception as e:\r\n            print(f\"  ❌ 异常: {e}\")\r\n    \r\n    print(\"\\n🎯 新功能验证完成!\")\r\n\r\nif __name__ == \"__main__\":\r\n    test_mcp_functions()\r\n"}]}