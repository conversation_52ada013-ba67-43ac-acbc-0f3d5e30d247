{"sourceFile": "src/ida_pro_mcp/server.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754226089736, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754226089736, "name": "Commit-0", "content": "import os\nimport sys\nimport ast\nimport json\nimport shutil\nimport argparse\nimport http.client\nfrom urllib.parse import urlparse\n\nfrom mcp.server.fastmcp import Fast<PERSON>P\n\n# The log_level is necessary for Cline to work: https://github.com/jlowin/fastmcp/issues/81\nmcp = FastMCP(\"github.com/mrexodia/ida-pro-mcp\", log_level=\"ERROR\")\n\njsonrpc_request_id = 1\nida_host = \"127.0.0.1\"\nida_port = 13337\n\ndef make_jsonrpc_request(method: str, *params):\n    \"\"\"Make a JSON-RPC request to the IDA plugin\"\"\"\n    global jsonrpc_request_id, ida_host, ida_port\n    conn = http.client.HTTPConnection(ida_host, ida_port)\n    request = {\n        \"jsonrpc\": \"2.0\",\n        \"method\": method,\n        \"params\": list(params),\n        \"id\": jsonrpc_request_id,\n    }\n    jsonrpc_request_id += 1\n\n    try:\n        conn.request(\"POST\", \"/mcp\", json.dumps(request), {\n            \"Content-Type\": \"application/json\"\n        })\n        response = conn.getresponse()\n        data = json.loads(response.read().decode())\n\n        if \"error\" in data:\n            error = data[\"error\"]\n            code = error[\"code\"]\n            message = error[\"message\"]\n            pretty = f\"JSON-RPC error {code}: {message}\"\n            if \"data\" in error:\n                pretty += \"\\n\" + error[\"data\"]\n            raise Exception(pretty)\n\n        result = data[\"result\"]\n        # NOTE: LLMs do not respond well to empty responses\n        if result is None:\n            result = \"success\"\n        return result\n    except Exception:\n        raise\n    finally:\n        conn.close()\n\**********()\ndef check_connection() -> str:\n    \"\"\"Check if the IDA plugin is running\"\"\"\n    try:\n        metadata = make_jsonrpc_request(\"get_metadata\")\n        return f\"Successfully connected to IDA Pro (open file: {metadata['module']})\"\n    except Exception as e:\n        if sys.platform == \"darwin\":\n            shortcut = \"Ctrl+Option+M\"\n        else:\n            shortcut = \"Ctrl+Alt+M\"\n        return f\"Failed to connect to IDA Pro! Did you run Edit -> Plugins -> MCP ({shortcut}) to start the server?\"\n\n# Code taken from https://github.com/mrexodia/ida-pro-mcp (MIT License)\nclass MCPVisitor(ast.NodeVisitor):\n    def __init__(self):\n        self.types: dict[str, ast.ClassDef] = {}\n        self.functions: dict[str, ast.FunctionDef] = {}\n        self.descriptions: dict[str, str] = {}\n        self.unsafe: list[str] = []\n\n    def visit_FunctionDef(self, node):\n        for decorator in node.decorator_list:\n            if isinstance(decorator, ast.Name):\n                if decorator.id == \"jsonrpc\":\n                    for i, arg in enumerate(node.args.args):\n                        arg_name = arg.arg\n                        arg_type = arg.annotation\n                        if arg_type is None:\n                            raise Exception(f\"Missing argument type for {node.name}.{arg_name}\")\n                        if isinstance(arg_type, ast.Subscript):\n                            assert isinstance(arg_type.value, ast.Name)\n                            assert arg_type.value.id == \"Annotated\"\n                            assert isinstance(arg_type.slice, ast.Tuple)\n                            assert len(arg_type.slice.elts) == 2\n                            annot_type = arg_type.slice.elts[0]\n                            annot_description = arg_type.slice.elts[1]\n                            assert isinstance(annot_description, ast.Constant)\n                            node.args.args[i].annotation = ast.Subscript(\n                                value=ast.Name(id=\"Annotated\", ctx=ast.Load()),\n                                slice=ast.Tuple(\n                                    elts=[\n                                    annot_type,\n                                    ast.Call(\n                                        func=ast.Name(id=\"Field\", ctx=ast.Load()),\n                                        args=[],\n                                        keywords=[\n                                        ast.keyword(\n                                            arg=\"description\",\n                                            value=annot_description)])],\n                                    ctx=ast.Load()),\n                                ctx=ast.Load())\n                        elif isinstance(arg_type, ast.Name):\n                            pass\n                        else:\n                            raise Exception(f\"Unexpected type annotation for {node.name}.{arg_name} -> {type(arg_type)}\")\n\n                    body_comment = node.body[0]\n                    if isinstance(body_comment, ast.Expr) and isinstance(body_comment.value, ast.Constant):\n                        new_body = [body_comment]\n                        self.descriptions[node.name] = body_comment.value.value\n                    else:\n                        new_body = []\n\n                    call_args = [ast.Constant(value=node.name)]\n                    for arg in node.args.args:\n                        call_args.append(ast.Name(id=arg.arg, ctx=ast.Load()))\n                    new_body.append(ast.Return(\n                        value=ast.Call(\n                            func=ast.Name(id=\"make_jsonrpc_request\", ctx=ast.Load()),\n                            args=call_args,\n                            keywords=[])))\n                    decorator_list = [\n                        ast.Call(\n                            func=ast.Attribute(\n                                value=ast.Name(id=\"mcp\", ctx=ast.Load()),\n                                attr=\"tool\",\n                                ctx=ast.Load()),\n                            args=[],\n                            keywords=[]\n                        )\n                    ]\n                    node_nobody = ast.FunctionDef(node.name, node.args, new_body, decorator_list, node.returns, node.type_comment, lineno=node.lineno, col_offset=node.col_offset)\n                    assert node.name not in self.functions, f\"Duplicate function: {node.name}\"\n                    self.functions[node.name] = node_nobody\n                elif decorator.id == \"unsafe\":\n                    self.unsafe.append(node.name)\n\n    def visit_ClassDef(self, node):\n        for base in node.bases:\n            if isinstance(base, ast.Name):\n                if base.id == \"TypedDict\":\n                    self.types[node.name] = node\n\n\nSCRIPT_DIR = os.path.dirname(os.path.realpath(__file__))\nIDA_PLUGIN_PY = os.path.join(SCRIPT_DIR, \"mcp-plugin.py\")\nGENERATED_PY = os.path.join(SCRIPT_DIR, \"server_generated.py\")\n\n# NOTE: This is in the global scope on purpose\nif not os.path.exists(IDA_PLUGIN_PY):\n    raise RuntimeError(f\"IDA plugin not found at {IDA_PLUGIN_PY} (did you move it?)\")\nwith open(IDA_PLUGIN_PY, \"r\", encoding=\"utf-8\") as f:\n    code = f.read()\nmodule = ast.parse(code, IDA_PLUGIN_PY)\nvisitor = MCPVisitor()\nvisitor.visit(module)\ncode = \"\"\"# NOTE: This file has been automatically generated, do not modify!\n# Architecture based on https://github.com/mrexodia/ida-pro-mcp (MIT License)\nimport sys\nif sys.version_info >= (3, 12):\n    from typing import Annotated, Optional, TypedDict, Generic, TypeVar, NotRequired, Any\nelse:\n    from typing_extensions import Annotated, Optional, TypedDict, Generic, TypeVar, NotRequired, Any\nfrom pydantic import Field\n\nT = TypeVar(\"T\")\n\n\"\"\"\nfor type in visitor.types.values():\n    code += ast.unparse(type)\n    code += \"\\n\\n\"\nfor function in visitor.functions.values():\n    code += ast.unparse(function)\n    code += \"\\n\\n\"\n\ntry:\n    if os.path.exists(GENERATED_PY):\n        with open(GENERATED_PY, \"rb\") as f:\n            existing_code_bytes = f.read()\n    else:\n        existing_code_bytes = b\"\"\n    code_bytes = code.encode(\"utf-8\").replace(b\"\\r\", b\"\")\n    if code_bytes != existing_code_bytes:\n        with open(GENERATED_PY, \"wb\") as f:\n            f.write(code_bytes)\nexcept:\n    print(f\"Failed to generate code: {GENERATED_PY}\", file=sys.stderr, flush=True)\n\nexec(compile(code, GENERATED_PY, \"exec\"))\n\nMCP_FUNCTIONS = [\"check_connection\"] + list(visitor.functions.keys())\nUNSAFE_FUNCTIONS = visitor.unsafe\nSAFE_FUNCTIONS = [f for f in visitor.functions.keys() if f not in UNSAFE_FUNCTIONS]\n\ndef generate_readme():\n    print(\"README:\")\n    print(f\"- `check_connection()`: Check if the IDA plugin is running.\")\n    def get_description(name: str):\n        function = visitor.functions[name]\n        signature = function.name + \"(\"\n        for i, arg in enumerate(function.args.args):\n            if i > 0:\n                signature += \", \"\n            signature += arg.arg\n        signature += \")\"\n        description = visitor.descriptions.get(function.name, \"<no description>\").strip()\n        if description[-1] != \".\":\n            description += \".\"\n        return f\"- `{signature}`: {description}\"\n    for safe_function in SAFE_FUNCTIONS:\n        print(get_description(safe_function))\n    print(\"\\nUnsafe functions (`--unsafe` flag required):\\n\")\n    for unsafe_function in UNSAFE_FUNCTIONS:\n        print(get_description(unsafe_function))\n    print(\"\\nMCP Config:\")\n    mcp_config = {\n        \"mcpServers\": {\n            \"github.com/mrexodia/ida-pro-mcp\": {\n            \"command\": \"uv\",\n            \"args\": [\n                \"--directory\",\n                \"c:\\\\MCP\\\\ida-pro-mcp\",\n                \"run\",\n                \"server.py\",\n                \"--install-plugin\"\n            ],\n            \"timeout\": 1800,\n            \"disabled\": False,\n            }\n        }\n    }\n    print(json.dumps(mcp_config, indent=2))\n\ndef get_python_executable():\n    \"\"\"Get the path to the Python executable\"\"\"\n    venv = os.environ.get(\"VIRTUAL_ENV\")\n    if venv:\n        if sys.platform == \"win32\":\n            python = os.path.join(venv, \"Scripts\", \"python.exe\")\n        else:\n            python = os.path.join(venv, \"bin\", \"python3\")\n        if os.path.exists(python):\n            return python\n\n    for path in sys.path:\n        if sys.platform == \"win32\":\n            path = path.replace(\"/\", \"\\\\\")\n\n        split = path.split(os.sep)\n        if split[-1].endswith(\".zip\"):\n            path = os.path.dirname(path)\n            if sys.platform == \"win32\":\n                python_executable = os.path.join(path, \"python.exe\")\n            else:\n                python_executable = os.path.join(path, \"..\", \"bin\", \"python3\")\n            python_executable = os.path.abspath(python_executable)\n\n            if os.path.exists(python_executable):\n                return python_executable\n    return sys.executable\n\ndef print_mcp_config():\n    print(json.dumps({\n            \"mcpServers\": {\n                mcp.name: {\n                    \"command\": get_python_executable(),\n                    \"args\": [\n                        __file__,\n                    ],\n                    \"timeout\": 1800,\n                    \"disabled\": False,\n                }\n            }\n        }, indent=2)\n    )\n\ndef install_mcp_servers(*, uninstall=False, quiet=False, env={}):\n    if sys.platform == \"win32\":\n        configs = {\n            \"Cline\": (os.path.join(os.getenv(\"APPDATA\"), \"Code\", \"User\", \"globalStorage\", \"saoudrizwan.claude-dev\", \"settings\"), \"cline_mcp_settings.json\"),\n            \"Roo Code\": (os.path.join(os.getenv(\"APPDATA\"), \"Code\", \"User\", \"globalStorage\", \"rooveterinaryinc.roo-cline\", \"settings\"), \"mcp_settings.json\"),\n            \"Claude\": (os.path.join(os.getenv(\"APPDATA\"), \"Claude\"), \"claude_desktop_config.json\"),\n            \"Cursor\": (os.path.join(os.path.expanduser(\"~\"), \".cursor\"), \"mcp.json\"),\n            \"Windsurf\": (os.path.join(os.path.expanduser(\"~\"), \".codeium\", \"windsurf\"), \"mcp_config.json\"),\n            # Windows does not support Claude Code, yet.\n            \"LM Studio\": (os.path.join(os.path.expanduser(\"~\"), \".lmstudio\"), \"mcp.json\"),\n        }\n    elif sys.platform == \"darwin\":\n        configs = {\n            \"Cline\": (os.path.join(os.path.expanduser(\"~\"), \"Library\", \"Application Support\", \"Code\", \"User\", \"globalStorage\", \"saoudrizwan.claude-dev\", \"settings\"), \"cline_mcp_settings.json\"),\n            \"Roo Code\": (os.path.join(os.path.expanduser(\"~\"), \"Library\", \"Application Support\", \"Code\", \"User\", \"globalStorage\", \"rooveterinaryinc.roo-cline\", \"settings\"), \"mcp_settings.json\"),\n            \"Claude\": (os.path.join(os.path.expanduser(\"~\"), \"Library\", \"Application Support\", \"Claude\"), \"claude_desktop_config.json\"),\n            \"Cursor\": (os.path.join(os.path.expanduser(\"~\"), \".cursor\"), \"mcp.json\"),\n            \"Windsurf\": (os.path.join(os.path.expanduser(\"~\"), \".codeium\", \"windsurf\"), \"mcp_config.json\"),\n            \"Claude Code\": (os.path.join(os.path.expanduser(\"~\")), \".claude.json\"),\n            \"LM Studio\": (os.path.join(os.path.expanduser(\"~\"), \".lmstudio\"), \"mcp.json\"),\n        }\n    elif sys.platform == \"linux\":\n        configs = {\n            \"Cline\": (os.path.join(os.path.expanduser(\"~\"), \".config\", \"Code\", \"User\", \"globalStorage\", \"saoudrizwan.claude-dev\", \"settings\"), \"cline_mcp_settings.json\"),\n            \"Roo Code\": (os.path.join(os.path.expanduser(\"~\"), \".config\", \"Code\", \"User\", \"globalStorage\", \"rooveterinaryinc.roo-cline\", \"settings\"), \"mcp_settings.json\"),\n            # Claude not supported on Linux\n            \"Cursor\": (os.path.join(os.path.expanduser(\"~\"), \".cursor\"), \"mcp.json\"),\n            \"Windsurf\": (os.path.join(os.path.expanduser(\"~\"), \".codeium\", \"windsurf\"), \"mcp_config.json\"),\n            \"Claude Code\": (os.path.join(os.path.expanduser(\"~\")), \".claude.json\"),\n            \"LM Studio\": (os.path.join(os.path.expanduser(\"~\"), \".lmstudio\"), \"mcp.json\"),\n        }\n    else:\n        print(f\"Unsupported platform: {sys.platform}\")\n        return\n\n    installed = 0\n    for name, (config_dir, config_file) in configs.items():\n        config_path = os.path.join(config_dir, config_file)\n        if not os.path.exists(config_dir):\n            action = \"uninstall\" if uninstall else \"installation\"\n            if not quiet:\n                print(f\"Skipping {name} {action}\\n  Config: {config_path} (not found)\")\n            continue\n        if not os.path.exists(config_path):\n            config = {}\n        else:\n            with open(config_path, \"r\") as f:\n                data = f.read().strip()\n                if len(data) == 0:\n                    config = {}\n                else:\n                    try:\n                        config = json.loads(data)\n                    except json.decoder.JSONDecodeError:\n                        if not quiet:\n                            print(f\"Skipping {name} uninstall\\n  Config: {config_path} (invalid JSON)\")\n                        continue\n        if \"mcpServers\" not in config:\n            config[\"mcpServers\"] = {}\n        mcp_servers = config[\"mcpServers\"]\n        if uninstall:\n            if mcp.name not in mcp_servers:\n                if not quiet:\n                    print(f\"Skipping {name} uninstall\\n  Config: {config_path} (not installed)\")\n                continue\n            del mcp_servers[mcp.name]\n        else:\n            if mcp.name in mcp_servers:\n                for key, value in mcp_servers[mcp.name].get(\"env\", {}):\n                    env[key] = value\n            mcp_servers[mcp.name] = {\n                \"command\": get_python_executable(),\n                \"args\": [\n                    __file__,\n                ],\n                \"timeout\": 1800,\n                \"disabled\": False,\n                \"autoApprove\": SAFE_FUNCTIONS,\n                \"alwaysAllow\": SAFE_FUNCTIONS,\n            }\n            if env:\n                mcp_servers[mcp.name][\"env\"] = env\n        with open(config_path, \"w\") as f:\n            json.dump(config, f, indent=2)\n        if not quiet:\n            action = \"Uninstalled\" if uninstall else \"Installed\"\n            print(f\"{action} {name} MCP server (restart required)\\n  Config: {config_path}\")\n        installed += 1\n    if not uninstall and installed == 0:\n        print(\"No MCP servers installed. For unsupported MCP clients, use the following config:\\n\")\n        print_mcp_config()\n\ndef install_ida_plugin(*, uninstall: bool = False, quiet: bool = False):\n    if sys.platform == \"win32\":\n        ida_plugin_folder = os.path.join(os.getenv(\"APPDATA\"), \"Hex-Rays\", \"IDA Pro\", \"plugins\")\n    else:\n        ida_plugin_folder = os.path.join(os.path.expanduser(\"~\"), \".idapro\", \"plugins\")\n    plugin_destination = os.path.join(ida_plugin_folder, \"mcp-plugin.py\")\n    if uninstall:\n        if not os.path.exists(plugin_destination):\n            print(f\"Skipping IDA plugin uninstall\\n  Path: {plugin_destination} (not found)\")\n            return\n        os.remove(plugin_destination)\n        if not quiet:\n            print(f\"Uninstalled IDA plugin\\n  Path: {plugin_destination}\")\n    else:\n        # Create IDA plugins folder\n        if not os.path.exists(ida_plugin_folder):\n            os.makedirs(ida_plugin_folder)\n\n        # Skip if symlink already up to date\n        realpath = os.path.realpath(plugin_destination)\n        if realpath == IDA_PLUGIN_PY:\n            if not quiet:\n                print(f\"Skipping IDA plugin installation (symlink up to date)\\n  Plugin: {realpath}\")\n        else:\n            # Remove existing plugin\n            if os.path.lexists(plugin_destination):\n                os.remove(plugin_destination)\n\n            # Symlink or copy the plugin\n            try:\n                os.symlink(IDA_PLUGIN_PY, plugin_destination)\n            except OSError:\n                shutil.copy(IDA_PLUGIN_PY, plugin_destination)\n\n            if not quiet:\n                print(f\"Installed IDA Pro plugin (IDA restart required)\\n  Plugin: {plugin_destination}\")\n\ndef main():\n    global ida_host, ida_port\n    parser = argparse.ArgumentParser(description=\"IDA Pro MCP Server\")\n    parser.add_argument(\"--install\", action=\"store_true\", help=\"Install the MCP Server and IDA plugin\")\n    parser.add_argument(\"--uninstall\", action=\"store_true\", help=\"Uninstall the MCP Server and IDA plugin\")\n    parser.add_argument(\"--generate-docs\", action=\"store_true\", help=argparse.SUPPRESS)\n    parser.add_argument(\"--install-plugin\", action=\"store_true\", help=argparse.SUPPRESS)\n    parser.add_argument(\"--transport\", type=str, default=\"stdio\", help=\"MCP transport protocol to use (stdio or http://127.0.0.1:8744)\")\n    parser.add_argument(\"--ida-rpc\", type=str, default=f\"http://{ida_host}:{ida_port}\", help=f\"IDA RPC server to use (default: http://{ida_host}:{ida_port})\")\n    parser.add_argument(\"--unsafe\", action=\"store_true\", help=\"Enable unsafe functions (DANGEROUS)\")\n    parser.add_argument(\"--config\", action=\"store_true\", help=\"Generate MCP config JSON\")\n    args = parser.parse_args()\n\n    if args.install and args.uninstall:\n        print(\"Cannot install and uninstall at the same time\")\n        return\n\n    if args.install:\n        install_mcp_servers()\n        install_ida_plugin()\n        return\n\n    if args.uninstall:\n        install_mcp_servers(uninstall=True)\n        install_ida_plugin(uninstall=True)\n        return\n\n    # NOTE: Developers can use this to generate the README\n    if args.generate_docs:\n        generate_readme()\n        return\n\n    # NOTE: This is silent for automated Cline installations\n    if args.install_plugin:\n        install_ida_plugin(quiet=True)\n\n    if args.config:\n        print_mcp_config()\n        return\n\n    # Parse IDA RPC server argument\n    ida_rpc = urlparse(args.ida_rpc)\n    if ida_rpc.hostname is None or ida_rpc.port is None:\n        raise Exception(f\"Invalid IDA RPC server: {args.ida_rpc}\")\n    ida_host = ida_rpc.hostname\n    ida_port = ida_rpc.port\n\n    # Remove unsafe tools\n    if not args.unsafe:\n        mcp_tools = mcp._tool_manager._tools\n        for unsafe in UNSAFE_FUNCTIONS:\n            if unsafe in mcp_tools:\n                del mcp_tools[unsafe]\n\n    try:\n        if args.transport == \"stdio\":\n            mcp.run(transport=\"stdio\")\n        else:\n            url = urlparse(args.transport)\n            if url.hostname is None or url.port is None:\n                raise Exception(f\"Invalid transport URL: {args.transport}\")\n            mcp.settings.host = url.hostname\n            mcp.settings.port = url.port\n            # NOTE: npx @modelcontextprotocol/inspector for debugging\n            print(f\"MCP Server availabile at http://{mcp.settings.host}:{mcp.settings.port}/sse\")\n            mcp.settings.log_level = \"INFO\"\n            mcp.run(transport=\"sse\")\n    except KeyboardInterrupt:\n        pass\n\nif __name__ == \"__main__\":\n    main()\n"}]}