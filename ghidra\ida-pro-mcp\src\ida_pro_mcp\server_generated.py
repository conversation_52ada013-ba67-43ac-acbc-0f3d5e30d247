# NOTE: This file has been automatically generated, do not modify!
# Architecture based on https://github.com/mrexodia/ida-pro-mcp (MIT License)
import sys
if sys.version_info >= (3, 12):
    from typing import Annotated, Optional, TypedDict, Generic, TypeVar, NotRequired, Any
else:
    from typing_extensions import Annotated, Optional, TypedDict, Generic, TypeVar, NotRequired, Any
from pydantic import Field

T = TypeVar("T")

class CacheEntry(TypedDict):
    """缓存条目类型定义"""
    data: Any
    timestamp: float
    access_count: int
    size: int

class Metadata(TypedDict):
    path: str
    module: str
    base: str
    size: str
    md5: str
    sha256: str
    crc32: str
    filesize: str

class Function(TypedDict):
    address: str
    name: str
    size: str

class ConvertedNumber(TypedDict):
    decimal: str
    hexadecimal: str
    bytes: str
    ascii: Optional[str]
    binary: str

class Page(TypedDict, Generic[T]):
    data: list[T]
    next_offset: Optional[int]

class Global(TypedDict):
    address: str
    name: str

class Import(TypedDict):
    address: str
    imported_name: str
    module: str

class String(TypedDict):
    address: str
    length: int
    string: str

class DisassemblyLine(TypedDict):
    segment: NotRequired[str]
    address: str
    label: NotRequired[str]
    instruction: str
    comments: NotRequired[list[str]]

class Argument(TypedDict):
    name: str
    type: str

class DisassemblyFunction(TypedDict):
    name: str
    start_ea: str
    return_type: NotRequired[str]
    arguments: NotRequired[list[Argument]]
    stack_frame: list[dict]
    lines: list[DisassemblyLine]

class Xref(TypedDict):
    address: str
    type: str
    function: Optional[Function]

class StackFrameVariable(TypedDict):
    name: str
    offset: str
    size: str
    type: str

class StructureMember(TypedDict):
    name: str
    offset: str
    size: str
    type: str

class StructureDefinition(TypedDict):
    name: str
    size: str
    members: list[StructureMember]

class VerificationPoint(TypedDict):
    address: str
    type: str
    instruction: str
    target: Optional[str]

class JumpCondition(TypedDict):
    address: str
    instruction: str
    condition: str
    true_target: str
    false_target: str

class CallChainEntry(TypedDict):
    caller: str
    callee: str
    call_site: str

class TamperingPoint(TypedDict):
    address: str
    function: str
    return_type: str
    modification_hint: str

class CryptoMatch(TypedDict):
    algorithm: str
    confidence: float
    address: str
    signature_type: str

class CryptoKey(TypedDict):
    address: str
    algorithm: str
    key_size: int
    confidence: float

class CryptoFlow(TypedDict):
    algorithm: str
    operations: list[str]
    data_flow: list[str]
    confidence: float

class AntiDebugDetection(TypedDict):
    technique: str
    address: str
    api_name: str
    risk_level: str
    bypass_suggestion: str

class BypassStrategy(TypedDict):
    target: str
    method: str
    description: str
    risk_level: str
    patch_bytes: Optional[str]

class PatchResult(TypedDict):
    address: str
    original_bytes: str
    patched_bytes: str
    status: str
    backup_info: str

class LicenseValidation(TypedDict):
    location: str
    validation_type: str
    algorithm_pattern: str
    input_format: str
    validation_strength: str
    bypass_suggestion: str

class SerialFormat(TypedDict):
    pattern: str
    length: int
    charset: str
    validation_address: str
    algorithm_hint: str
    sample_format: str

class TimeRestriction(TypedDict):
    check_type: str
    address: str
    limit_value: str
    comparison_method: str
    bypass_strategy: str
    risk_level: str

class KeygenHint(TypedDict):
    algorithm_type: str
    input_constraints: str
    validation_steps: list[str]
    key_generation_strategy: str
    success_indicators: str
    difficulty_level: str

class MemoryPatch(TypedDict):
    patch_id: str
    address: str
    original_bytes: str
    patched_bytes: str
    size: int
    timestamp: str
    description: str
    is_active: bool

class InstructionMod(TypedDict):
    address: str
    original_instruction: str
    modified_instruction: str
    modification_type: str
    risk_level: str
    rollback_data: str

class FunctionHook(TypedDict):
    target_function: str
    hook_address: str
    hook_type: str
    original_entry: str
    redirect_target: str
    status: str

class ReturnValuePatch(TypedDict):
    function_address: str
    original_return_type: str
    patched_return_value: str
    patch_method: str
    success_rate: str

class PatchHistoryEntry(TypedDict):
    operation_id: str
    operation_type: str
    target_address: str
    timestamp: str
    operation_data: str
    rollback_possible: bool

class EncodedString(TypedDict):
    """编码字符串结果"""
    original: str
    decoded: str
    encoding_type: str
    confidence: float
    address: str

class LicenseString(TypedDict):
    """许可证字符串结果"""
    string: str
    address: str
    category: str
    importance: str
    keywords_found: list[str]

class ErrorMessage(TypedDict):
    """错误消息分析结果"""
    message: str
    address: str
    severity: str
    category: str
    potential_cause: str

class ResourceString(TypedDict):
    """资源字符串结果"""
    string: str
    address: str
    resource_type: str
    context: str
    usage_hint: str

class ProtectionType(TypedDict):
    """保护类型检测结果"""
    protection_name: str
    confidence: float
    detected_features: list[str]
    analysis_strategy: str
    priority: str

class AnalysisStrategy(TypedDict):
    """分析策略"""
    strategy_name: str
    target_modules: list[str]
    execution_order: list[str]
    parameters: dict[str, Any]
    estimated_time: str
    success_probability: float

class BatchTask(TypedDict):
    """批处理任务"""
    task_id: str
    task_type: str
    target: str
    status: str
    progress: float
    results: Optional[dict[str, Any]]
    start_time: str
    end_time: Optional[str]

class CrackReport(TypedDict):
    """破解分析报告"""
    report_id: str
    analysis_summary: str
    protection_analysis: dict[str, Any]
    vulnerability_points: list[dict[str, Any]]
    recommended_approach: str
    success_indicators: list[str]
    risk_assessment: str
    generated_time: str

class JavaScriptPattern(TypedDict):
    """JavaScript模式匹配结果"""
    pattern_type: str
    location: str
    code_snippet: str
    security_level: str
    description: str
    exploitation_hint: str

class APIEndpoint(TypedDict):
    """API端点发现结果"""
    endpoint_url: str
    method: str
    parameters: list[str]
    location: str
    authentication_required: bool
    potential_vulnerability: str

class WebResource(TypedDict):
    """Web资源分析结果"""
    resource_type: str
    location: str
    size_bytes: int
    encoding: str
    content_preview: str
    security_impact: str

class ClientSideValidation(TypedDict):
    """客户端验证分析结果"""
    validation_type: str
    location: str
    validation_logic: str
    bypass_difficulty: str
    bypass_suggestion: str

class WebCryptoPattern(TypedDict):
    """Web加密模式结果"""
    crypto_type: str
    implementation: str
    location: str
    strength_assessment: str
    weakness_description: str
    attack_vector: str

class CrackStrategy(TypedDict):
    """破解策略"""
    strategy_id: str
    target_type: str
    difficulty_level: str
    success_probability: float
    attack_vector: str
    required_tools: list[str]
    execution_steps: list[str]
    estimated_time: str
    risk_level: str

class AdvancedBypass(TypedDict):
    """高级绕过技术"""
    bypass_name: str
    target_protection: str
    complexity: str
    implementation_code: str
    success_indicators: list[str]
    failure_recovery: str

class ExploitChain(TypedDict):
    """漏洞利用链"""
    chain_id: str
    vulnerability_points: list[str]
    exploitation_order: list[str]
    payload_templates: dict[str, str]
    verification_steps: list[str]
    cleanup_required: bool

class IntelligentPatch(TypedDict):
    """智能补丁方案"""
    patch_id: str
    target_addresses: list[str]
    patch_strategy: str
    backup_plan: str
    verification_method: str
    rollback_procedure: str

class APICall(TypedDict):
    """API调用记录"""
    api_name: str
    module: str
    address: str
    parameters: list[str]
    return_value: str
    timestamp: str
    thread_id: str

class MemoryAccess(TypedDict):
    """内存访问记录"""
    access_type: str
    address: str
    size: int
    value: str
    instruction_pointer: str
    access_count: int

class BehaviorPattern(TypedDict):
    """行为模式分析"""
    pattern_type: str
    description: str
    frequency: int
    risk_level: str
    indicators: list[str]
    mitigation: str

class ProcessInteraction(TypedDict):
    """进程交互记录"""
    interaction_type: str
    target_process: str
    operation: str
    parameters: dict[str, Any]
    success: bool
    timestamp: str

class NetworkActivity(TypedDict):
    """网络活动记录"""
    activity_type: str
    destination: str
    port: int
    protocol: str
    data_size: int
    timestamp: str
    suspicious: bool

class DecryptionResult(TypedDict):
    """解密结果"""
    algorithm_identified: str
    key_found: str
    decrypted_data: str
    confidence_level: float
    decryption_method: str
    verification_status: str

class KeyDerivationAnalysis(TypedDict):
    """密钥推导分析结果"""
    function_address: str
    derivation_method: str
    input_sources: list[str]
    key_material: str
    salt_location: Optional[str]
    iteration_count: int

class CustomCipherPattern(TypedDict):
    """自定义密码模式"""
    pattern_type: str
    implementation_address: str
    key_schedule: str
    block_size: int
    operation_mode: str
    weakness_analysis: str

class ConfigFileAnalysis(TypedDict):
    """配置文件分析结果"""
    file_location: str
    encryption_type: str
    key_source: str
    decrypted_content: dict[str, Any]
    security_level: str

class VulnerabilityPoint(TypedDict):
    """漏洞点信息"""
    vulnerability_type: str
    function_name: str
    address: str
    risk_level: str
    description: str
    exploitation_difficulty: str
    mitigation_suggestion: str

class UnsafeFunction(TypedDict):
    """不安全函数使用"""
    function_name: str
    call_sites: list[str]
    risk_assessment: str
    safer_alternative: str
    usage_context: str

class IntegerOverflow(TypedDict):
    """整数溢出检测"""
    location: str
    operation_type: str
    potential_overflow: str
    input_validation: str
    severity: str

class DynamicAnalysisSession(TypedDict):
    """动态分析会话"""
    session_id: str
    target_process: NotRequired[str]
    target_pid: NotRequired[int]
    start_time: str
    status: str
    monitoring_rules: list[dict]
    captured_events: list[dict]

class BreakpointInfo(TypedDict):
    """断点信息"""
    breakpoint_id: str
    address: str
    type: str
    condition: NotRequired[str]
    hit_count: int
    enabled: bool
    description: NotRequired[str]

class WorkflowTemplate(TypedDict):
    """工作流模板"""
    template_id: str
    name: str
    description: str
    category: str
    steps: list[dict]
    estimated_time: str
    difficulty_level: str

class WorkflowExecution(TypedDict):
    """工作流执行状态"""
    execution_id: str
    template_id: str
    status: str
    current_step: int
    total_steps: int
    start_time: str
    end_time: NotRequired[str]
    results: list[dict]
    errors: list[str]

class PerformanceConfig(TypedDict):
    """性能配置"""
    max_memory_usage: int
    parallel_workers: int
    chunk_size: int
    cache_enabled: bool
    batch_processing: bool

@mcp.tool()
def get_metadata() -> Metadata:
    """Get metadata about the current IDB - 版本：2025-01-04-增强版"""
    return make_jsonrpc_request('get_metadata')

@mcp.tool()
def test_enhanced_version() -> dict[str, Any]:
    """测试增强版本是否加载"""
    return make_jsonrpc_request('test_enhanced_version')

@mcp.tool()
def get_function_by_name(name: Annotated[str, Field(description='Name of the function to get')]) -> Function:
    """Get a function by its name"""
    return make_jsonrpc_request('get_function_by_name', name)

@mcp.tool()
def get_function_by_address(address: Annotated[str, Field(description='Address of the function to get')]) -> Function:
    """Get a function by its address"""
    return make_jsonrpc_request('get_function_by_address', address)

@mcp.tool()
def get_current_address() -> str:
    """Get the address currently selected by the user"""
    return make_jsonrpc_request('get_current_address')

@mcp.tool()
def get_current_function() -> Optional[Function]:
    """Get the function currently selected by the user"""
    return make_jsonrpc_request('get_current_function')

@mcp.tool()
def convert_number(text: Annotated[str, Field(description='Textual representation of the number to convert')], size: Annotated[Optional[int], Field(description='Size of the variable in bytes')]) -> ConvertedNumber:
    """Convert a number (decimal, hexadecimal) to different representations"""
    return make_jsonrpc_request('convert_number', text, size)

@mcp.tool()
def list_functions(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of functions to list (100 is a good default, 0 means remainder)')]) -> Page[Function]:
    """List all functions in the database (paginated)"""
    return make_jsonrpc_request('list_functions', offset, count)

@mcp.tool()
def list_globals_filter(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of globals to list (100 is a good default, 0 means remainder)')], filter: Annotated[str, Field(description='Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax')]) -> Page[Global]:
    """List matching globals in the database (paginated, filtered)"""
    return make_jsonrpc_request('list_globals_filter', offset, count, filter)

@mcp.tool()
def list_globals(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of globals to list (100 is a good default, 0 means remainder)')]) -> Page[Global]:
    """List all globals in the database (paginated)"""
    return make_jsonrpc_request('list_globals', offset, count)

@mcp.tool()
def list_imports(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of imports to list (100 is a good default, 0 means remainder)')]) -> Page[Import]:
    """ List all imported symbols with their name and module (paginated) """
    return make_jsonrpc_request('list_imports', offset, count)

@mcp.tool()
def list_strings_filter(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of strings to list (100 is a good default, 0 means remainder)')], filter: Annotated[str, Field(description='Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax')]) -> Page[String]:
    """List matching strings in the database (paginated, filtered)"""
    return make_jsonrpc_request('list_strings_filter', offset, count, filter)

@mcp.tool()
def list_strings(offset: Annotated[int, Field(description='Offset to start listing from (start at 0)')], count: Annotated[int, Field(description='Number of strings to list (100 is a good default, 0 means remainder)')]) -> Page[String]:
    """List all strings in the database (paginated)"""
    return make_jsonrpc_request('list_strings', offset, count)

@mcp.tool()
def list_local_types():
    """List all Local types in the database"""
    return make_jsonrpc_request('list_local_types')

@mcp.tool()
def decompile_function(address: Annotated[str, Field(description='Address of the function to decompile')]) -> str:
    """Decompile a function at the given address"""
    return make_jsonrpc_request('decompile_function', address)

@mcp.tool()
def disassemble_function(start_address: Annotated[str, Field(description='Address of the function to disassemble')]) -> DisassemblyFunction:
    """Get assembly code for a function"""
    return make_jsonrpc_request('disassemble_function', start_address)

@mcp.tool()
def get_xrefs_to(address: Annotated[str, Field(description='Address to get cross references to')]) -> list[Xref]:
    """Get all cross references to the given address"""
    return make_jsonrpc_request('get_xrefs_to', address)

@mcp.tool()
def get_xrefs_to_field(struct_name: Annotated[str, Field(description='Name of the struct (type) containing the field')], field_name: Annotated[str, Field(description='Name of the field (member) to get xrefs to')]) -> list[Xref]:
    """Get all cross references to a named struct field (member)"""
    return make_jsonrpc_request('get_xrefs_to_field', struct_name, field_name)

@mcp.tool()
def get_entry_points() -> list[Function]:
    """Get all entry points in the database"""
    return make_jsonrpc_request('get_entry_points')

@mcp.tool()
def set_comment(address: Annotated[str, Field(description='Address in the function to set the comment for')], comment: Annotated[str, Field(description='Comment text')]):
    """Set a comment for a given address in the function disassembly and pseudocode"""
    return make_jsonrpc_request('set_comment', address, comment)

@mcp.tool()
def rename_local_variable(function_address: Annotated[str, Field(description='Address of the function containing the variable')], old_name: Annotated[str, Field(description='Current name of the variable')], new_name: Annotated[str, Field(description='New name for the variable (empty for a default name)')]):
    """Rename a local variable in a function"""
    return make_jsonrpc_request('rename_local_variable', function_address, old_name, new_name)

@mcp.tool()
def rename_global_variable(old_name: Annotated[str, Field(description='Current name of the global variable')], new_name: Annotated[str, Field(description='New name for the global variable (empty for a default name)')]):
    """Rename a global variable"""
    return make_jsonrpc_request('rename_global_variable', old_name, new_name)

@mcp.tool()
def set_global_variable_type(variable_name: Annotated[str, Field(description='Name of the global variable')], new_type: Annotated[str, Field(description='New type for the variable')]):
    """Set a global variable's type"""
    return make_jsonrpc_request('set_global_variable_type', variable_name, new_type)

@mcp.tool()
def get_global_variable_value_by_name(variable_name: Annotated[str, Field(description='Name of the global variable')]) -> str:
    """
    Read a global variable's value (if known at compile-time)

    Prefer this function over the `data_read_*` functions.
    """
    return make_jsonrpc_request('get_global_variable_value_by_name', variable_name)

@mcp.tool()
def get_global_variable_value_at_address(ea: Annotated[str, Field(description='Address of the global variable')]) -> str:
    """
    Read a global variable's value by its address (if known at compile-time)

    Prefer this function over the `data_read_*` functions.
    """
    return make_jsonrpc_request('get_global_variable_value_at_address', ea)

@mcp.tool()
def rename_function(function_address: Annotated[str, Field(description='Address of the function to rename')], new_name: Annotated[str, Field(description='New name for the function (empty for a default name)')]):
    """Rename a function"""
    return make_jsonrpc_request('rename_function', function_address, new_name)

@mcp.tool()
def set_function_prototype(function_address: Annotated[str, Field(description='Address of the function')], prototype: Annotated[str, Field(description='New function prototype')]):
    """Set a function's prototype"""
    return make_jsonrpc_request('set_function_prototype', function_address, prototype)

@mcp.tool()
def declare_c_type(c_declaration: Annotated[str, Field(description='C declaration of the type. Examples include: typedef int foo_t; struct bar { int a; bool b; };')]):
    """Create or update a local type from a C declaration"""
    return make_jsonrpc_request('declare_c_type', c_declaration)

@mcp.tool()
def set_local_variable_type(function_address: Annotated[str, Field(description='Address of the decompiled function containing the variable')], variable_name: Annotated[str, Field(description='Name of the variable')], new_type: Annotated[str, Field(description='New type for the variable')]):
    """Set a local variable's type"""
    return make_jsonrpc_request('set_local_variable_type', function_address, variable_name, new_type)

@mcp.tool()
def get_stack_frame_variables(function_address: Annotated[str, Field(description='Address of the disassembled function to retrieve the stack frame variables')]) -> list[StackFrameVariable]:
    """ Retrieve the stack frame variables for a given function """
    return make_jsonrpc_request('get_stack_frame_variables', function_address)

@mcp.tool()
def get_defined_structures() -> list[StructureDefinition]:
    """ Returns a list of all defined structures """
    return make_jsonrpc_request('get_defined_structures')

@mcp.tool()
def rename_stack_frame_variable(function_address: Annotated[str, Field(description='Address of the disassembled function to set the stack frame variables')], old_name: Annotated[str, Field(description='Current name of the variable')], new_name: Annotated[str, Field(description='New name for the variable (empty for a default name)')]):
    """ Change the name of a stack variable for an IDA function """
    return make_jsonrpc_request('rename_stack_frame_variable', function_address, old_name, new_name)

@mcp.tool()
def create_stack_frame_variable(function_address: Annotated[str, Field(description='Address of the disassembled function to set the stack frame variables')], offset: Annotated[str, Field(description='Offset of the stack frame variable')], variable_name: Annotated[str, Field(description='Name of the stack variable')], type_name: Annotated[str, Field(description='Type of the stack variable')]):
    """ For a given function, create a stack variable at an offset and with a specific type """
    return make_jsonrpc_request('create_stack_frame_variable', function_address, offset, variable_name, type_name)

@mcp.tool()
def set_stack_frame_variable_type(function_address: Annotated[str, Field(description='Address of the disassembled function to set the stack frame variables')], variable_name: Annotated[str, Field(description='Name of the stack variable')], type_name: Annotated[str, Field(description='Type of the stack variable')]):
    """ For a given disassembled function, set the type of a stack variable """
    return make_jsonrpc_request('set_stack_frame_variable_type', function_address, variable_name, type_name)

@mcp.tool()
def delete_stack_frame_variable(function_address: Annotated[str, Field(description='Address of the function to set the stack frame variables')], variable_name: Annotated[str, Field(description='Name of the stack variable')]):
    """ Delete the named stack variable for a given function """
    return make_jsonrpc_request('delete_stack_frame_variable', function_address, variable_name)

@mcp.tool()
def read_memory_bytes(memory_address: Annotated[str, Field(description='Address of the memory value to be read')], size: Annotated[int, Field(description='size of memory to read')]) -> str:
    """
    Read bytes at a given address.

    Only use this function if `get_global_variable_at` and `get_global_variable_by_name`
    both failed.
    """
    return make_jsonrpc_request('read_memory_bytes', memory_address, size)

@mcp.tool()
def data_read_byte(address: Annotated[str, Field(description='Address to get 1 byte value from')]) -> int:
    """
    Read the 1 byte value at the specified address.

    Only use this function if `get_global_variable_at` failed.
    """
    return make_jsonrpc_request('data_read_byte', address)

@mcp.tool()
def data_read_word(address: Annotated[str, Field(description='Address to get 2 bytes value from')]) -> int:
    """
    Read the 2 byte value at the specified address as a WORD.

    Only use this function if `get_global_variable_at` failed.
    """
    return make_jsonrpc_request('data_read_word', address)

@mcp.tool()
def data_read_dword(address: Annotated[str, Field(description='Address to get 4 bytes value from')]) -> int:
    """
    Read the 4 byte value at the specified address as a DWORD.

    Only use this function if `get_global_variable_at` failed.
    """
    return make_jsonrpc_request('data_read_dword', address)

@mcp.tool()
def data_read_qword(address: Annotated[str, Field(description='Address to get 8 bytes value from')]) -> int:
    """
    Read the 8 byte value at the specified address as a QWORD.

    Only use this function if `get_global_variable_at` failed.
    """
    return make_jsonrpc_request('data_read_qword', address)

@mcp.tool()
def data_read_string(address: Annotated[str, Field(description='Address to get string from')]) -> str:
    """
    Read the string at the specified address.

    Only use this function if `get_global_variable_at` failed.
    """
    return make_jsonrpc_request('data_read_string', address)

@mcp.tool()
def dbg_get_registers() -> list[dict[str, str]]:
    """Get all registers and their values. This function is only available when debugging."""
    return make_jsonrpc_request('dbg_get_registers')

@mcp.tool()
def dbg_get_call_stack() -> list[dict[str, str]]:
    """Get the current call stack."""
    return make_jsonrpc_request('dbg_get_call_stack')

@mcp.tool()
def dbg_list_breakpoints():
    """List all breakpoints in the program."""
    return make_jsonrpc_request('dbg_list_breakpoints')

@mcp.tool()
def dbg_start_process() -> str:
    """Start the debugger"""
    return make_jsonrpc_request('dbg_start_process')

@mcp.tool()
def dbg_exit_process() -> str:
    """Exit the debugger"""
    return make_jsonrpc_request('dbg_exit_process')

@mcp.tool()
def dbg_continue_process() -> str:
    """Continue the debugger"""
    return make_jsonrpc_request('dbg_continue_process')

@mcp.tool()
def dbg_run_to(address: Annotated[str, Field(description='Run the debugger to the specified address')]) -> str:
    """Run the debugger to the specified address"""
    return make_jsonrpc_request('dbg_run_to', address)

@mcp.tool()
def dbg_set_breakpoint(address: Annotated[str, Field(description='Set a breakpoint at the specified address')]) -> str:
    """Set a breakpoint at the specified address"""
    return make_jsonrpc_request('dbg_set_breakpoint', address)

@mcp.tool()
def dbg_delete_breakpoint(address: Annotated[str, Field(description='del a breakpoint at the specified address')]) -> str:
    """del a breakpoint at the specified address"""
    return make_jsonrpc_request('dbg_delete_breakpoint', address)

@mcp.tool()
def dbg_enable_breakpoint(address: Annotated[str, Field(description='Enable or disable a breakpoint at the specified address')], enable: Annotated[bool, Field(description='Enable or disable a breakpoint')]) -> str:
    """Enable or disable a breakpoint at the specified address"""
    return make_jsonrpc_request('dbg_enable_breakpoint', address, enable)

@mcp.tool()
def identify_verification_points(function_address: Annotated[str, Field(description='函数地址')]) -> list[VerificationPoint]:
    """识别关键验证逻辑点"""
    return make_jsonrpc_request('identify_verification_points', function_address)

@mcp.tool()
def analyze_jump_conditions(function_address: Annotated[str, Field(description='函数地址')]) -> list[JumpCondition]:
    """分析条件跳转逻辑"""
    return make_jsonrpc_request('analyze_jump_conditions', function_address)

@mcp.tool()
def trace_function_call_chain(start_address: Annotated[str, Field(description='起始地址')]) -> list[CallChainEntry]:
    """追踪函数调用链"""
    return make_jsonrpc_request('trace_function_call_chain', start_address)

@mcp.tool()
def identify_return_tampering_points(function_address: Annotated[str, Field(description='函数地址')]) -> list[TamperingPoint]:
    """定位返回值篡改点"""
    return make_jsonrpc_request('identify_return_tampering_points', function_address)

@mcp.tool()
def identify_crypto_algorithms(search_area: Annotated[Optional[str], Field(description='搜索区域地址（可选）')]=None) -> list[CryptoMatch]:
    """识别加密算法特征"""
    return make_jsonrpc_request('identify_crypto_algorithms', search_area)

@mcp.tool()
def locate_crypto_keys(algorithm: Annotated[str, Field(description='算法类型')], search_address: Annotated[str, Field(description='搜索起始地址')]) -> list[CryptoKey]:
    """定位加密密钥"""
    return make_jsonrpc_request('locate_crypto_keys', algorithm, search_address)

@mcp.tool()
def analyze_crypto_flow(function_address: Annotated[str, Field(description='函数地址')]) -> CryptoFlow:
    """分析加密流程"""
    return make_jsonrpc_request('analyze_crypto_flow', function_address)

@mcp.tool()
def detect_anti_debug_techniques(search_area: Annotated[Optional[str], Field(description='搜索区域（可选）')]=None) -> list[AntiDebugDetection]:
    """检测反调试技术"""
    return make_jsonrpc_request('detect_anti_debug_techniques', search_area)

@mcp.tool()
def generate_bypass_strategies(detection_address: Annotated[str, Field(description='检测点地址')]) -> list[BypassStrategy]:
    """生成绕过策略"""
    return make_jsonrpc_request('generate_bypass_strategies', detection_address)

@mcp.tool()
def apply_anti_debug_patches(patch_address: Annotated[str, Field(description='补丁地址')], patch_method: Annotated[str, Field(description='补丁方法')]) -> PatchResult:
    """应用反调试绕过补丁"""
    return make_jsonrpc_request('apply_anti_debug_patches', patch_address, patch_method)

@mcp.tool()
def analyze_license_validation(target_area: Annotated[Optional[str], Field(description='目标分析区域（可选）')]=None) -> list[LicenseValidation]:
    """分析许可证验证逻辑"""
    return make_jsonrpc_request('analyze_license_validation', target_area)

@mcp.tool()
def trace_serial_validation(serial_input_area: Annotated[str, Field(description='序列号输入处理区域地址')]) -> list[SerialFormat]:
    """追踪序列号验证过程"""
    return make_jsonrpc_request('trace_serial_validation', serial_input_area)

@mcp.tool()
def detect_time_limitations(search_scope: Annotated[Optional[str], Field(description='搜索范围（可选）')]=None) -> list[TimeRestriction]:
    """检测时间限制机制"""
    return make_jsonrpc_request('detect_time_limitations', search_scope)

@mcp.tool()
def generate_keygen_hints(validation_function: Annotated[str, Field(description='验证函数地址')]) -> KeygenHint:
    """生成注册机提示信息"""
    return make_jsonrpc_request('generate_keygen_hints', validation_function)

@mcp.tool()
def apply_memory_patch(target_address: Annotated[str, Field(description='目标地址')], patch_data: Annotated[str, Field(description='补丁数据（十六进制）')], description: Annotated[str, Field(description='补丁描述')]) -> MemoryPatch:
    """应用内存补丁"""
    return make_jsonrpc_request('apply_memory_patch', target_address, patch_data, description)

@mcp.tool()
def modify_instruction(instruction_address: Annotated[str, Field(description='指令地址')], new_instruction: Annotated[str, Field(description='新指令')]) -> InstructionMod:
    """修改单条指令"""
    return make_jsonrpc_request('modify_instruction', instruction_address, new_instruction)

@mcp.tool()
def hook_function_calls(function_name: Annotated[str, Field(description='函数名称')], hook_method: Annotated[str, Field(description='Hook方法')]) -> FunctionHook:
    """Hook函数调用"""
    return make_jsonrpc_request('hook_function_calls', function_name, hook_method)

@mcp.tool()
def patch_return_values(function_address: Annotated[str, Field(description='函数地址')], return_value: Annotated[str, Field(description='返回值')]) -> ReturnValuePatch:
    """修改函数返回值"""
    return make_jsonrpc_request('patch_return_values', function_address, return_value)

@mcp.tool()
def manage_patch_history(operation: Annotated[str, Field(description='操作类型: list/rollback/clear')]) -> list[PatchHistoryEntry]:
    """管理补丁历史记录"""
    return make_jsonrpc_request('manage_patch_history', operation)

@mcp.tool()
def test_lazy_initialization() -> dict[str, Any]:
    """测试延迟初始化框架功能"""
    return make_jsonrpc_request('test_lazy_initialization')

@mcp.tool()
def get_lazy_module_stats() -> dict[str, Any]:
    """获取延迟初始化模块统计信息"""
    return make_jsonrpc_request('get_lazy_module_stats')

@mcp.tool()
def get_cache_statistics() -> dict[str, Any]:
    """获取智能缓存系统统计信息"""
    return make_jsonrpc_request('get_cache_statistics')

@mcp.tool()
def clear_analysis_cache() -> dict[str, str]:
    """清空分析缓存"""
    return make_jsonrpc_request('clear_analysis_cache')

@mcp.tool()
def configure_cache_settings(max_size: Annotated[int, Field(description='缓存最大条目数')], max_memory_mb: Annotated[int, Field(description='缓存最大内存使用量（MB）')], ttl_seconds: Annotated[int, Field(description='缓存条目存活时间（秒）')]) -> dict[str, str]:
    """配置缓存设置"""
    return make_jsonrpc_request('configure_cache_settings', max_size, max_memory_mb, ttl_seconds)

@mcp.tool()
def decrypt_encoded_strings(min_length: Annotated[int, Field(description='最小字符串长度')]=8, max_count: Annotated[int, Field(description='最大返回数量')]=100) -> list[EncodedString]:
    """解密和识别编码字符串"""
    return make_jsonrpc_request('decrypt_encoded_strings', min_length, max_count)

@mcp.tool()
def extract_license_strings(include_trial: Annotated[bool, Field(description='包含试用版相关字符串')]=True) -> list[LicenseString]:
    """提取和分类许可证相关字符串"""
    return make_jsonrpc_request('extract_license_strings', include_trial)

@mcp.tool()
def analyze_error_messages(severity_filter: Annotated[Optional[str], Field(description='严重性过滤器 (critical/high/medium/low)')]=None) -> list[ErrorMessage]:
    """分析错误消息和异常信息"""
    return make_jsonrpc_request('analyze_error_messages', severity_filter)

@mcp.tool()
def find_resource_strings(resource_type: Annotated[Optional[str], Field(description='资源类型过滤器')]=None) -> list[ResourceString]:
    """查找和分类GUI资源字符串"""
    return make_jsonrpc_request('find_resource_strings', resource_type)

@mcp.tool()
def detect_protection_type(analysis_area: Annotated[Optional[str], Field(description='分析区域地址（可选）')]=None) -> list[ProtectionType]:
    """检测程序保护类型和特征"""
    return make_jsonrpc_request('detect_protection_type', analysis_area)

@mcp.tool()
def generate_analysis_strategy(protection_info: Annotated[str, Field(description='保护信息JSON字符串')]) -> AnalysisStrategy:
    """根据保护类型生成分析策略"""
    return make_jsonrpc_request('generate_analysis_strategy', protection_info)

@mcp.tool()
def execute_batch_analysis(task_list: Annotated[str, Field(description='任务列表JSON字符串')], execution_mode: Annotated[str, Field(description='执行模式：sequential/parallel')]='sequential') -> list[BatchTask]:
    """执行批量分析任务"""
    return make_jsonrpc_request('execute_batch_analysis', task_list, execution_mode)

@mcp.tool()
def generate_crack_report(analysis_results: Annotated[str, Field(description='分析结果JSON字符串')], report_type: Annotated[str, Field(description='报告类型：summary/detailed')]='summary') -> CrackReport:
    """生成破解分析报告"""
    return make_jsonrpc_request('generate_crack_report', analysis_results, report_type)

@mcp.tool()
def get_workflow_status() -> dict[str, Any]:
    """获取工作流引擎状态 - 增强版包含动态分析功能"""
    return make_jsonrpc_request('get_workflow_status')

@mcp.tool()
def analyze_javascript_patterns() -> list[JavaScriptPattern]:
    """分析JavaScript代码模式和安全问题"""
    return make_jsonrpc_request('analyze_javascript_patterns')

@mcp.tool()
def discover_api_endpoints() -> list[APIEndpoint]:
    """发现API端点和网络请求"""
    return make_jsonrpc_request('discover_api_endpoints')

@mcp.tool()
def extract_web_resources() -> list[WebResource]:
    """提取Web资源文件分析"""
    return make_jsonrpc_request('extract_web_resources')

@mcp.tool()
def analyze_client_side_validation() -> list[ClientSideValidation]:
    """分析客户端验证逻辑"""
    return make_jsonrpc_request('analyze_client_side_validation')

@mcp.tool()
def identify_web_crypto_patterns() -> list[WebCryptoPattern]:
    """识别Web加密实现模式"""
    return make_jsonrpc_request('identify_web_crypto_patterns')

@mcp.tool()
def scan_web_vulnerabilities() -> dict[str, Any]:
    """综合Web漏洞扫描"""
    return make_jsonrpc_request('scan_web_vulnerabilities')

@mcp.tool()
def generate_crack_strategies(target_analysis: Annotated[str, Field(description='目标分析数据（JSON字符串）')]) -> list[CrackStrategy]:
    """生成智能破解策略"""
    return make_jsonrpc_request('generate_crack_strategies', target_analysis)

@mcp.tool()
def create_advanced_bypass(protection_type: Annotated[str, Field(description='保护类型')], target_address: Annotated[str, Field(description='目标地址')]) -> AdvancedBypass:
    """创建高级绕过技术"""
    return make_jsonrpc_request('create_advanced_bypass', protection_type, target_address)

@mcp.tool()
def build_exploit_chain(vulnerability_points: Annotated[list[str], Field(description='漏洞点列表')]) -> ExploitChain:
    """构建漏洞利用链"""
    return make_jsonrpc_request('build_exploit_chain', vulnerability_points)

@mcp.tool()
def apply_intelligent_patch(patch_strategy: Annotated[str, Field(description='补丁策略')], target_addresses: Annotated[list[str], Field(description='目标地址列表')]) -> IntelligentPatch:
    """应用智能补丁方案"""
    return make_jsonrpc_request('apply_intelligent_patch', patch_strategy, target_addresses)

@mcp.tool()
def optimize_crack_workflow(target_analysis: Annotated[str, Field(description='目标分析数据（JSON字符串）')], user_preferences: Annotated[str, Field(description='用户偏好设置（JSON字符串）')]) -> dict[str, Any]:
    """优化破解工作流程"""
    return make_jsonrpc_request('optimize_crack_workflow', target_analysis, user_preferences)

@mcp.tool()
def start_behavior_monitoring() -> dict[str, str]:
    """启动动态行为监控"""
    return make_jsonrpc_request('start_behavior_monitoring')

@mcp.tool()
def stop_behavior_monitoring() -> dict[str, str]:
    """停止动态行为监控"""
    return make_jsonrpc_request('stop_behavior_monitoring')

@mcp.tool()
def capture_api_calls(duration_seconds: Annotated[int, Field(description='监控持续时间（秒）')]=10) -> list[APICall]:
    """捕获API调用（基于IDA调试器事件）"""
    return make_jsonrpc_request('capture_api_calls', duration_seconds)

@mcp.tool()
def monitor_memory_access(target_address: Annotated[str, Field(description='目标内存地址')], size: Annotated[int, Field(description='监控大小（字节）')]=4) -> list[MemoryAccess]:
    """监控内存访问"""
    return make_jsonrpc_request('monitor_memory_access', target_address, size)

@mcp.tool()
def track_process_interactions() -> list[ProcessInteraction]:
    """跟踪进程交互"""
    return make_jsonrpc_request('track_process_interactions')

@mcp.tool()
def monitor_network_activity() -> list[NetworkActivity]:
    """监控网络活动"""
    return make_jsonrpc_request('monitor_network_activity')

@mcp.tool()
def analyze_behavior_patterns() -> dict[str, Any]:
    """分析行为模式"""
    return make_jsonrpc_request('analyze_behavior_patterns')

@mcp.tool()
def detect_evasion_techniques() -> list[dict[str, Any]]:
    """检测逃避技术"""
    return make_jsonrpc_request('detect_evasion_techniques')

@mcp.tool()
def generate_behavior_report() -> dict[str, Any]:
    """生成行为分析报告"""
    return make_jsonrpc_request('generate_behavior_report')

@mcp.tool()
def analyze_custom_encryption(target_function: Annotated[str, Field(description='目标函数地址')]) -> DecryptionResult:
    """分析自定义加密算法"""
    return make_jsonrpc_request('analyze_custom_encryption', target_function)

@mcp.tool()
def analyze_key_derivation_function(function_address: Annotated[str, Field(description='函数地址')]) -> KeyDerivationAnalysis:
    """分析密钥推导函数"""
    return make_jsonrpc_request('analyze_key_derivation_function', function_address)

@mcp.tool()
def identify_custom_cipher_patterns() -> list[CustomCipherPattern]:
    """识别自定义密码实现模式"""
    return make_jsonrpc_request('identify_custom_cipher_patterns')

@mcp.tool()
def analyze_config_encryption() -> list[ConfigFileAnalysis]:
    """分析配置文件加密"""
    return make_jsonrpc_request('analyze_config_encryption')

@mcp.tool()
def extract_encryption_constants() -> list[dict[str, Any]]:
    """提取加密常数和魔数"""
    return make_jsonrpc_request('extract_encryption_constants')

@mcp.tool()
def detect_buffer_overflows() -> list[VulnerabilityPoint]:
    """检测潜在的缓冲区溢出点"""
    return make_jsonrpc_request('detect_buffer_overflows')

@mcp.tool()
def analyze_unsafe_functions() -> list[UnsafeFunction]:
    """分析不安全函数使用"""
    return make_jsonrpc_request('analyze_unsafe_functions')

@mcp.tool()
def detect_integer_overflows() -> list[IntegerOverflow]:
    """检测整数溢出漏洞"""
    return make_jsonrpc_request('detect_integer_overflows')

@mcp.tool()
def comprehensive_vulnerability_scan() -> dict[str, Any]:
    """综合漏洞扫描"""
    return make_jsonrpc_request('comprehensive_vulnerability_scan')

@mcp.tool()
def start_dynamic_analysis(target_address: Annotated[str, Field(description='目标分析地址')]=None) -> dict[str, Any]:
    """启动动态分析会话"""
    return make_jsonrpc_request('start_dynamic_analysis', target_address)

@mcp.tool()
def set_breakpoint(address: Annotated[str, Field(description='断点地址')], condition: Annotated[str, Field(description='断点条件（可选）')]=None, description: Annotated[str, Field(description='断点描述')]=None) -> dict[str, Any]:
    """设置断点"""
    return make_jsonrpc_request('set_breakpoint', address, condition, description)

@mcp.tool()
def step_execution(step_type: Annotated[str, Field(description='单步类型：step_into, step_over, step_out')]='step_into') -> dict[str, Any]:
    """单步执行"""
    return make_jsonrpc_request('step_execution', step_type)

@mcp.tool()
def manage_breakpoints(action: Annotated[str, Field(description='操作类型：list, enable, disable, remove')]='list', breakpoint_id: Annotated[str, Field(description='断点ID（可选）')]=None) -> dict[str, Any]:
    """管理断点"""
    return make_jsonrpc_request('manage_breakpoints', action, breakpoint_id)

@mcp.tool()
def dump_process_memory(start_address: Annotated[str, Field(description='起始地址')], size: Annotated[int, Field(description='转储大小（字节）')]=256) -> dict[str, Any]:
    """转储进程内存"""
    return make_jsonrpc_request('dump_process_memory', start_address, size)

@mcp.tool()
def list_workflow_templates() -> dict[str, Any]:
    """列出所有可用的工作流模板"""
    return make_jsonrpc_request('list_workflow_templates')

@mcp.tool()
def get_workflow_template(template_id: Annotated[str, Field(description='工作流模板ID')]) -> dict[str, Any]:
    """获取工作流模板详细信息"""
    return make_jsonrpc_request('get_workflow_template', template_id)

@mcp.tool()
def execute_workflow(template_id: Annotated[str, Field(description='工作流模板ID')]) -> dict[str, Any]:
    """执行工作流"""
    return make_jsonrpc_request('execute_workflow', template_id)

@mcp.tool()
def get_workflow_execution_status(execution_id: Annotated[str, Field(description='工作流执行ID')]) -> dict[str, Any]:
    """获取工作流执行状态"""
    return make_jsonrpc_request('get_workflow_execution_status', execution_id)

@mcp.tool()
def recommend_tools(analysis_target: Annotated[str, Field(description='分析目标类型：binary, function, memory, strings')]='binary') -> dict[str, Any]:
    """智能工具推荐"""
    return make_jsonrpc_request('recommend_tools', analysis_target)

@mcp.tool()
def create_custom_workflow(name: Annotated[str, Field(description='工作流名称')], description: Annotated[str, Field(description='工作流描述')], tools: Annotated[list[str], Field(description='工具列表')]) -> dict[str, Any]:
    """创建自定义工作流"""
    return make_jsonrpc_request('create_custom_workflow', name, description, tools)

@mcp.tool()
def execute_crack_strategy(strategy_id: Annotated[str, Field(description='策略ID')], target_addresses: Annotated[list[str], Field(description='目标地址列表')]=None) -> dict[str, Any]:
    """执行破解策略"""
    return make_jsonrpc_request('execute_crack_strategy', strategy_id, target_addresses)

@mcp.tool()
def analyze_protection_effectiveness(target_area: Annotated[str, Field(description='分析区域地址')]=None) -> dict[str, Any]:
    """分析保护机制有效性"""
    return make_jsonrpc_request('analyze_protection_effectiveness', target_area)

@mcp.tool()
def monitor_api_calls(api_filter: Annotated[str, Field(description='API过滤器（可选）')]=None, duration: Annotated[int, Field(description='监控时长（秒）')]=30) -> dict[str, Any]:
    """监控API调用"""
    return make_jsonrpc_request('monitor_api_calls', api_filter, duration)

@mcp.tool()
def trace_memory_access(start_address: Annotated[str, Field(description='起始地址')], size: Annotated[int, Field(description='监控大小')]=1024, access_type: Annotated[str, Field(description='访问类型：read, write, execute, all')]='all') -> dict[str, Any]:
    """追踪内存访问"""
    return make_jsonrpc_request('trace_memory_access', start_address, size, access_type)

@mcp.tool()
def get_dynamic_analysis_summary(session_id: Annotated[str, Field(description='会话ID')]=None) -> dict[str, Any]:
    """获取动态分析摘要"""
    return make_jsonrpc_request('get_dynamic_analysis_summary', session_id)

@mcp.tool()
def optimize_large_file_processing(file_size_mb: Annotated[int, Field(description='文件大小（MB）')]=None) -> dict[str, Any]:
    """优化大文件处理"""
    return make_jsonrpc_request('optimize_large_file_processing', file_size_mb)

@mcp.tool()
def enable_parallel_processing(task_type: Annotated[str, Field(description='任务类型：analysis, search, batch')]='analysis') -> dict[str, Any]:
    """启用并行处理"""
    return make_jsonrpc_request('enable_parallel_processing', task_type)

@mcp.tool()
def optimize_memory_management() -> dict[str, Any]:
    """优化内存管理"""
    return make_jsonrpc_request('optimize_memory_management')

@mcp.tool()
def get_performance_statistics() -> dict[str, Any]:
    """获取性能统计信息"""
    return make_jsonrpc_request('get_performance_statistics')

@mcp.tool()
def test_new_function() -> dict[str, Any]:
    """测试新增功能是否正确注册"""
    return make_jsonrpc_request('test_new_function')

