{"sourceFile": "docs/破解能力增强实施方案.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754217382354, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754217382354, "name": "Commit-0", "content": "# IDA Pro MCP 破解能力增强实施方案\r\n\r\n> **作者**: <PERSON> (deepractice.ai)  \r\n> **创建时间**: 2025年8月3日  \r\n> **方案原则**: 奥卡姆剃刀 - 用最简方案解决问题\r\n\r\n## 🎯 核心目标\r\n\r\n在现有ida-pro-mcp架构上**直接添加**专业破解分析能力，充分发挥IDA Pro 9的逆向工程潜力。\r\n\r\n## 📊 矛盾分析\r\n\r\n**根本矛盾**: 现有MCP系统功能完善 vs 缺乏专业破解分析能力\r\n\r\n**解决策略**: 在mcp-plugin.py中添加@jsonrpc函数，保持架构一致性，立即可用\r\n\r\n## 🛠️ 技术实施方案\r\n\r\n### 阶段一：控制流分析核心 (优先级最高)\r\n\r\n#### 1.1 关键验证点识别\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef identify_verification_points(function_address: str) -> Dict[str, Any]:\r\n    \"\"\"\r\n    自动识别函数内的关键验证点\r\n    - 扫描cmp、test、jz、jnz等比较和跳转指令\r\n    - 识别许可证验证、密码检查等关键逻辑\r\n    - 返回验证点地址、类型和修改建议\r\n    \"\"\"\r\n```\r\n\r\n**技术要点**:\r\n- 使用ida_ua模块解析指令\r\n- 模式识别常见验证逻辑\r\n- 提供patch建议(nop、强制跳转等)\r\n\r\n#### 1.2 跳转条件分析\r\n```python\r\n@jsonrpc\r\n@idaread  \r\ndef analyze_jump_conditions(address: str, depth: int = 5) -> Dict[str, Any]:\r\n    \"\"\"\r\n    深度分析条件跳转逻辑\r\n    - 分析跳转目标和跳转条件\r\n    - 提供修改策略(nop、强制跳转等)\r\n    - 支持多级跳转追踪\r\n    \"\"\"\r\n```\r\n\r\n**技术要点**:\r\n- 递归分析跳转链\r\n- 识别关键分支逻辑\r\n- 生成绕过方案\r\n\r\n#### 1.3 函数调用链追踪\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef trace_function_call_chain(start_address: str, max_depth: int = 10) -> Dict[str, Any]:\r\n    \"\"\"\r\n    递归构建函数调用关系树\r\n    - 基于现有get_xrefs_to扩展\r\n    - 识别关键路径和潜在破解点\r\n    - 支持循环调用检测\r\n    \"\"\"\r\n```\r\n\r\n**技术要点**:\r\n- 复用现有get_xrefs_to函数\r\n- 构建调用关系图\r\n- 识别关键执行路径\r\n\r\n### 阶段二：加密算法识别 (快速见效)\r\n\r\n#### 2.1 加密特征识别\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef identify_crypto_algorithms(start_address: str, end_address: str) -> Dict[str, Any]:\r\n    \"\"\"\r\n    识别常见加密算法\r\n    - AES S-box特征码识别\r\n    - RSA大数运算模式\r\n    - DES轮函数特征\r\n    \"\"\"\r\n```\r\n\r\n**已知特征库**:\r\n- AES S-box: `63 7c 77 7b f2 6b 6f c5 30 01 67 2b fe d7 ab 76`\r\n- DES IP置换表、PC1/PC2置换表\r\n- RSA模运算特征\r\n\r\n#### 2.2 密钥定位\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef locate_crypto_keys(algorithm_type: str, search_range: Dict) -> List[Dict]:\r\n    \"\"\"\r\n    定位加密密钥存储位置\r\n    - 静态密钥扫描\r\n    - 动态密钥生成分析\r\n    \"\"\"\r\n```\r\n\r\n### 阶段三：反调试检测与绕过 (实用性强)\r\n\r\n#### 3.1 反调试技术检测\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef detect_anti_debug_techniques(binary_range: Dict) -> List[Dict]:\r\n    \"\"\"\r\n    检测反调试技术\r\n    - IsDebuggerPresent API调用\r\n    - 时间检查 (rdtsc指令)\r\n    - 异常处理反调试\r\n    - 内存保护检查\r\n    \"\"\"\r\n```\r\n\r\n**检测模式**:\r\n- API调用模式扫描\r\n- 时间相关指令识别  \r\n- 异常处理流程分析\r\n- 内存访问模式检测\r\n\r\n#### 3.2 绕过策略生成\r\n```python\r\n@jsonrpc\r\n@idaread\r\ndef generate_bypass_strategies(anti_debug_points: List) -> Dict[str, Any]:\r\n    \"\"\"\r\n    为反调试点生成绕过方案\r\n    - API Hook策略\r\n    - 指令Patch方案\r\n    - 返回值修改\r\n    \"\"\"\r\n```\r\n\r\n### 阶段四：内存补丁系统 (安全可控)\r\n\r\n#### 4.1 安全补丁管理\r\n```python\r\n@jsonrpc\r\n@idawrite\r\ndef create_memory_patch(address: str, patch_data: bytes, patch_type: str) -> Dict[str, Any]:\r\n    \"\"\"\r\n    创建安全的内存补丁\r\n    - 补丁前置检查\r\n    - 原始数据备份\r\n    - 补丁效果验证\r\n    \"\"\"\r\n```\r\n\r\n**安全机制**:\r\n- 补丁前内存校验\r\n- 自动备份原始数据\r\n- 支持一键回滚\r\n- 补丁冲突检测\r\n\r\n## 📂 文件修改计划\r\n\r\n### 主要修改文件\r\n1. **src/ida_pro_mcp/mcp-plugin.py** - 添加所有@jsonrpc函数\r\n2. **src/ida_pro_mcp/server_generated.py** - 自动同步MCP工具注册\r\n3. **README.md** - 更新功能列表\r\n\r\n### 代码插入位置\r\n- 在现有@jsonrpc函数后、`class MCP(idaapi.plugin_t):` 之前\r\n- 保持现有代码结构不变\r\n- 使用相同的错误处理模式\r\n\r\n## 🚀 实施时间表\r\n\r\n### 第一天：控制流分析核心\r\n- identify_verification_points\r\n- analyze_jump_conditions  \r\n- trace_function_call_chain\r\n\r\n### 第二天：加密算法识别\r\n- identify_crypto_algorithms\r\n- locate_crypto_keys\r\n\r\n### 第三天：反调试检测\r\n- detect_anti_debug_techniques\r\n- generate_bypass_strategies\r\n\r\n### 第四天：补丁系统\r\n- create_memory_patch\r\n- patch管理功能\r\n\r\n## 🔧 技术依赖\r\n\r\n### IDA Pro API模块\r\n- **ida_ua**: 指令解析和分析\r\n- **ida_bytes**: 内存读写操作\r\n- **ida_xref**: 交叉引用(已有)\r\n- **ida_funcs**: 函数操作(已有)\r\n- **ida_kernwin**: 用户界面交互\r\n\r\n### 现有功能复用\r\n- **get_xrefs_to**: 用于调用链分析\r\n- **disassemble_function**: 用于指令分析\r\n- **convert_number**: 用于地址和数值转换\r\n\r\n## ✅ 验证方案\r\n\r\n### 功能测试\r\n1. **目标程序**: 使用简单的crackme程序测试\r\n2. **验证点识别**: 确认能找到关键比较指令\r\n3. **绕过效果**: 验证生成的patch是否有效\r\n\r\n### 兼容性测试\r\n1. **现有功能**: 确保不影响原有MCP功能\r\n2. **错误处理**: 验证新功能的异常处理\r\n3. **性能影响**: 确保不影响IDA Pro运行性能\r\n\r\n## 🎯 成功标准\r\n\r\n1. **功能完整性**: 所有新增@jsonrpc函数正常工作\r\n2. **架构一致性**: 与现有代码风格完全一致\r\n3. **实用性验证**: 能够分析真实的保护程序\r\n4. **部署简单**: 保持现有的插件部署方式\r\n\r\n## 📋 风险评估\r\n\r\n### 低风险\r\n- 基于现有成熟架构扩展\r\n- 使用IDA Pro官方API\r\n- 渐进式开发验证\r\n\r\n### 需要注意\r\n- @idawrite权限的安全使用\r\n- 大型二进制文件的性能问题\r\n- 复杂混淆代码的识别准确性\r\n\r\n---\r\n\r\n**结论**: 这是一个基于奥卡姆剃刀原则的务实方案，直接在现有架构上扩展，快速见效，风险可控。立即开始实施！\r\n"}]}