#!/usr/bin/env python3
"""
IDA Pro MCP插件集成测试脚本
测试延迟初始化、缓存系统和核心功能组件
"""

import sys
import json
import time
from typing import Any, Dict

# 模拟IDA环境
def mock_ida_environment():
    """模拟IDA API环境用于测试"""
    # 创建模拟的IDA模块
    modules_to_mock = [
        'idaapi', 'ida_kernwin', 'ida_hexrays', 'idautils', 'idc',
        'ida_funcs', 'ida_gdl', 'ida_lines', 'ida_idaapi', 'ida_nalt',
        'ida_bytes', 'ida_typeinf', 'ida_xref', 'ida_entry', 'ida_idd',
        'ida_dbg', 'ida_name', 'ida_ida', 'ida_frame'
    ]
    
    for module_name in modules_to_mock:
        if module_name not in sys.modules:
            mock_module = type(sys)(module_name)
            # 添加常用常量和函数
            setattr(mock_module, 'BADADDR', 0xFFFFFFFFFFFFFFFF)
            setattr(mock_module, 'get_imagebase', lambda: 0x10000000)
            setattr(mock_module, 'get_func', lambda x: None)
            setattr(mock_module, 'init_hexrays_plugin', lambda: True)
            setattr(mock_module, 'MFF_READ', 1)
            setattr(mock_module, 'MFF_WRITE', 2)
            setattr(mock_module, 'MFF_FAST', 0)
            sys.modules[module_name] = mock_module

def test_lazy_module_manager():
    """测试延迟模块管理器"""
    print("=" * 50)
    print("测试延迟模块管理器")
    print("=" * 50)
    
    # 添加路径并导入
    import sys
    sys.path.insert(0, 'src')
    
    # 直接加载模块文件
    import importlib.util
    spec = importlib.util.spec_from_file_location("mcp_plugin", "src/ida_pro_mcp/mcp-plugin.py")
    mcp_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(mcp_module)
    
    lazy_module_manager = mcp_module.lazy_module_manager
    
    # 测试模块初始化
    test_modules = ['control_flow', 'crypto', 'anti_debug', 'license']
    
    for module_name in test_modules:
        # 测试初始化前状态
        assert not lazy_module_manager.is_initialized(module_name), f"{module_name} 不应该已初始化"
        
        # 初始化模块
        lazy_module_manager.initialize_module(module_name)
        
        # 测试初始化后状态
        assert lazy_module_manager.is_initialized(module_name), f"{module_name} 应该已初始化"
        
        # 获取模块数据
        data = lazy_module_manager.get_module_data(module_name)
        assert isinstance(data, dict), f"{module_name} 数据应该是字典类型"
        
        print(f"✓ {module_name} 模块初始化成功，数据keys: {list(data.keys())[:3]}...")
    
    # 测试使用统计
    stats = lazy_module_manager.get_usage_stats()
    print(f"✓ 模块使用统计: {stats}")
    
    print("延迟模块管理器测试通过！\n")

def test_analysis_cache():
    """测试智能缓存系统"""
    print("=" * 50)
    print("测试智能缓存系统")
    print("=" * 50)
    
    from src.ida_pro_mcp.mcp_plugin import analysis_cache
    
    # 测试缓存存储和检索
    test_data = {"result": "test_analysis_result", "timestamp": time.time()}
    
    # 存储数据
    analysis_cache.put("test_function", ("arg1", "arg2"), {"param": "value"}, test_data)
    
    # 检索数据
    hit, cached_data = analysis_cache.get("test_function", ("arg1", "arg2"), {"param": "value"})
    assert hit, "缓存命中应该成功"
    assert cached_data == test_data, "缓存数据应该匹配"
    
    # 测试缓存未命中
    hit, _ = analysis_cache.get("non_existent", (), {})
    assert not hit, "不存在的缓存应该未命中"
    
    # 测试缓存统计
    stats = analysis_cache.get_stats()
    assert 'hit_rate' in stats, "统计信息应该包含命中率"
    
    print(f"✓ 缓存系统测试通过，命中率: {stats['hit_rate']}")
    print(f"✓ 内存使用: {stats['memory_usage_mb']} MB")
    print(f"✓ 条目数量: {stats['total_entries']}")
    
    print("智能缓存系统测试通过！\n")

def test_rpc_registry():
    """测试RPC注册表功能"""
    print("=" * 50)
    print("测试RPC注册表")
    print("=" * 50)
    
    from src.ida_pro_mcp.mcp_plugin import rpc_registry
    
    # 测试方法注册
    @rpc_registry.register
    def test_method(param1: str, param2: int) -> str:
        return f"Processed: {param1} - {param2}"
    
    # 测试调度
    result = rpc_registry.dispatch("test_method", {"param1": "hello", "param2": 42})
    expected = "Processed: hello - 42"
    assert result == expected, f"期望 {expected}, 获得 {result}"
    
    print(f"✓ RPC方法注册和调度成功: {result}")
    
    # 测试错误处理
    try:
        rpc_registry.dispatch("non_existent_method", {})
        assert False, "应该抛出异常"
    except Exception as e:
        print(f"✓ 错误处理正常: {type(e).__name__}")
    
    print("RPC注册表测试通过！\n")

def test_workflow_engine():
    """测试工作流引擎"""
    print("=" * 50)
    print("测试工作流引擎")
    print("=" * 50)
    
    from src.ida_pro_mcp.mcp_plugin import workflow_engine
    
    # 测试保护特征分析
    features = workflow_engine._analyze_protection_patterns()
    print(f"✓ 保护特征分析: 检测到 {len(features)} 个特征")
    
    # 测试策略置信度计算
    confidence = workflow_engine._calculate_strategy_confidence(['anti_debug', 'timing_check'])
    assert 0.0 <= confidence <= 1.0, "置信度应该在0-1之间"
    print(f"✓ 策略置信度计算: {confidence:.2f}")
    
    # 测试任务管理
    print(f"✓ 运行中任务: {len(workflow_engine.running_tasks)}")
    print(f"✓ 已完成任务: {len(workflow_engine.completed_tasks)}")
    print(f"✓ 策略缓存: {len(workflow_engine.strategy_cache)}")
    
    print("工作流引擎测试通过！\n")

def test_memory_usage():
    """测试内存使用情况"""
    print("=" * 50)
    print("内存使用测试")
    print("=" * 50)
    
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    print(f"✓ RSS内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
    print(f"✓ VMS内存使用: {memory_info.vms / 1024 / 1024:.2f} MB")
    
    # 简单的内存负载测试
    from src.ida_pro_mcp.mcp_plugin import analysis_cache
    
    # 填充缓存
    for i in range(100):
        test_data = {"large_data": "x" * 1000, "index": i}
        analysis_cache.put(f"test_{i}", (i,), {}, test_data)
    
    memory_after = psutil.Process(os.getpid()).memory_info()
    print(f"✓ 缓存填充后内存: {memory_after.rss / 1024 / 1024:.2f} MB")
    
    print("内存使用测试通过！\n")

def run_performance_test():
    """性能测试"""
    print("=" * 50)
    print("性能测试")
    print("=" * 50)
    
    from src.ida_pro_mcp.mcp_plugin import lazy_module_manager, analysis_cache
    
    # 测试延迟初始化性能
    start_time = time.time()
    for _ in range(1000):
        lazy_module_manager.get_module_data('control_flow')
    init_time = time.time() - start_time
    print(f"✓ 1000次模块数据获取耗时: {init_time:.4f}秒")
    
    # 测试缓存性能
    start_time = time.time()
    for i in range(1000):
        analysis_cache.get(f"perf_test_{i % 10}", (i,), {})
    cache_time = time.time() - start_time
    print(f"✓ 1000次缓存查询耗时: {cache_time:.4f}秒")
    
    print("性能测试通过！\n")

def main():
    """主测试函数"""
    print("IDA Pro MCP 插件集成测试")
    print("=" * 70)
    
    # 模拟IDA环境
    mock_ida_environment()
    
    try:
        # 运行各项测试
        test_lazy_module_manager()
        test_analysis_cache()
        test_rpc_registry()
        test_workflow_engine()
        test_memory_usage()
        run_performance_test()
        
        print("=" * 70)
        print("🎉 所有集成测试通过！")
        print("=" * 70)
        
        # 输出最终统计
        from src.ida_pro_mcp.mcp_plugin import lazy_module_manager, analysis_cache
        
        print("最终统计信息:")
        print(f"- 已初始化模块: {sum(lazy_module_manager.module_states.values())}")
        print(f"- 缓存命中率: {analysis_cache.get_stats()['hit_rate']}")
        print(f"- 缓存条目数: {analysis_cache.get_stats()['total_entries']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
