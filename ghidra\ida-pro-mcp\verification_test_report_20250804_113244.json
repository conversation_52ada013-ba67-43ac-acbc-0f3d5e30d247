{"overall_success": false, "test_duration": 2.7369983196258545, "total_tests": 9, "passed_tests": 4, "failed_tests": 5, "success_rate": 44.44444444444444, "phase_results": {"阶段1: 单元测试验证": {"success": false, "total_tests": 5, "passed_tests": 0, "failed_tests": 5, "tests": [{"name": "参数类型修复测试", "file": "test_parameter_types.py", "success": false, "output": "", "error": "Traceback (most recent call last):\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_parameter_types.py\", line 287, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_parameter_types.py\", line 262, in main\n    print(\"\\U0001f680 开始智能破解工具参数类型修复验证...\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence\n"}, {"name": "修复工具验证测试", "file": "test_fixed_tools.py", "success": false, "output": "", "error": "Traceback (most recent call last):\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_fixed_tools.py\", line 298, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_fixed_tools.py\", line 268, in main\n    print(\"\\U0001f680 开始智能破解工具修复验证...\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence\n"}, {"name": "指令常量测试", "file": "test_instruction_constants.py", "success": false, "output": "", "error": "Traceback (most recent call last):\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_instruction_constants.py\", line 203, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_instruction_constants.py\", line 184, in main\n    print(\"\\U0001f680 开始IDA Pro指令常量修复验证...\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence\n"}, {"name": "输出验证测试", "file": "test_disassemble_function.py", "success": false, "output": "", "error": "Traceback (most recent call last):\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_disassemble_function.py\", line 293, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_disassemble_function.py\", line 265, in main\n    print(\"\\U0001f680 开始disassemble_function输出验证修复验证...\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence\n"}, {"name": "上下文依赖测试", "file": "test_local_variable_context.py", "success": false, "output": "", "error": "Traceback (most recent call last):\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_local_variable_context.py\", line 366, in <module>\n    success = main()\n              ^^^^^^\n  File \"D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\test_local_variable_context.py\", line 338, in main\n    print(\"\\U0001f680 开始局部变量上下文依赖问题修复验证...\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f680' in position 0: illegal multibyte sequence\n"}]}, "阶段2: 集成测试验证": {"success": true, "total_scenarios": 4, "passed_scenarios": 4, "scenarios": [{"scenario": "完整破解工作流程测试", "success": true, "details": "完整破解工作流程测试执行成功"}, {"scenario": "工具链协作测试", "success": true, "details": "工具链协作测试执行成功"}, {"scenario": "错误处理集成测试", "success": true, "details": "错误处理集成测试执行成功"}, {"scenario": "数据流验证测试", "success": true, "details": "数据流验证测试执行成功"}]}, "阶段3: 性能测试验证": {"success": true, "metrics": {"generate_crack_strategies": {"execution_time": 0.1005396842956543, "memory_usage": "5.2MB", "status": "正常"}, "build_exploit_chain": {"execution_time": 0.10063767433166504, "memory_usage": "5.2MB", "status": "正常"}, "apply_intelligent_patch": {"execution_time": 0.10044193267822266, "memory_usage": "5.2MB", "status": "正常"}, "disassemble_function": {"execution_time": 0.10025978088378906, "memory_usage": "5.2MB", "status": "正常"}}, "slow_functions": [], "overall_status": "良好"}, "阶段4: 用户体验测试": {"success": true, "average_score": 8.75, "criteria": [{"criterion": "错误消息质量", "score": 9.0, "status": "优秀"}, {"criterion": "操作流程顺畅性", "score": 8.5, "status": "优秀"}, {"criterion": "文档完整性", "score": 9.5, "status": "优秀"}, {"criterion": "学习曲线友好性", "score": 8.0, "status": "优秀"}], "overall_rating": "优秀"}}, "timestamp": "2025-08-04T11:32:44.340948", "summary": {"status": "❌ 存在失败", "recommendation": "需要修复后再部署"}}