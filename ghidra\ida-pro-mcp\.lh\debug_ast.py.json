{"sourceFile": "debug_ast.py", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1754234922106, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1754234922106, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\n调试AST解析问题的脚本\r\n\"\"\"\r\nimport ast\r\nimport sys\r\nfrom pathlib import Path\r\n\r\ndef debug_ast_parsing():\r\n    \"\"\"调试AST解析问题\"\"\"\r\n    plugin_file = Path(\"src/ida_pro_mcp/mcp-plugin.py\")\r\n    \r\n    if not plugin_file.exists():\r\n        print(f\"文件不存在: {plugin_file}\")\r\n        return\r\n    \r\n    print(f\"正在解析文件: {plugin_file}\")\r\n    \r\n    try:\r\n        with open(plugin_file, 'r', encoding='utf-8') as f:\r\n            source = f.read()\r\n        \r\n        # 解析AST\r\n        module = ast.parse(source)\r\n        \r\n        # 查找所有带@jsonrpc装饰器的函数\r\n        class JSONRPCVisitor(ast.NodeVisitor):\r\n            def visit_FunctionDef(self, node):\r\n                # 检查是否有@jsonrpc装饰器\r\n                has_jsonrpc = False\r\n                for decorator in node.decorator_list:\r\n                    if isinstance(decorator, ast.Name) and decorator.id == \"jsonrpc\":\r\n                        has_jsonrpc = True\r\n                        break\r\n                \r\n                if has_jsonrpc:\r\n                    print(f\"\\n函数: {node.name}\")\r\n                    print(f\"参数数量: {len(node.args.args)}\")\r\n                    \r\n                    for i, arg in enumerate(node.args.args):\r\n                        arg_name = arg.arg\r\n                        arg_type = arg.annotation\r\n                        print(f\"  参数 {i}: {arg_name}\")\r\n                        \r\n                        if arg_type is None:\r\n                            print(f\"    错误: 缺少类型注解\")\r\n                        elif isinstance(arg_type, ast.Subscript):\r\n                            if isinstance(arg_type.value, ast.Name):\r\n                                print(f\"    类型: {arg_type.value.id}\")\r\n                                if arg_type.value.id != \"Annotated\":\r\n                                    print(f\"    错误: 期望Annotated，实际是{arg_type.value.id}\")\r\n                            else:\r\n                                print(f\"    错误: 复杂的订阅类型: {ast.dump(arg_type.value)}\")\r\n                        else:\r\n                            print(f\"    错误: 非Annotated类型: {ast.dump(arg_type)}\")\r\n                \r\n                self.generic_visit(node)\r\n        \r\n        visitor = JSONRPCVisitor()\r\n        visitor.visit(module)\r\n        \r\n    except Exception as e:\r\n        print(f\"解析错误: {e}\")\r\n        import traceback\r\n        traceback.print_exc()\r\n\r\nif __name__ == \"__main__\":\r\n    debug_ast_parsing()\r\n"}]}