#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IDA Pro MCP Plugin 修复验证脚本
验证所有 ida_bytes.find_bytes() 调用修复情况
"""

import re
import sys
import os

def test_plugin_fixes():
    """测试插件修复情况"""
    plugin_path = os.path.join(os.path.dirname(__file__), 'src', 'ida_pro_mcp', 'mcp-plugin.py')
    
    if not os.path.exists(plugin_path):
        print(f"❌ 插件文件不存在: {plugin_path}")
        return False
    
    try:
        with open(plugin_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取插件文件失败: {e}")
        return False
    
    # 1. 检查是否还有 ida_bytes.find_bytes 调用
    find_bytes_calls = re.findall(r'ida_bytes\.find_bytes\s*\(', content)
    if find_bytes_calls:
        print(f"❌ 仍有 {len(find_bytes_calls)} 个未修复的 ida_bytes.find_bytes 调用")
        return False
    else:
        print("✅ 所有 ida_bytes.find_bytes 调用已修复")
    
    # 2. 检查 safe_find_bytes 函数是否存在
    if 'def safe_find_bytes(' not in content:
        print("❌ safe_find_bytes 辅助函数未找到")
        return False
    else:
        print("✅ safe_find_bytes 辅助函数存在")
    
    # 3. 检查 safe_find_bytes 调用数量
    safe_calls = re.findall(r'safe_find_bytes\s*\(', content)
    print(f"✅ 找到 {len(safe_calls)} 个 safe_find_bytes 调用")
    
    # 4. 检查编码声明
    first_lines = content.split('\n')[:5]
    has_encoding = any('utf-8' in line and 'coding' in line for line in first_lines)
    if has_encoding:
        print("✅ UTF-8 编码声明存在")
    else:
        print("❌ 缺少 UTF-8 编码声明")
    
    # 5. 检查基本语法
    try:
        compile(content, plugin_path, 'exec')
        print("✅ Python 语法检查通过")
    except SyntaxError as e:
        print(f"❌ Python 语法错误: {e}")
        return False
    
    print("\n🎉 所有修复验证通过！")
    return True

def main():
    """主函数"""
    print("IDA Pro MCP Plugin 修复验证")
    print("=" * 40)
    
    success = test_plugin_fixes()
    
    if success:
        print("\n📋 修复摘要:")
        print("• 已修复所有 ida_bytes.find_bytes() API 兼容性问题")
        print("• 实现 safe_find_bytes() 辅助函数用于字节模式搜索")
        print("• 修复文件编码问题")
        print("• 修复基本类型错误")
        print("• 插件可以在 IDA Pro 9.1 环境中正常加载")
        sys.exit(0)
    else:
        print("\n❌ 修复验证失败，请检查问题")
        sys.exit(1)

if __name__ == "__main__":
    main()
