#!/usr/bin/env python3
"""
IDA Pro MCP插件性能测试脚本
测试启动时间和内存占用
"""

import time
import sys
import os
import gc
from typing import Dict, Any

def mock_ida_modules():
    """模拟IDA模块以进行性能测试"""
    import types
    
    ida_modules = [
        'idaapi', 'ida_kernwin', 'ida_hexrays', 'idautils', 'idc',
        'ida_funcs', 'ida_gdl', 'ida_lines', 'ida_idaapi', 'ida_nalt',
        'ida_bytes', 'ida_typeinf', 'ida_xref', 'ida_entry', 'ida_idd',
        'ida_dbg', 'ida_name', 'ida_ida', 'ida_frame'
    ]
    
    for module_name in ida_modules:
        if module_name not in sys.modules:
            mock_module = types.ModuleType(module_name)
            
            # 添加通用属性
            setattr(mock_module, 'BADADDR', 0xFFFFFFFFFFFFFFFF)
            setattr(mock_module, 'get_imagebase', lambda: 0x10000000)
            setattr(mock_module, 'MFF_READ', 1)
            setattr(mock_module, 'MFF_WRITE', 2)
            setattr(mock_module, 'MFF_FAST', 0)
            
            # 为特定模块添加专门的属性
            if module_name == 'idaapi':
                setattr(mock_module, 'plugin_t', type('plugin_t', (), {}))
                setattr(mock_module, 'PLUGIN_KEEP', 1)
                setattr(mock_module, 'get_func', lambda x: None)
                setattr(mock_module, 'insn_t', type('insn_t', (), {}))
                setattr(mock_module, 'decode_insn', lambda x, y: False)
                
            elif module_name == 'ida_funcs':
                setattr(mock_module, 'func_t', type('func_t', (), {'get_name': lambda: 'test_func'}))
                setattr(mock_module, 'get_func_name', lambda x: 'test_func')
                
            elif module_name == 'ida_kernwin':
                setattr(mock_module, 'simpleline_t', type('simpleline_t', (), {}))
                setattr(mock_module, 'get_current_widget', lambda: None)
                
            elif module_name == 'ida_hexrays':
                setattr(mock_module, 'ctree_item_t', type('ctree_item_t', (), {}))
                setattr(mock_module, 'init_hexrays_plugin', lambda: True)
                setattr(mock_module, 'OPF_REUSE', 1)
                setattr(mock_module, 'open_pseudocode', lambda x, y: None)
                
            elif module_name == 'idautils':
                class MockStrings:
                    def __iter__(self):
                        # 返回一些模拟字符串
                        for i in range(5):
                            yield type('string_item', (), {'ea': 0x1000 + i, '__str__': lambda: f'test_string_{i}'})()
                
                setattr(mock_module, 'Strings', MockStrings)
                setattr(mock_module, 'Functions', lambda: [0x1000, 0x1100, 0x1200])
                
            elif module_name == 'idc':
                setattr(mock_module, 'get_item_size', lambda x: 4)
                setattr(mock_module, 'generate_disasm_line', lambda x, y: 'mov eax, ebx')
                setattr(mock_module, 'set_func_cmt', lambda x, y, z: True)
                
            elif module_name == 'ida_bytes':
                setattr(mock_module, 'get_bytes', lambda x, y: b'\x90\x90\x90\x90')
                setattr(mock_module, 'has_any_name', lambda x: True)
                setattr(mock_module, 'find_bytes', lambda x, y, z: 0xFFFFFFFFFFFFFFFF)
                
            elif module_name == 'ida_nalt':
                setattr(mock_module, 'get_tinfo', lambda x, y: False)
                
            elif module_name == 'ida_typeinf':
                setattr(mock_module, 'tinfo_t', type('tinfo_t', (), {}))
                setattr(mock_module, 'parse_decl', lambda x, y, z: (0, ''))
                
            elif module_name == 'ida_xref':
                setattr(mock_module, 'get_first_cref_to', lambda x: 0xFFFFFFFFFFFFFFFF)
                setattr(mock_module, 'get_next_cref_to', lambda x, y: 0xFFFFFFFFFFFFFFFF)
                
            elif module_name == 'ida_entry':
                setattr(mock_module, 'get_entry_qty', lambda: 1)
                setattr(mock_module, 'get_entry', lambda x: 0x1000)
                
            elif module_name == 'ida_name':
                setattr(mock_module, 'get_name_ea', lambda x, y: 0xFFFFFFFFFFFFFFFF)
                
            elif module_name == 'ida_frame':
                setattr(mock_module, 'get_frame', lambda x: None)
                
            # 添加HTTP服务器相关的模拟
            import http.server
            setattr(mock_module, 'BaseHTTPRequestHandler', http.server.BaseHTTPRequestHandler)
            setattr(mock_module, 'HTTPServer', http.server.HTTPServer)
            
            sys.modules[module_name] = mock_module

def test_plugin_load_time():
    """测试插件加载时间"""
    print("🚀 测试插件加载性能...")
    
    # 模拟IDA环境
    mock_ida_modules()
    
    # 测试加载时间
    start_time = time.time()
    
    try:
        # 动态加载插件
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "mcp_plugin", 
            "src/ida_pro_mcp/mcp-plugin.py"
        )
        
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            load_start = time.time()
            spec.loader.exec_module(module)
            load_time = time.time() - load_start
            
            total_time = time.time() - start_time
            
            print(f"✅ 插件加载成功")
            print(f"   📊 总耗时: {total_time:.3f} 秒")
            print(f"   📊 模块执行时间: {load_time:.3f} 秒")
            
            # 检查是否满足<2秒的要求
            if total_time < 2.0:
                print(f"   ✅ 启动时间符合要求 (<2秒)")
            else:
                print(f"   ⚠️  启动时间超出要求 (>2秒)")
            
            return True, total_time, module
        else:
            print("❌ 无法创建模块规范")
            return False, 0, None
            
    except Exception as e:
        print(f"❌ 插件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, None

def test_module_initialization(module):
    """测试模块初始化性能"""
    print("\n🔧 测试模块初始化性能...")
    
    try:
        # 测试延迟模块管理器
        lazy_manager = getattr(module, 'lazy_module_manager', None)
        if lazy_manager:
            start_time = time.time()
            
            # 初始化所有模块
            test_modules = ['control_flow', 'crypto', 'anti_debug', 'license', 'memory_patch', 'string_analysis']
            
            for mod_name in test_modules:
                lazy_manager.initialize_module(mod_name)
            
            init_time = time.time() - start_time
            print(f"   ✅ {len(test_modules)} 个模块初始化耗时: {init_time:.3f} 秒")
            
            # 测试模块数据获取
            start_time = time.time()
            for _ in range(100):
                lazy_manager.get_module_data('control_flow')
            data_time = time.time() - start_time
            print(f"   ✅ 100次数据获取耗时: {data_time:.3f} 秒")
        
        # 测试缓存系统
        cache = getattr(module, 'analysis_cache', None)
        if cache:
            start_time = time.time()
            
            # 填充缓存
            for i in range(50):
                cache.put(f"test_func_{i}", (i,), {}, {"result": f"data_{i}"})
            
            cache_time = time.time() - start_time
            print(f"   ✅ 50个缓存条目存储耗时: {cache_time:.3f} 秒")
            
            # 测试缓存检索
            start_time = time.time()
            hits = 0
            for i in range(100):
                hit, _ = cache.get(f"test_func_{i % 50}", (i % 50,), {})
                if hit:
                    hits += 1
            retrieval_time = time.time() - start_time
            print(f"   ✅ 100次缓存查询耗时: {retrieval_time:.3f} 秒 (命中率: {hits}%)")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块初始化测试失败: {e}")
        return False

def test_memory_usage():
    """测试内存使用情况"""
    print("\n💾 测试内存使用...")
    
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        
        # 获取当前内存使用
        memory_before = process.memory_info()
        
        print(f"   📊 当前内存使用: {memory_before.rss / 1024 / 1024:.2f} MB")
        
        # 强制垃圾回收
        gc.collect()
        
        memory_after = process.memory_info()
        print(f"   📊 垃圾回收后: {memory_after.rss / 1024 / 1024:.2f} MB")
        
        # 内存使用评估
        memory_mb = memory_after.rss / 1024 / 1024
        if memory_mb < 50:  # 假设基准内存占用
            print(f"   ✅ 内存使用合理 (<50MB)")
        else:
            print(f"   ⚠️  内存使用较高 (>{memory_mb:.1f}MB)")
        
        return True
        
    except ImportError:
        print("   ⚠️  psutil未安装，跳过内存测试")
        return True
    except Exception as e:
        print(f"   ❌ 内存测试失败: {e}")
        return False

def test_api_response_time(module):
    """测试API响应时间"""
    print("\n⚡测试API响应性能...")
    
    try:
        # 测试RPC注册表
        rpc_registry = getattr(module, 'rpc_registry', None)
        if rpc_registry and hasattr(rpc_registry, 'methods'):
            method_count = len(rpc_registry.methods)
            print(f"   📊 已注册API方法: {method_count} 个")
            
            # 模拟API调用（测试分发机制）
            if method_count > 0:
                start_time = time.time()
                
                # 测试方法查找性能
                for _ in range(1000):
                    method_name = list(rpc_registry.methods.keys())[0]
                    _ = rpc_registry.methods.get(method_name)
                
                lookup_time = time.time() - start_time
                print(f"   ✅ 1000次方法查找耗时: {lookup_time:.3f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ API性能测试失败: {e}")
        return False

def generate_performance_report(load_time: float, module) -> Dict[str, Any]:
    """生成性能报告"""
    
    report = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "load_time_seconds": round(load_time, 3),
        "startup_target_met": load_time < 2.0,
        "components": {}
    }
    
    # 检查组件状态
    try:
        if hasattr(module, 'lazy_module_manager'):
            lmm = module.lazy_module_manager
            report["components"]["lazy_module_manager"] = {
                "initialized_modules": sum(lmm.module_states.values()),
                "total_modules": len(lmm.module_states)
            }
        
        if hasattr(module, 'analysis_cache'):
            cache = module.analysis_cache
            stats = cache.get_stats()
            report["components"]["analysis_cache"] = {
                "max_size": cache.max_size,
                "current_entries": stats.get("total_entries", 0),
                "hit_rate": stats.get("hit_rate", "0%")
            }
        
        if hasattr(module, 'workflow_engine'):
            engine = module.workflow_engine
            report["components"]["workflow_engine"] = {
                "running_tasks": len(engine.running_tasks),
                "completed_tasks": len(engine.completed_tasks),
                "cached_strategies": len(engine.strategy_cache)
            }
        
        if hasattr(module, 'rpc_registry'):
            rpc = module.rpc_registry
            report["components"]["rpc_registry"] = {
                "registered_methods": len(rpc.methods),
                "unsafe_methods": len(rpc.unsafe)
            }
            
    except Exception as e:
        report["error"] = str(e)
    
    return report

def main():
    """主性能测试函数"""
    print("IDA Pro MCP 插件性能测试")
    print("=" * 50)
    
    # 测试插件加载
    success, load_time, module = test_plugin_load_time()
    
    if not success:
        print("❌ 性能测试失败：插件无法加载")
        return False
    
    # 运行各项性能测试
    init_success = test_module_initialization(module)
    memory_success = test_memory_usage()
    api_success = test_api_response_time(module)
    
    # 生成性能报告
    report = generate_performance_report(load_time, module)
    
    print("\n" + "=" * 50)
    print("📋 性能测试报告")
    print("=" * 50)
    
    print(f"📅 测试时间: {report['timestamp']}")
    print(f"⏱️  启动时间: {report['load_time_seconds']} 秒")
    print(f"🎯 启动目标: {'✅ 达成' if report['startup_target_met'] else '❌ 未达成'} (<2秒)")
    
    print("\n🔧 组件状态:")
    for component, details in report.get("components", {}).items():
        print(f"   📦 {component}:")
        for key, value in details.items():
            print(f"      - {key}: {value}")
    
    # 综合评估
    all_tests_passed = success and init_success and memory_success and api_success
    startup_ok = report['startup_target_met']
    
    print("\n🏆 综合评估:")
    if all_tests_passed and startup_ok:
        print("✅ 所有性能测试通过！")
        print("🚀 插件已优化完成，满足所有性能要求")
        print(f"   - 启动时间: {load_time:.3f}s < 2s ✅")
        print(f"   - 组件加载: 正常 ✅")
        print(f"   - 内存管理: 良好 ✅")
        print(f"   - API响应: 快速 ✅")
        
        return True
    else:
        print("⚠️  部分性能测试需要改进:")
        if not startup_ok:
            print("   - 启动时间超过2秒限制")
        if not all_tests_passed:
            print("   - 某些组件测试失败")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
