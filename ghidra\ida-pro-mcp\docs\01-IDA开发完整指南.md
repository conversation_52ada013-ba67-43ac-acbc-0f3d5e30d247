# IDA Pro 开发完整指南

## 📋 目录

1. [概述](#概述)
2. [环境准备](#环境准备)
3. [IDAPython 基础](#idapython-基础)
4. [插件开发](#插件开发)
5. [常用API参考](#常用api参考)
6. [代码示例](#代码示例)
7. [最佳实践](#最佳实践)
8. [调试技巧](#调试技巧)
9. [资源链接](#资源链接)

---

## 🎯 概述

IDA Pro 是世界领先的反汇编和逆向工程工具，支持多种扩展方式：

### 开发方式对比

| 方式 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **IDAPython** | 快速开发、无需编译、丰富API | 性能略低 | 脚本自动化、插件原型 |
| **C++ SDK** | 最高性能、完整访问 | 开发复杂、编译耗时 | 核心功能、性能关键 |
| **IDC** | 简单易学、内置支持 | 功能有限 | 简单脚本、快速任务 |

### IDAPython 核心优势

- **快速开发**: 无需编译，即写即用
- **丰富API**: 几乎覆盖所有IDA功能
- **Python生态**: 可使用所有Python库
- **跨平台**: Windows、Linux、macOS全支持
- **社区活跃**: 大量开源插件和示例

---

## 🛠️ 环境准备

### 1. IDA Pro 安装

确保安装了 IDA Pro 9.0+ 版本，IDAPython 已内置集成。

### 2. Python 环境

IDA Pro 内置 Python 3.x 环境，无需额外安装。

### 3. 开发工具推荐

- **IDE**: PyCharm、VSCode、Sublime Text
- **调试**: IDA Pro 内置 Python 控制台
- **版本控制**: Git
- **文档**: Hex-Rays 官方文档

### 4. 目录结构

```
IDA_Installation/
├── plugins/           # 插件目录
├── python/
│   ├── examples/      # 官方示例
│   └── 3/            # Python 3 模块
├── cfg/
│   └── plugins.cfg   # 插件配置
└── docs/             # 文档
```

---

## 🐍 IDAPython 基础

### 核心模块

#### 1. 必备模块

```python
import ida_idaapi      # 核心API
import ida_kernwin     # UI交互
import ida_funcs       # 函数操作
import ida_bytes       # 字节操作
import ida_name        # 名称管理
import ida_ua          # 指令分析
import ida_xref        # 交叉引用
import idautils        # 实用工具
import idc             # IDC兼容函数
```

#### 2. 常用变量和常量

```python
# 地址相关
ea = idc.here()                    # 当前地址
BADADDR = ida_idaapi.BADADDR       # 无效地址常量

# 地址范围
min_ea = idc.get_inf_attr(INF_MIN_EA)  # 最小地址
max_ea = idc.get_inf_attr(INF_MAX_EA)  # 最大地址

# 当前选择
start_ea = idc.read_selection_start()  # 选择开始
end_ea = idc.read_selection_end()      # 选择结束
```

### 基础操作

#### 1. 地址和导航

```python
# 获取当前地址
current_addr = idc.here()
current_addr = idc.get_screen_ea()

# 跳转到地址
idc.jumpto(0x401000)

# 获取函数地址
func_ea = ida_name.get_name_ea(0, "main")

# 获取地址名称
name = ida_name.get_name(0x401000)
```

#### 2. 数据读写

```python
# 读取数据
byte_val = idc.get_wide_byte(ea)      # 读取字节
word_val = idc.get_wide_word(ea)      # 读取字(2字节)
dword_val = idc.get_wide_dword(ea)    # 读取双字(4字节)
qword_val = idc.get_qword(ea)         # 读取四字(8字节)

# 写入数据
idc.patch_byte(ea, 0x90)              # 写入字节
idc.patch_word(ea, 0x9090)            # 写入字
idc.patch_dword(ea, 0x90909090)       # 写入双字

# 字符串操作
string_val = idc.get_strlit_contents(ea)  # 读取字符串
```

#### 3. 注释操作

```python
# 设置注释
idc.set_cmt(ea, "这是一个注释", 0)        # 普通注释
idc.set_cmt(ea, "这是重复注释", 1)        # 重复注释

# 获取注释
comment = idc.get_cmt(ea, 0)             # 获取普通注释
rep_comment = idc.get_cmt(ea, 1)         # 获取重复注释
```

---

## 🔌 插件开发

### 插件架构

IDA Pro 9.0+ 使用新的插件框架，基于 `plugin_t` 和 `plugmod_t` 类。

#### 1. 基本插件结构

```python
import ida_idaapi
import ida_kernwin
import idautils

class MyPlugmod(ida_idaapi.plugmod_t):
    """插件模块类 - 实现核心功能"""
    
    def __init__(self):
        super().__init__()
        print(">>> MyPlugmod: 初始化")
    
    def __del__(self):
        print(">>> MyPlugmod: 析构函数调用")
    
    def run(self, arg):
        """插件主要功能"""
        print(f">>> MyPlugmod.run() 参数: {arg}")
        
        # 示例：列出所有函数
        for func_ea in idautils.Functions():
            func_name = ida_funcs.get_func_name(func_ea)
            print(f"函数: {func_name} @ {hex(func_ea)}")
        
        return True

class MyPlugin(ida_idaapi.plugin_t):
    """插件主类 - 定义插件属性"""
    
    # 插件标志
    flags = ida_idaapi.PLUGIN_UNL | ida_idaapi.PLUGIN_MULTI
    
    # 插件信息
    comment = "我的第一个IDA插件"
    help = "这个插件列出当前数据库中的所有函数"
    wanted_name = "我的插件"
    wanted_hotkey = "Shift-P"
    
    def init(self):
        """插件初始化"""
        print(">>> MyPlugin: 初始化调用")
        return MyPlugmod()

def PLUGIN_ENTRY():
    """插件入口点"""
    return MyPlugin()
```

#### 2. 插件标志详解

```python
# 常用标志组合
flags = ida_idaapi.PLUGIN_UNL | ida_idaapi.PLUGIN_MULTI

# 标志说明
PLUGIN_UNL    # 运行后立即卸载
PLUGIN_MULTI  # 支持多个IDB同时运行（推荐）
PLUGIN_FIX    # IDA启动时加载，退出时卸载
PLUGIN_DRAW   # 每次屏幕刷新时调用
PLUGIN_MOD    # 插件会修改IDB数据库
PLUGIN_PROC   # 处理器模块扩展
PLUGIN_DBG    # 仅在调试器激活时加载
```

### 高级插件功能

#### 1. UI集成

```python
import ida_kernwin

class UIPlugin(ida_idaapi.plugmod_t):
    def run(self, arg):
        # 显示消息对话框
        ida_kernwin.info("插件执行成功！")
        
        # 显示警告
        ida_kernwin.warning("这是一个警告消息")
        
        # 获取用户输入
        user_input = ida_kernwin.ask_str("", 0, "请输入内容:")
        if user_input:
            print(f"用户输入: {user_input}")
        
        # 选择文件
        filename = ida_kernwin.ask_file(0, "*.txt", "选择文件")
        if filename:
            print(f"选择的文件: {filename}")
```

#### 2. 菜单和快捷键

```python
class MenuPlugin(ida_idaapi.plugmod_t):
    def __init__(self):
        super().__init__()
        self.menu_context = None
    
    def run(self, arg):
        # 添加菜单项
        action_desc = ida_kernwin.action_desc_t(
            'my_plugin:action',     # 动作ID
            '我的插件动作',          # 显示名称
            MyActionHandler(),      # 处理器
            'Ctrl+Shift+M',        # 快捷键
            '执行我的插件功能',      # 提示文本
            199                     # 图标ID
        )
        
        ida_kernwin.register_action(action_desc)
        ida_kernwin.attach_action_to_menu(
            'Edit/Plugins/',
            'my_plugin:action',
            ida_kernwin.SETMENU_APP
        )

class MyActionHandler(ida_kernwin.action_handler_t):
    def activate(self, ctx):
        print("菜单动作被激活")
        return 1
    
    def update(self, ctx):
        return ida_kernwin.AST_ENABLE_ALWAYS
```

---

## 📚 常用API参考

### 函数操作

```python
# 获取函数信息
func = ida_funcs.get_func(ea)
if func:
    func_name = ida_funcs.get_func_name(ea)
    func_start = func.start_ea
    func_end = func.end_ea
    func_size = func.end_ea - func.start_ea

# 创建函数
ida_funcs.add_func(start_ea, end_ea)

# 删除函数
ida_funcs.del_func(ea)

# 遍历所有函数
for func_ea in idautils.Functions():
    func_name = ida_funcs.get_func_name(func_ea)
    print(f"{func_name}: {hex(func_ea)}")
```

### 交叉引用

```python
# 获取引用到某地址的交叉引用
for xref in idautils.XrefsTo(ea):
    print(f"从 {hex(xref.frm)} 引用到 {hex(xref.to)}")

# 获取从某地址引用出去的交叉引用
for xref in idautils.XrefsFrom(ea):
    print(f"从 {hex(xref.frm)} 引用到 {hex(xref.to)}")
```

### 段操作

```python
# 遍历所有段
for seg_ea in idautils.Segments():
    seg = ida_segment.getseg(seg_ea)
    seg_name = ida_segment.get_segm_name(seg)
    print(f"段: {seg_name} [{hex(seg.start_ea)}-{hex(seg.end_ea)}]")

# 获取段信息
seg = ida_segment.getseg(ea)
if seg:
    seg_name = ida_segment.get_segm_name(seg)
    seg_start = seg.start_ea
    seg_end = seg.end_ea
```

---

## 💡 代码示例

### 示例1: 函数分析器

```python
def analyze_functions():
    """分析所有函数的基本信息"""
    results = []
    
    for func_ea in idautils.Functions():
        func = ida_funcs.get_func(func_ea)
        if not func:
            continue
            
        func_name = ida_funcs.get_func_name(func_ea)
        func_size = func.end_ea - func.start_ea
        
        # 统计指令数量
        insn_count = 0
        ea = func.start_ea
        while ea < func.end_ea:
            if ida_bytes.is_code(ida_bytes.get_flags(ea)):
                insn_count += 1
            ea = ida_bytes.next_head(ea, func.end_ea)
        
        # 统计交叉引用
        xrefs_to = len(list(idautils.XrefsTo(func_ea)))
        xrefs_from = len(list(idautils.XrefsFrom(func_ea)))
        
        results.append({
            'name': func_name,
            'address': hex(func_ea),
            'size': func_size,
            'instructions': insn_count,
            'xrefs_to': xrefs_to,
            'xrefs_from': xrefs_from
        })
    
    return results
```

### 示例2: 字符串提取器

```python
def extract_strings(min_length=4):
    """提取所有字符串"""
    strings = []
    
    for string_ea in idautils.Strings():
        string_val = str(string_ea)
        if len(string_val) >= min_length:
            strings.append({
                'address': hex(string_ea.ea),
                'value': string_val,
                'length': len(string_val),
                'type': string_ea.strtype
            })
    
    return strings
```

---

## 🎯 最佳实践

### 1. 错误处理

```python
def safe_operation(ea):
    try:
        # 检查地址有效性
        if ea == ida_idaapi.BADADDR:
            print("无效地址")
            return False
        
        # 执行操作
        result = some_ida_operation(ea)
        return result
        
    except Exception as e:
        print(f"操作失败: {e}")
        return False
```

### 2. 性能优化

```python
# 批量操作时禁用UI更新
ida_kernwin.show_wait_box("处理中...")
ida_auto.set_ida_state(ida_auto.st_Work)

try:
    # 执行批量操作
    for ea in address_list:
        process_address(ea)
finally:
    ida_auto.set_ida_state(ida_auto.st_Ready)
    ida_kernwin.hide_wait_box()
```

### 3. 内存管理

```python
# 及时释放大对象
large_data = process_large_dataset()
# 使用完毕后
del large_data

# 使用生成器处理大量数据
def process_functions():
    for func_ea in idautils.Functions():
        yield process_function(func_ea)
```

---

## 🐛 调试技巧

### 1. 日志记录

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ida_plugin.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def debug_function(ea):
    logger.debug(f"处理地址: {hex(ea)}")
    try:
        result = ida_operation(ea)
        logger.info(f"操作成功: {result}")
        return result
    except Exception as e:
        logger.error(f"操作失败: {e}")
        raise
```

### 2. 断点调试

```python
# 在关键位置添加断点
import pdb

def complex_operation():
    # 设置断点
    pdb.set_trace()
    
    # 继续执行
    result = some_operation()
    return result
```

---

## 🔗 资源链接

### 官方文档
- [IDAPython API参考](https://python.docs.hex-rays.com/)
- [开发者指南](https://docs.hex-rays.com/developer-guide/idapython)
- [示例库](https://docs.hex-rays.com/developer-guide/idapython/idapython-examples)
- [插件创建指南](https://docs.hex-rays.com/developer-guide/idapython/how-to-create-a-plugin)

### 社区资源
- [Hex-Rays论坛](https://community.hex-rays.com/)
- [插件仓库](https://plugins.hex-rays.com/)
- [GitHub示例](https://github.com/HexRaysSA/IDAPython)

### 学习资源
- [IDAPython入门教程](https://docs.hex-rays.com/developer-guide/idapython/idapython-getting-started)
- [移植指南](https://docs.hex-rays.com/developer-guide/idapython/idapython-porting-guide-ida-9)
- [发布说明](https://docs.hex-rays.com/release-notes/9_1)

---

## 🔧 高级开发技巧

### 1. 反编译器集成

```python
import ida_hexrays

def decompile_function(func_ea):
    """反编译指定函数"""
    try:
        # 获取反编译结果
        cfunc = ida_hexrays.decompile(func_ea)
        if cfunc:
            # 获取伪代码
            pseudocode = str(cfunc)
            print(f"函数 {hex(func_ea)} 的伪代码:")
            print(pseudocode)
            return pseudocode
        else:
            print(f"无法反编译函数 {hex(func_ea)}")
            return None
    except Exception as e:
        print(f"反编译失败: {e}")
        return None

def analyze_ctree(func_ea):
    """分析C语法树"""
    cfunc = ida_hexrays.decompile(func_ea)
    if cfunc:
        # 遍历语法树
        class CTreeVisitor(ida_hexrays.ctree_visitor_t):
            def __init__(self):
                ida_hexrays.ctree_visitor_t.__init__(self, ida_hexrays.CV_FAST)
                self.calls = []

            def visit_expr(self, expr):
                if expr.op == ida_hexrays.cot_call:
                    self.calls.append(expr.x.obj_ea)
                return 0

        visitor = CTreeVisitor()
        visitor.apply_to(cfunc.body, None)
        return visitor.calls
```

### 2. 调试器集成

```python
import ida_dbg

def debug_operations():
    """调试器相关操作"""
    # 检查调试器状态
    if ida_dbg.is_debugger_on():
        print("调试器已启动")

        # 获取寄存器值
        regs = ida_dbg.get_reg_vals()
        for reg_name, reg_val in regs.items():
            print(f"{reg_name}: {hex(reg_val)}")

        # 设置断点
        ida_dbg.add_bpt(0x401000)

        # 继续执行
        ida_dbg.continue_process()
    else:
        print("调试器未启动")

def memory_operations():
    """内存操作"""
    if ida_dbg.is_debugger_on():
        # 读取内存
        data = ida_dbg.read_dbg_memory(0x401000, 16)
        print(f"内存数据: {data.hex()}")

        # 写入内存
        new_data = b'\x90' * 4  # NOP指令
        ida_dbg.write_dbg_memory(0x401000, new_data)
```

### 3. 类型系统操作

```python
import ida_typeinf

def type_operations():
    """类型系统操作"""
    # 创建结构体
    struct_name = "MyStruct"
    sid = ida_struct.add_struc(ida_idaapi.BADADDR, struct_name)

    if sid != ida_idaapi.BADADDR:
        # 添加成员
        ida_struct.add_struc_member(sid, "field1", 0, ida_bytes.FF_DWORD, None, 4)
        ida_struct.add_struc_member(sid, "field2", 4, ida_bytes.FF_QWORD, None, 8)

        print(f"创建结构体 {struct_name} 成功")

    # 应用类型到地址
    tinfo = ida_typeinf.tinfo_t()
    if tinfo.get_named_type(None, struct_name):
        ida_typeinf.apply_tinfo(0x401000, tinfo, ida_typeinf.TINFO_DEFINITE)

def enum_operations():
    """枚举操作"""
    # 创建枚举
    enum_name = "MyEnum"
    eid = ida_enum.add_enum(ida_idaapi.BADADDR, enum_name, ida_bytes.hex_flag())

    if eid != ida_idaapi.BADADDR:
        # 添加枚举成员
        ida_enum.add_enum_member(eid, "VALUE1", 0)
        ida_enum.add_enum_member(eid, "VALUE2", 1)
        ida_enum.add_enum_member(eid, "VALUE3", 2)

        print(f"创建枚举 {enum_name} 成功")
```

### 4. 自动化分析

```python
def auto_analysis():
    """自动化分析流程"""
    print("开始自动化分析...")

    # 等待自动分析完成
    ida_auto.auto_wait()

    # 创建所有函数
    for ea in idautils.Heads():
        if ida_bytes.is_code(ida_bytes.get_flags(ea)):
            if not ida_funcs.get_func(ea):
                ida_funcs.add_func(ea)

    # 分析字符串
    ida_auto.auto_make_proc(ida_auto.AAUTO_STRINGS)

    # 等待分析完成
    ida_auto.auto_wait()

    print("自动化分析完成")

def batch_rename():
    """批量重命名"""
    # 重命名所有sub_开头的函数
    for func_ea in idautils.Functions():
        func_name = ida_funcs.get_func_name(func_ea)
        if func_name.startswith("sub_"):
            # 分析函数特征
            new_name = analyze_function_purpose(func_ea)
            if new_name:
                ida_name.set_name(func_ea, new_name)
                print(f"重命名: {func_name} -> {new_name}")

def analyze_function_purpose(func_ea):
    """分析函数用途"""
    func = ida_funcs.get_func(func_ea)
    if not func:
        return None

    # 检查字符串引用
    strings = []
    for ea in range(func.start_ea, func.end_ea):
        for xref in idautils.XrefsFrom(ea):
            if ida_bytes.is_strlit(ida_bytes.get_flags(xref.to)):
                string_val = ida_bytes.get_strlit_contents(xref.to)
                if string_val:
                    strings.append(string_val.decode('utf-8', errors='ignore'))

    # 根据字符串推断函数用途
    if any('error' in s.lower() for s in strings):
        return f"error_handler_{func_ea:x}"
    elif any('init' in s.lower() for s in strings):
        return f"initialize_{func_ea:x}"
    elif any('debug' in s.lower() for s in strings):
        return f"debug_func_{func_ea:x}"

    return None
```

### 5. 插件配置管理

```python
import json
import os

class PluginConfig:
    """插件配置管理"""

    def __init__(self, plugin_name):
        self.plugin_name = plugin_name
        self.config_file = os.path.join(
            ida_diskio.get_user_idadir(),
            f"{plugin_name}_config.json"
        )
        self.config = self.load_config()

    def load_config(self):
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置失败: {e}")

        # 返回默认配置
        return {
            'auto_analysis': True,
            'max_functions': 1000,
            'output_format': 'json'
        }

    def save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"保存配置失败: {e}")

    def get(self, key, default=None):
        """获取配置项"""
        return self.config.get(key, default)

    def set(self, key, value):
        """设置配置项"""
        self.config[key] = value
        self.save_config()
```

### 6. 数据导出

```python
def export_analysis_results():
    """导出分析结果"""
    results = {
        'metadata': {
            'filename': ida_nalt.get_root_filename(),
            'analysis_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'ida_version': ida_kernwin.get_kernel_version()
        },
        'functions': [],
        'strings': [],
        'imports': [],
        'exports': []
    }

    # 导出函数信息
    for func_ea in idautils.Functions():
        func_info = {
            'address': hex(func_ea),
            'name': ida_funcs.get_func_name(func_ea),
            'size': ida_funcs.get_func(func_ea).size(),
            'type': 'function'
        }
        results['functions'].append(func_info)

    # 导出字符串
    for string_ea in idautils.Strings():
        string_info = {
            'address': hex(string_ea.ea),
            'value': str(string_ea),
            'length': string_ea.length,
            'type': 'string'
        }
        results['strings'].append(string_info)

    # 导出导入表
    for i in range(ida_nalt.get_import_module_qty()):
        module_name = ida_nalt.get_import_module_name(i)
        for j in range(ida_nalt.get_import_module_size(i)):
            import_name = ida_nalt.get_import_name(i, j)
            if import_name:
                import_info = {
                    'module': module_name,
                    'name': import_name,
                    'ordinal': j,
                    'type': 'import'
                }
                results['imports'].append(import_info)

    # 保存结果
    output_file = ida_kernwin.ask_file(1, "*.json", "保存分析结果")
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        print(f"分析结果已保存到: {output_file}")
```

---

## 🚀 实战项目示例

### 项目1: 恶意软件分析器

```python
class MalwareAnalyzer(ida_idaapi.plugmod_t):
    """恶意软件分析插件"""

    def __init__(self):
        super().__init__()
        self.suspicious_apis = [
            'CreateFile', 'WriteFile', 'RegSetValue',
            'CreateProcess', 'VirtualAlloc', 'LoadLibrary'
        ]
        self.crypto_constants = [
            0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476  # MD5
        ]

    def run(self, arg):
        """执行恶意软件分析"""
        print("开始恶意软件分析...")

        # 分析可疑API调用
        suspicious_calls = self.find_suspicious_apis()

        # 检测加密常量
        crypto_refs = self.find_crypto_constants()

        # 分析网络相关功能
        network_funcs = self.find_network_functions()

        # 生成报告
        self.generate_report(suspicious_calls, crypto_refs, network_funcs)

    def find_suspicious_apis(self):
        """查找可疑API调用"""
        results = []

        for func_ea in idautils.Functions():
            for ea in idautils.FuncItems(func_ea):
                for xref in idautils.XrefsFrom(ea):
                    name = ida_name.get_name(xref.to)
                    if any(api in name for api in self.suspicious_apis):
                        results.append({
                            'caller': hex(ea),
                            'api': name,
                            'function': ida_funcs.get_func_name(func_ea)
                        })

        return results

    def find_crypto_constants(self):
        """查找加密常量"""
        results = []

        for const in self.crypto_constants:
            ea = ida_search.find_binary(0, ida_idaapi.BADADDR,
                                      f"{const:08X}", 16, ida_search.SEARCH_DOWN)
            while ea != ida_idaapi.BADADDR:
                results.append({
                    'address': hex(ea),
                    'constant': hex(const),
                    'type': 'crypto_constant'
                })
                ea = ida_search.find_binary(ea + 4, ida_idaapi.BADADDR,
                                          f"{const:08X}", 16, ida_search.SEARCH_DOWN)

        return results
```

### 项目2: 代码覆盖率分析

```python
class CoverageAnalyzer(ida_idaapi.plugmod_t):
    """代码覆盖率分析插件"""

    def __init__(self):
        super().__init__()
        self.coverage_data = {}
        self.trace_file = None

    def run(self, arg):
        """运行覆盖率分析"""
        # 选择trace文件
        self.trace_file = ida_kernwin.ask_file(0, "*.trace", "选择trace文件")
        if not self.trace_file:
            return

        # 解析trace数据
        self.parse_trace_file()

        # 可视化覆盖率
        self.visualize_coverage()

        # 生成报告
        self.generate_coverage_report()

    def parse_trace_file(self):
        """解析trace文件"""
        try:
            with open(self.trace_file, 'r') as f:
                for line in f:
                    addr = int(line.strip(), 16)
                    self.coverage_data[addr] = self.coverage_data.get(addr, 0) + 1
        except Exception as e:
            print(f"解析trace文件失败: {e}")

    def visualize_coverage(self):
        """可视化覆盖率"""
        for addr, count in self.coverage_data.items():
            # 根据执行次数设置颜色
            if count > 100:
                color = 0x0000FF  # 红色 - 热点
            elif count > 10:
                color = 0x00FFFF  # 黄色 - 中等
            else:
                color = 0x00FF00  # 绿色 - 低频

            ida_kernwin.set_item_color(addr, color)

    def generate_coverage_report(self):
        """生成覆盖率报告"""
        total_instructions = 0
        covered_instructions = len(self.coverage_data)

        # 统计总指令数
        for func_ea in idautils.Functions():
            func = ida_funcs.get_func(func_ea)
            for ea in range(func.start_ea, func.end_ea):
                if ida_bytes.is_code(ida_bytes.get_flags(ea)):
                    total_instructions += 1

        coverage_rate = (covered_instructions / total_instructions) * 100

        report = f"""
代码覆盖率报告
================
总指令数: {total_instructions}
覆盖指令数: {covered_instructions}
覆盖率: {coverage_rate:.2f}%

热点函数 (执行次数 > 50):
"""

        # 统计函数级覆盖率
        func_coverage = {}
        for addr, count in self.coverage_data.items():
            func = ida_funcs.get_func(addr)
            if func:
                func_name = ida_funcs.get_func_name(func.start_ea)
                if func_name not in func_coverage:
                    func_coverage[func_name] = []
                func_coverage[func_name].append(count)

        for func_name, counts in func_coverage.items():
            avg_count = sum(counts) / len(counts)
            if avg_count > 50:
                report += f"\n{func_name}: 平均执行 {avg_count:.1f} 次"

        print(report)
```

---

**文档版本**: 1.0
**适用版本**: IDA Pro 9.0+
**最后更新**: 2025-08-04
**作者**: IDA开发团队
