# IDA Pro MCP服务器重启指南

## 🚨 重要说明
当对`mcp-plugin.py`文件进行修改后，必须重启MCP服务器才能使新功能生效。

## 📋 重启步骤

### 1. 停止当前MCP服务器
在IDA Pro中：
- 按 `Ctrl+Alt+M` 或
- 菜单：`Edit -> Plugins -> MCP`
- 如果服务器正在运行，会显示停止选项

### 2. 重新启动MCP服务器
- 再次按 `Ctrl+Alt+M` 或
- 菜单：`Edit -> Plugins -> MCP`
- 选择启动服务器

### 3. 验证连接
使用以下命令验证连接：
```
check_connection_ida-pro-mcp()
```

## 🔧 新增功能列表

### 动态分析框架（20个新函数）
- `start_dynamic_analysis()` - 启动动态分析会话
- `set_breakpoint()` - 设置断点
- `step_execution()` - 单步执行
- `manage_breakpoints()` - 管理断点
- `dump_process_memory()` - 转储内存
- `monitor_api_calls()` - 监控API调用
- `trace_memory_access()` - 追踪内存访问
- `get_dynamic_analysis_summary()` - 获取分析摘要

### 工作流自动化引擎
- `list_workflow_templates()` - 列出工作流模板
- `get_workflow_template()` - 获取模板详情
- `execute_workflow()` - 执行工作流
- `get_workflow_execution_status()` - 获取执行状态
- `recommend_tools()` - 智能工具推荐
- `create_custom_workflow()` - 创建自定义工作流

### 工具增强
- `execute_crack_strategy()` - 执行破解策略
- `analyze_protection_effectiveness()` - 分析保护有效性

### 性能优化
- `optimize_large_file_processing()` - 大文件处理优化
- `enable_parallel_processing()` - 启用并行处理
- `optimize_memory_management()` - 内存管理优化
- `get_performance_statistics()` - 性能统计

## ⚠️ 注意事项

1. **代码修改后必须重启**：任何对Python代码的修改都需要重启MCP服务器
2. **连接验证**：重启后务必验证连接状态
3. **功能测试**：重启后测试新功能是否正常工作
4. **错误处理**：如果重启失败，检查IDA Pro控制台的错误信息

## 🧪 测试新功能

重启后可以测试以下功能：

```python
# 测试动态分析
start_dynamic_analysis()

# 测试工作流
list_workflow_templates()

# 测试性能优化
get_performance_statistics()
```

## 🚨 重要发现：Python模块缓存问题

### 问题诊断
经过测试发现，即使重启MCP插件，新增的函数仍然无法注册。根本原因是：
- **Python模块缓存机制**：IDA Pro的Python环境缓存了旧版本的模块
- **简单重启无效**：仅重启MCP插件无法清除Python模块缓存

### 🔧 完整解决方案

#### 方案1：完全重启IDA Pro（推荐）
1. **保存当前工作**：保存IDA数据库
2. **完全关闭IDA Pro**：关闭整个程序
3. **清除缓存**：删除Python缓存文件（已自动完成）
4. **重新启动IDA Pro**：重新打开程序和文件
5. **启动MCP服务器**：按 `Ctrl+Alt+M`

#### 方案2：强制重载Python模块
在IDA Pro的Python控制台中执行：
```python
import sys
import importlib
if 'ida_pro_mcp.mcp-plugin' in sys.modules:
    importlib.reload(sys.modules['ida_pro_mcp.mcp-plugin'])
```

### 🧪 验证步骤
重启后测试以下新增功能：
```python
# 测试简单函数
test_new_function_ida-pro-mcp()

# 测试动态分析
start_dynamic_analysis_ida-pro-mcp()

# 测试工作流
list_workflow_templates_ida-pro-mcp()
```

## 📊 修复状态

- ✅ 参数类型错误已修复
- ✅ 新功能代码已实现
- ✅ Python缓存已清除
- ⚠️ 需要完全重启IDA Pro
- ⏳ 功能注册验证待完成
