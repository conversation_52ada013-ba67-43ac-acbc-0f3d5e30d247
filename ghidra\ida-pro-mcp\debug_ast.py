#!/usr/bin/env python3
"""
调试AST解析问题的脚本
"""
import ast
import sys
from pathlib import Path

def debug_ast_parsing():
    """调试AST解析问题"""
    plugin_file = Path("src/ida_pro_mcp/mcp-plugin.py")
    
    if not plugin_file.exists():
        print(f"文件不存在: {plugin_file}")
        return
    
    print(f"正在解析文件: {plugin_file}")
    
    try:
        with open(plugin_file, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # 解析AST
        module = ast.parse(source)
        
        # 查找所有带@jsonrpc装饰器的函数
        class JSONRPCVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # 检查是否有@jsonrpc装饰器
                has_jsonrpc = False
                for decorator in node.decorator_list:
                    if isinstance(decorator, ast.Name) and decorator.id == "jsonrpc":
                        has_jsonrpc = True
                        break
                
                if has_jsonrpc:
                    print(f"\n函数: {node.name}")
                    print(f"参数数量: {len(node.args.args)}")
                    
                    for i, arg in enumerate(node.args.args):
                        arg_name = arg.arg
                        arg_type = arg.annotation
                        print(f"  参数 {i}: {arg_name}")
                        
                        if arg_type is None:
                            print(f"    错误: 缺少类型注解")
                        elif isinstance(arg_type, ast.Subscript):
                            if isinstance(arg_type.value, ast.Name):
                                print(f"    类型: {arg_type.value.id}")
                                if arg_type.value.id != "Annotated":
                                    print(f"    错误: 期望Annotated，实际是{arg_type.value.id}")
                            else:
                                print(f"    错误: 复杂的订阅类型: {ast.dump(arg_type.value)}")
                        else:
                            print(f"    错误: 非Annotated类型: {ast.dump(arg_type)}")
                
                self.generic_visit(node)
        
        visitor = JSONRPCVisitor()
        visitor.visit(module)
        
    except Exception as e:
        print(f"解析错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_ast_parsing()
