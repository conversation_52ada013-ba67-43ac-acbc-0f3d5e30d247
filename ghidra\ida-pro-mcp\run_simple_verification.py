#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版验证测试执行器
避免Unicode编码问题
"""

import sys
import os
import time
import subprocess
import json
from datetime import datetime

def run_syntax_checks():
    """运行语法检查"""
    print("=== 语法检查 ===")
    
    files_to_check = [
        "src/ida_pro_mcp/mcp-plugin.py",
        "src/ida_pro_mcp/server_generated.py"
    ]
    
    results = []
    
    for file_path in files_to_check:
        print(f"检查文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"  [SKIP] 文件不存在")
            results.append({"file": file_path, "status": "skip", "reason": "file not found"})
            continue
        
        try:
            result = subprocess.run(
                [sys.executable, "-m", "py_compile", file_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                print(f"  [PASS] 语法检查通过")
                results.append({"file": file_path, "status": "pass"})
            else:
                print(f"  [FAIL] 语法错误: {result.stderr}")
                results.append({"file": file_path, "status": "fail", "error": result.stderr})
                
        except Exception as e:
            print(f"  [ERROR] 检查异常: {str(e)}")
            results.append({"file": file_path, "status": "error", "error": str(e)})
    
    return results

def check_fix_completeness():
    """检查修复完整性"""
    print("\n=== 修复完整性检查 ===")
    
    # 检查关键修复点
    checks = [
        {
            "name": "参数类型修复",
            "file": "src/ida_pro_mcp/server_generated.py",
            "pattern": "json.loads",
            "description": "检查JSON参数处理"
        },
        {
            "name": "指令常量修复", 
            "file": "src/ida_pro_mcp/mcp-plugin.py",
            "pattern": "ida_allins.NN_",
            "description": "检查ida_allins模块使用"
        },
        {
            "name": "上下文检测修复",
            "file": "src/ida_pro_mcp/mcp-plugin.py", 
            "pattern": "check_local_variable_exists",
            "description": "检查上下文检测函数"
        },
        {
            "name": "输出验证修复",
            "file": "src/ida_pro_mcp/mcp-plugin.py",
            "pattern": "disassembly_function.update(return_type=",
            "description": "检查返回类型处理"
        }
    ]
    
    results = []
    
    for check in checks:
        print(f"检查: {check['name']}")
        
        if not os.path.exists(check["file"]):
            print(f"  [SKIP] 文件不存在: {check['file']}")
            results.append({
                "check": check["name"],
                "status": "skip",
                "reason": "file not found"
            })
            continue
        
        try:
            with open(check["file"], "r", encoding="utf-8") as f:
                content = f.read()
            
            if check["pattern"] in content:
                print(f"  [PASS] 找到修复标记: {check['pattern']}")
                results.append({
                    "check": check["name"],
                    "status": "pass",
                    "pattern": check["pattern"]
                })
            else:
                print(f"  [FAIL] 未找到修复标记: {check['pattern']}")
                results.append({
                    "check": check["name"],
                    "status": "fail",
                    "pattern": check["pattern"]
                })
                
        except Exception as e:
            print(f"  [ERROR] 检查异常: {str(e)}")
            results.append({
                "check": check["name"],
                "status": "error",
                "error": str(e)
            })
    
    return results

def check_documentation():
    """检查文档完整性"""
    print("\n=== 文档完整性检查 ===")
    
    required_docs = [
        "comprehensive_fix_documentation.md",
        "code_modification_recommendations.md", 
        "verification_test_plan.md",
        "parameter_fix_validation_report.md",
        "instruction_constants_fix_report.md",
        "disassemble_function_fix_report.md",
        "local_variable_context_fix_report.md"
    ]
    
    results = []
    
    for doc_file in required_docs:
        print(f"检查文档: {doc_file}")
        
        if os.path.exists(doc_file):
            try:
                with open(doc_file, "r", encoding="utf-8") as f:
                    content = f.read()
                
                if len(content) > 100:  # 基本内容检查
                    print(f"  [PASS] 文档存在且有内容 ({len(content)} 字符)")
                    results.append({
                        "document": doc_file,
                        "status": "pass",
                        "size": len(content)
                    })
                else:
                    print(f"  [WARN] 文档内容过少 ({len(content)} 字符)")
                    results.append({
                        "document": doc_file,
                        "status": "warn",
                        "size": len(content)
                    })
                    
            except Exception as e:
                print(f"  [ERROR] 读取文档失败: {str(e)}")
                results.append({
                    "document": doc_file,
                    "status": "error",
                    "error": str(e)
                })
        else:
            print(f"  [FAIL] 文档不存在")
            results.append({
                "document": doc_file,
                "status": "fail",
                "reason": "file not found"
            })
    
    return results

def generate_verification_summary(syntax_results, fix_results, doc_results):
    """生成验证摘要"""
    print("\n=== 验证摘要 ===")
    
    # 统计结果
    syntax_pass = sum(1 for r in syntax_results if r["status"] == "pass")
    syntax_total = len(syntax_results)
    
    fix_pass = sum(1 for r in fix_results if r["status"] == "pass")
    fix_total = len(fix_results)
    
    doc_pass = sum(1 for r in doc_results if r["status"] == "pass")
    doc_total = len(doc_results)
    
    total_pass = syntax_pass + fix_pass + doc_pass
    total_tests = syntax_total + fix_total + doc_total
    
    success_rate = (total_pass / total_tests * 100) if total_tests > 0 else 0
    
    print(f"语法检查: {syntax_pass}/{syntax_total} 通过")
    print(f"修复检查: {fix_pass}/{fix_total} 通过")
    print(f"文档检查: {doc_pass}/{doc_total} 通过")
    print(f"总体成功率: {success_rate:.1f}% ({total_pass}/{total_tests})")
    
    # 判断整体状态
    if success_rate >= 90:
        status = "EXCELLENT"
        recommendation = "修复质量优秀，可以部署"
    elif success_rate >= 80:
        status = "GOOD"
        recommendation = "修复质量良好，建议部署"
    elif success_rate >= 70:
        status = "ACCEPTABLE"
        recommendation = "修复基本完成，可以部署但需要监控"
    else:
        status = "NEEDS_IMPROVEMENT"
        recommendation = "修复不完整，需要进一步改进"
    
    print(f"整体状态: {status}")
    print(f"部署建议: {recommendation}")
    
    # 生成报告
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "syntax_check": {"pass": syntax_pass, "total": syntax_total},
            "fix_check": {"pass": fix_pass, "total": fix_total},
            "doc_check": {"pass": doc_pass, "total": doc_total},
            "overall": {
                "pass": total_pass,
                "total": total_tests,
                "success_rate": success_rate,
                "status": status,
                "recommendation": recommendation
            }
        },
        "details": {
            "syntax_results": syntax_results,
            "fix_results": fix_results,
            "doc_results": doc_results
        }
    }
    
    # 保存报告
    report_file = f"verification_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n详细报告已保存到: {report_file}")
    
    return report

def main():
    """主函数"""
    print("IDA Pro MCP 修复验证检查器")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        # 运行各项检查
        syntax_results = run_syntax_checks()
        fix_results = check_fix_completeness()
        doc_results = check_documentation()
        
        # 生成摘要
        report = generate_verification_summary(syntax_results, fix_results, doc_results)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n检查完成，耗时: {duration:.2f}秒")
        
        # 根据结果决定退出码
        if report["summary"]["overall"]["success_rate"] >= 80:
            print("验证通过！")
            return 0
        else:
            print("验证未完全通过，请检查报告。")
            return 1
            
    except KeyboardInterrupt:
        print("\n检查被用户中断")
        return 2
    except Exception as e:
        print(f"\n检查执行异常: {str(e)}")
        return 3

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
