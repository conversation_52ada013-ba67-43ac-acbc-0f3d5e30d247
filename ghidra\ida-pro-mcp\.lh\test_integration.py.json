{"sourceFile": "test_integration.py", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1754225281573, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754225368557, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,11 +38,20 @@\n     print(\"=\" * 50)\r\n     print(\"测试延迟模块管理器\")\r\n     print(\"=\" * 50)\r\n     \r\n-    # 导入延迟模块管理器相关代码\r\n-    from src.ida_pro_mcp.mcp_plugin import lazy_module_manager\r\n+    # 添加路径并导入\r\n+    import sys\r\n+    sys.path.insert(0, 'src')\r\n     \r\n+    # 直接加载模块文件\r\n+    import importlib.util\r\n+    spec = importlib.util.spec_from_file_location(\"mcp_plugin\", \"src/ida_pro_mcp/mcp-plugin.py\")\r\n+    mcp_module = importlib.util.module_from_spec(spec)\r\n+    spec.loader.exec_module(mcp_module)\r\n+    \r\n+    lazy_module_manager = mcp_module.lazy_module_manager\r\n+    \r\n     # 测试模块初始化\r\n     test_modules = ['control_flow', 'crypto', 'anti_debug', 'license']\r\n     \r\n     for module_name in test_modules:\r\n"}], "date": 1754225281573, "name": "Commit-0", "content": "#!/usr/bin/env python3\r\n\"\"\"\r\nIDA Pro MCP插件集成测试脚本\r\n测试延迟初始化、缓存系统和核心功能组件\r\n\"\"\"\r\n\r\nimport sys\r\nimport json\r\nimport time\r\nfrom typing import Any, Dict\r\n\r\n# 模拟IDA环境\r\ndef mock_ida_environment():\r\n    \"\"\"模拟IDA API环境用于测试\"\"\"\r\n    # 创建模拟的IDA模块\r\n    modules_to_mock = [\r\n        'idaapi', 'ida_kernwin', 'ida_hexrays', 'idautils', 'idc',\r\n        'ida_funcs', 'ida_gdl', 'ida_lines', 'ida_idaapi', 'ida_nalt',\r\n        'ida_bytes', 'ida_typeinf', 'ida_xref', 'ida_entry', 'ida_idd',\r\n        'ida_dbg', 'ida_name', 'ida_ida', 'ida_frame'\r\n    ]\r\n    \r\n    for module_name in modules_to_mock:\r\n        if module_name not in sys.modules:\r\n            mock_module = type(sys)(module_name)\r\n            # 添加常用常量和函数\r\n            setattr(mock_module, 'BADADDR', 0xFFFFFFFFFFFFFFFF)\r\n            setattr(mock_module, 'get_imagebase', lambda: 0x10000000)\r\n            setattr(mock_module, 'get_func', lambda x: None)\r\n            setattr(mock_module, 'init_hexrays_plugin', lambda: True)\r\n            setattr(mock_module, 'MFF_READ', 1)\r\n            setattr(mock_module, 'MFF_WRITE', 2)\r\n            setattr(mock_module, 'MFF_FAST', 0)\r\n            sys.modules[module_name] = mock_module\r\n\r\ndef test_lazy_module_manager():\r\n    \"\"\"测试延迟模块管理器\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"测试延迟模块管理器\")\r\n    print(\"=\" * 50)\r\n    \r\n    # 导入延迟模块管理器相关代码\r\n    from src.ida_pro_mcp.mcp_plugin import lazy_module_manager\r\n    \r\n    # 测试模块初始化\r\n    test_modules = ['control_flow', 'crypto', 'anti_debug', 'license']\r\n    \r\n    for module_name in test_modules:\r\n        # 测试初始化前状态\r\n        assert not lazy_module_manager.is_initialized(module_name), f\"{module_name} 不应该已初始化\"\r\n        \r\n        # 初始化模块\r\n        lazy_module_manager.initialize_module(module_name)\r\n        \r\n        # 测试初始化后状态\r\n        assert lazy_module_manager.is_initialized(module_name), f\"{module_name} 应该已初始化\"\r\n        \r\n        # 获取模块数据\r\n        data = lazy_module_manager.get_module_data(module_name)\r\n        assert isinstance(data, dict), f\"{module_name} 数据应该是字典类型\"\r\n        \r\n        print(f\"✓ {module_name} 模块初始化成功，数据keys: {list(data.keys())[:3]}...\")\r\n    \r\n    # 测试使用统计\r\n    stats = lazy_module_manager.get_usage_stats()\r\n    print(f\"✓ 模块使用统计: {stats}\")\r\n    \r\n    print(\"延迟模块管理器测试通过！\\n\")\r\n\r\ndef test_analysis_cache():\r\n    \"\"\"测试智能缓存系统\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"测试智能缓存系统\")\r\n    print(\"=\" * 50)\r\n    \r\n    from src.ida_pro_mcp.mcp_plugin import analysis_cache\r\n    \r\n    # 测试缓存存储和检索\r\n    test_data = {\"result\": \"test_analysis_result\", \"timestamp\": time.time()}\r\n    \r\n    # 存储数据\r\n    analysis_cache.put(\"test_function\", (\"arg1\", \"arg2\"), {\"param\": \"value\"}, test_data)\r\n    \r\n    # 检索数据\r\n    hit, cached_data = analysis_cache.get(\"test_function\", (\"arg1\", \"arg2\"), {\"param\": \"value\"})\r\n    assert hit, \"缓存命中应该成功\"\r\n    assert cached_data == test_data, \"缓存数据应该匹配\"\r\n    \r\n    # 测试缓存未命中\r\n    hit, _ = analysis_cache.get(\"non_existent\", (), {})\r\n    assert not hit, \"不存在的缓存应该未命中\"\r\n    \r\n    # 测试缓存统计\r\n    stats = analysis_cache.get_stats()\r\n    assert 'hit_rate' in stats, \"统计信息应该包含命中率\"\r\n    \r\n    print(f\"✓ 缓存系统测试通过，命中率: {stats['hit_rate']}\")\r\n    print(f\"✓ 内存使用: {stats['memory_usage_mb']} MB\")\r\n    print(f\"✓ 条目数量: {stats['total_entries']}\")\r\n    \r\n    print(\"智能缓存系统测试通过！\\n\")\r\n\r\ndef test_rpc_registry():\r\n    \"\"\"测试RPC注册表功能\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"测试RPC注册表\")\r\n    print(\"=\" * 50)\r\n    \r\n    from src.ida_pro_mcp.mcp_plugin import rpc_registry\r\n    \r\n    # 测试方法注册\r\n    @rpc_registry.register\r\n    def test_method(param1: str, param2: int) -> str:\r\n        return f\"Processed: {param1} - {param2}\"\r\n    \r\n    # 测试调度\r\n    result = rpc_registry.dispatch(\"test_method\", {\"param1\": \"hello\", \"param2\": 42})\r\n    expected = \"Processed: hello - 42\"\r\n    assert result == expected, f\"期望 {expected}, 获得 {result}\"\r\n    \r\n    print(f\"✓ RPC方法注册和调度成功: {result}\")\r\n    \r\n    # 测试错误处理\r\n    try:\r\n        rpc_registry.dispatch(\"non_existent_method\", {})\r\n        assert False, \"应该抛出异常\"\r\n    except Exception as e:\r\n        print(f\"✓ 错误处理正常: {type(e).__name__}\")\r\n    \r\n    print(\"RPC注册表测试通过！\\n\")\r\n\r\ndef test_workflow_engine():\r\n    \"\"\"测试工作流引擎\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"测试工作流引擎\")\r\n    print(\"=\" * 50)\r\n    \r\n    from src.ida_pro_mcp.mcp_plugin import workflow_engine\r\n    \r\n    # 测试保护特征分析\r\n    features = workflow_engine._analyze_protection_patterns()\r\n    print(f\"✓ 保护特征分析: 检测到 {len(features)} 个特征\")\r\n    \r\n    # 测试策略置信度计算\r\n    confidence = workflow_engine._calculate_strategy_confidence(['anti_debug', 'timing_check'])\r\n    assert 0.0 <= confidence <= 1.0, \"置信度应该在0-1之间\"\r\n    print(f\"✓ 策略置信度计算: {confidence:.2f}\")\r\n    \r\n    # 测试任务管理\r\n    print(f\"✓ 运行中任务: {len(workflow_engine.running_tasks)}\")\r\n    print(f\"✓ 已完成任务: {len(workflow_engine.completed_tasks)}\")\r\n    print(f\"✓ 策略缓存: {len(workflow_engine.strategy_cache)}\")\r\n    \r\n    print(\"工作流引擎测试通过！\\n\")\r\n\r\ndef test_memory_usage():\r\n    \"\"\"测试内存使用情况\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"内存使用测试\")\r\n    print(\"=\" * 50)\r\n    \r\n    import psutil\r\n    import os\r\n    \r\n    process = psutil.Process(os.getpid())\r\n    memory_info = process.memory_info()\r\n    \r\n    print(f\"✓ RSS内存使用: {memory_info.rss / 1024 / 1024:.2f} MB\")\r\n    print(f\"✓ VMS内存使用: {memory_info.vms / 1024 / 1024:.2f} MB\")\r\n    \r\n    # 简单的内存负载测试\r\n    from src.ida_pro_mcp.mcp_plugin import analysis_cache\r\n    \r\n    # 填充缓存\r\n    for i in range(100):\r\n        test_data = {\"large_data\": \"x\" * 1000, \"index\": i}\r\n        analysis_cache.put(f\"test_{i}\", (i,), {}, test_data)\r\n    \r\n    memory_after = psutil.Process(os.getpid()).memory_info()\r\n    print(f\"✓ 缓存填充后内存: {memory_after.rss / 1024 / 1024:.2f} MB\")\r\n    \r\n    print(\"内存使用测试通过！\\n\")\r\n\r\ndef run_performance_test():\r\n    \"\"\"性能测试\"\"\"\r\n    print(\"=\" * 50)\r\n    print(\"性能测试\")\r\n    print(\"=\" * 50)\r\n    \r\n    from src.ida_pro_mcp.mcp_plugin import lazy_module_manager, analysis_cache\r\n    \r\n    # 测试延迟初始化性能\r\n    start_time = time.time()\r\n    for _ in range(1000):\r\n        lazy_module_manager.get_module_data('control_flow')\r\n    init_time = time.time() - start_time\r\n    print(f\"✓ 1000次模块数据获取耗时: {init_time:.4f}秒\")\r\n    \r\n    # 测试缓存性能\r\n    start_time = time.time()\r\n    for i in range(1000):\r\n        analysis_cache.get(f\"perf_test_{i % 10}\", (i,), {})\r\n    cache_time = time.time() - start_time\r\n    print(f\"✓ 1000次缓存查询耗时: {cache_time:.4f}秒\")\r\n    \r\n    print(\"性能测试通过！\\n\")\r\n\r\ndef main():\r\n    \"\"\"主测试函数\"\"\"\r\n    print(\"IDA Pro MCP 插件集成测试\")\r\n    print(\"=\" * 70)\r\n    \r\n    # 模拟IDA环境\r\n    mock_ida_environment()\r\n    \r\n    try:\r\n        # 运行各项测试\r\n        test_lazy_module_manager()\r\n        test_analysis_cache()\r\n        test_rpc_registry()\r\n        test_workflow_engine()\r\n        test_memory_usage()\r\n        run_performance_test()\r\n        \r\n        print(\"=\" * 70)\r\n        print(\"🎉 所有集成测试通过！\")\r\n        print(\"=\" * 70)\r\n        \r\n        # 输出最终统计\r\n        from src.ida_pro_mcp.mcp_plugin import lazy_module_manager, analysis_cache\r\n        \r\n        print(\"最终统计信息:\")\r\n        print(f\"- 已初始化模块: {sum(lazy_module_manager.module_states.values())}\")\r\n        print(f\"- 缓存命中率: {analysis_cache.get_stats()['hit_rate']}\")\r\n        print(f\"- 缓存条目数: {analysis_cache.get_stats()['total_entries']}\")\r\n        \r\n        return True\r\n        \r\n    except Exception as e:\r\n        print(f\"❌ 测试失败: {e}\")\r\n        import traceback\r\n        traceback.print_exc()\r\n        return False\r\n\r\nif __name__ == \"__main__\":\r\n    success = main()\r\n    sys.exit(0 if success else 1)\r\n"}]}