#!/usr/bin/env python3
"""
简化的IDA Pro MCP插件功能验证脚本
验证文件大小、函数数量、模块结构等关键指标
"""

import os
import re
import sys
from pathlib import Path

def get_file_size(file_path):
    """获取文件大小（KB）"""
    size_bytes = os.path.getsize(file_path)
    return size_bytes / 1024

def count_lines(file_path):
    """统计文件行数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return len(f.readlines())

def count_functions(file_path):
    """统计@jsonrpc函数数量"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计@jsonrpc装饰器
    jsonrpc_count = len(re.findall(r'@jsonrpc', content))
    
    # 统计函数定义
    func_count = len(re.findall(r'^def\s+\w+\s*\(', content, re.MULTILINE))
    
    # 统计类定义
    class_count = len(re.findall(r'^class\s+\w+', content, re.MULTILINE))
    
    return jsonrpc_count, func_count, class_count

def check_imports(file_path):
    """检查导入语句"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计导入语句
    import_count = len(re.findall(r'^(import|from)\s+', content, re.MULTILINE))
    
    # 检查关键导入
    key_imports = [
        'json', 'time', 'threading', 'queue', 'hashlib', 
        'collections', 'typing', 'functools'
    ]
    
    missing_imports = []
    for imp in key_imports:
        if imp not in content:
            missing_imports.append(imp)
    
    return import_count, missing_imports

def check_module_structure(file_path):
    """检查模块结构"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    components = {
        'LazyModuleManager': 'LazyModuleManager' in content,
        'AnalysisCache': 'AnalysisCache' in content,
        'WorkflowEngine': 'WorkflowEngine' in content,
        'RPCRegistry': 'RPCRegistry' in content,
        'JSONRPCRequestHandler': 'JSONRPCRequestHandler' in content,
        'cached_analysis': '@cached_analysis' in content,
        'lazy_init_module': '@lazy_init_module' in content,
    }
    
    return components

def check_api_functions(file_path):
    """检查关键API函数"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查破解分析模块的关键函数
    crack_functions = [
        'detect_protection_type',
        'generate_analysis_strategy', 
        'execute_batch_analysis',
        'generate_crack_report',
        'detect_anti_debug_techniques',
        'identify_crypto_algorithms',
        'analyze_license_validation',
        'apply_memory_patch',
        'decrypt_encoded_strings'
    ]
    
    found_functions = []
    missing_functions = []
    
    for func in crack_functions:
        if func in content:
            found_functions.append(func)
        else:
            missing_functions.append(func)
    
    return found_functions, missing_functions

def validate_syntax(file_path):
    """验证Python语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            source = f.read()
        compile(source, file_path, 'exec')
        return True, None
    except SyntaxError as e:
        return False, str(e)

def main():
    """主验证函数"""
    print("IDA Pro MCP 插件功能验证")
    print("=" * 50)
    
    plugin_file = "src/ida_pro_mcp/mcp-plugin.py"
    
    if not os.path.exists(plugin_file):
        print(f"❌ 插件文件不存在: {plugin_file}")
        return False
    
    # 1. 文件大小检查
    file_size_kb = get_file_size(plugin_file)
    print(f"📁 文件大小: {file_size_kb:.2f} KB")
    
    # 2. 代码行数统计
    line_count = count_lines(plugin_file)
    print(f"📊 代码行数: {line_count}")
    
    # 3. 语法验证
    syntax_ok, syntax_error = validate_syntax(plugin_file)
    if syntax_ok:
        print("✅ Python语法检查: 通过")
    else:
        print(f"❌ Python语法错误: {syntax_error}")
        return False
    
    # 4. 函数统计
    jsonrpc_count, func_count, class_count = count_functions(plugin_file)
    print(f"🔧 @jsonrpc函数: {jsonrpc_count} 个")
    print(f"🔧 总函数数量: {func_count} 个")
    print(f"🏗️  类定义数量: {class_count} 个")
    
    # 5. 导入检查
    import_count, missing_imports = check_imports(plugin_file)
    print(f"📦 导入语句: {import_count} 个")
    if missing_imports:
        print(f"⚠️  缺失关键导入: {missing_imports}")
    else:
        print("✅ 关键导入: 完整")
    
    # 6. 模块结构检查
    components = check_module_structure(plugin_file)
    print("\n🏗️  核心组件检查:")
    for component, exists in components.items():
        status = "✅" if exists else "❌"
        print(f"   {status} {component}")
    
    # 7. API函数检查
    found_funcs, missing_funcs = check_api_functions(plugin_file)
    print(f"\n🎯 破解分析API: {len(found_funcs)}/{len(found_funcs) + len(missing_funcs)} 个")
    if missing_funcs:
        print(f"   ❌ 缺失: {missing_funcs}")
    else:
        print("   ✅ 所有关键API已实现")
    
    # 8. 综合评估
    print("\n" + "=" * 50)
    print("综合评估结果:")
    
    issues = []
    
    # 检查各项指标
    if not syntax_ok:
        issues.append("语法错误")
    
    if jsonrpc_count < 30:
        issues.append(f"API函数过少({jsonrpc_count}<30)")
    
    if len([c for c in components.values() if c]) < 6:
        issues.append("核心组件不完整")
    
    if missing_funcs:
        issues.append("关键API缺失")
    
    if issues:
        print("❌ 存在问题:")
        for issue in issues:
            print(f"   - {issue}")
        print("\n建议进行修复后再进行性能测试。")
        return False
    else:
        print("🎉 所有功能验证通过！")
        print("\n插件特性:")
        print(f"   - {jsonrpc_count} 个JSON-RPC API函数")
        print(f"   - {len([c for c in components.values() if c])} 个核心组件")
        print(f"   - {len(found_funcs)} 个破解分析功能")
        print(f"   - {file_size_kb:.1f} KB 紧凑代码")
        print(f"   - {line_count} 行高质量代码")
        
        print("\n✅ 准备就绪，可进行性能优化和部署！")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
