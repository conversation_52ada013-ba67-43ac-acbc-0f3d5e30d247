{"sourceFile": "src/ida_pro_mcp/mcp-plugin copy.py", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1754231997243, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1754232679187, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,5062 @@\n+#!/usr/bin/env python3\r\n+# -*- coding: utf-8 -*-\r\n+\"\"\"\r\n+IDA Pro MCP Plugin - 逆向工程自动化插件\r\n+支持83个JSON-RPC功能的高级逆向分析工具\r\n+\"\"\"\r\n+\r\n+import os\r\n+import sys\r\n+\r\n+if sys.version_info < (3, 11):\r\n+    raise RuntimeError(\"Python 3.11 or higher is required for the MCP plugin\")\r\n+\r\n+import json\r\n+import struct\r\n+import threading\r\n+import functools\r\n+import http.server\r\n+import time\r\n+import hashlib\r\n+import weakref\r\n+import base64\r\n+import re\r\n+from collections import OrderedDict\r\n+from urllib.parse import urlparse\r\n+from typing import Any, Callable, get_type_hints, TypedDict, Optional, Annotated, TypeVar, Generic, NotRequired\r\n+\r\n+\r\n+class JSONRPCError(Exception):\r\n+    def __init__(self, code: int, message: str, data: Any = None):\r\n+        self.code = code\r\n+        self.message = message\r\n+        self.data = data\r\n+\r\n+class RPCRegistry:\r\n+    def __init__(self):\r\n+        self.methods: dict[str, Callable] = {}\r\n+        self.unsafe: set[str] = set()\r\n+\r\n+    def register(self, func: Callable) -> Callable:\r\n+        self.methods[func.__name__] = func\r\n+        return func\r\n+\r\n+    def mark_unsafe(self, func: Callable) -> Callable:\r\n+        self.unsafe.add(func.__name__)\r\n+        return func\r\n+\r\n+    def dispatch(self, method: str, params: Any) -> Any:\r\n+        if method not in self.methods:\r\n+            raise JSONRPCError(-32601, f\"Method '{method}' not found\")\r\n+\r\n+        func = self.methods[method]\r\n+        hints = get_type_hints(func)\r\n+\r\n+        # Remove return annotation if present\r\n+        hints.pop(\"return\", None)\r\n+\r\n+        if isinstance(params, list):\r\n+            if len(params) != len(hints):\r\n+                raise JSONRPCError(-32602, f\"Invalid params: expected {len(hints)} arguments, got {len(params)}\")\r\n+\r\n+            # Validate and convert parameters\r\n+            converted_params = []\r\n+            for value, (param_name, expected_type) in zip(params, hints.items()):\r\n+                try:\r\n+                    if not isinstance(value, expected_type):\r\n+                        value = expected_type(value)\r\n+                    converted_params.append(value)\r\n+                except (ValueError, TypeError):\r\n+                    raise JSONRPCError(-32602, f\"Invalid type for parameter '{param_name}': expected {expected_type.__name__}\")\r\n+\r\n+            return func(*converted_params)\r\n+        elif isinstance(params, dict):\r\n+            if set(params.keys()) != set(hints.keys()):\r\n+                raise JSONRPCError(-32602, f\"Invalid params: expected {list(hints.keys())}\")\r\n+\r\n+            # Validate and convert parameters\r\n+            converted_params = {}\r\n+            for param_name, expected_type in hints.items():\r\n+                value = params.get(param_name)\r\n+                try:\r\n+                    if not isinstance(value, expected_type):\r\n+                        value = expected_type(value)\r\n+                    converted_params[param_name] = value\r\n+                except (ValueError, TypeError):\r\n+                    raise JSONRPCError(-32602, f\"Invalid type for parameter '{param_name}': expected {expected_type.__name__}\")\r\n+\r\n+            return func(**converted_params)\r\n+        else:\r\n+            raise JSONRPCError(-32600, \"Invalid Request: params must be array or object\")\r\n+\r\n+rpc_registry = RPCRegistry()\r\n+\r\n+class LazyModuleManager:\r\n+    \"\"\"延迟初始化模块管理器 - 零配置模块延迟加载系统\"\"\"\r\n+    def __init__(self):\r\n+        self.module_states: dict[str, bool] = {}\r\n+        self.module_data: dict[str, Any] = {}\r\n+        self.usage_stats: dict[str, int] = {}\r\n+        self.initialization_lock = threading.Lock()\r\n+    \r\n+    def is_initialized(self, module_name: str) -> bool:\r\n+        \"\"\"检查模块是否已初始化\"\"\"\r\n+        return self.module_states.get(module_name, False)\r\n+    \r\n+    def _init_module_data(self, module_name: str) -> None:\r\n+        \"\"\"延迟加载模块数据\"\"\"\r\n+        if module_name == 'control_flow':\r\n+            self.module_data[module_name] = {\r\n+                'verification_patterns': [b'\\x85\\xc0', b'\\x84\\xc0', b'\\x83\\xf8', b'\\x3b\\xc0'],\r\n+                'jump_opcodes': [b'\\x74', b'\\x75', b'\\x73', b'\\x72', b'\\xe9', b'\\xeb']\r\n+            }\r\n+        elif module_name == 'crypto':\r\n+            self.module_data[module_name] = {\r\n+                'aes_sbox': bytes.fromhex('********************************'),\r\n+                'des_sbox': bytes.fromhex('********************************'),\r\n+                'rsa_patterns': [b'RSA', b'PKCS', b'\\x30\\x82']\r\n+            }\r\n+        elif module_name == 'anti_debug':\r\n+            self.module_data[module_name] = {\r\n+                'api_signatures': [\r\n+                    'IsDebuggerPresent', 'CheckRemoteDebuggerPresent', 'NtQueryInformationProcess',\r\n+                    'OutputDebugString', 'GetTickCount', 'QueryPerformanceCounter', \r\n+                    'ZwQueryInformationProcess', 'NtSetInformationThread', 'NtQueryObject',\r\n+                    'NtQuerySystemInformation', 'CreateToolhelp32Snapshot', 'Process32First',\r\n+                    'Process32Next', 'FindWindow', 'SetUnhandledExceptionFilter'\r\n+                ],\r\n+                'time_check_patterns': [\r\n+                    b'\\x0F\\x31',  # rdtsc\r\n+                    b'\\xFF\\x15',  # call GetTickCount\r\n+                    b'\\x0F\\xA2',  # cpuid  \r\n+                    b'\\x64\\xA1\\x18\\x00\\x00\\x00'  # mov eax, fs:[18h] - PEB access\r\n+                ],\r\n+                'debug_detection_opcodes': [0xCC, 0xCD, 0xF1, 0xCE],  # int3, int imm8, int1, into\r\n+                'bypass_patterns': {\r\n+                    'nop_replace': b'\\x90',\r\n+                    'ret_immediate': b'\\xC3',\r\n+                    'xor_eax_ret': b'\\x31\\xC0\\xC3',\r\n+                    'jmp_short': b'\\xEB\\x02'\r\n+                }\r\n+            }\r\n+        elif module_name == 'license':\r\n+            self.module_data[module_name] = {\r\n+                'license_keywords': ['LICENSE','SERIAL','REGISTER','ACTIVATION','TRIAL','EXPIRED','VALID','INVALID','KEY','CODE','PRODUCT_ID','USER_NAME','COMPANY','EVALUATION','DEMO','FULL_VERSION'],\r\n+                'validation_patterns': [b'\\x83\\xF8\\x01\\x74',b'\\x85\\xC0\\x75',b'\\x3D\\x00\\x00\\x00\\x00\\x74',b'\\x81\\xFE'],\r\n+                'serial_patterns': [b'\\x83\\xF9\\x10',b'\\x83\\xF9\\x14',b'\\x3C\\x2D',b'\\x80\\xF9\\x2D'],\r\n+                'time_apis': ['GetSystemTime','GetLocalTime','GetFileTime','GetTickCount','QueryPerformanceCounter','time','mktime','_time64'],\r\n+                'time_check_patterns': [b'\\x8B\\x45\\xF8\\x3B\\x45\\xFC',b'\\x39\\x4D\\xFC'],\r\n+                'trial_patterns': [b'\\xFF\\x4D\\xFC',b'\\x83\\x6D\\xFC\\x01',b'\\x8B\\x45\\xFC\\x85\\xC0'],\r\n+                'algorithm_signatures': {b'\\x69\\xC0\\x6D\\x4E\\xC6\\x41':'linear_congruential',b'\\x8B\\xC8\\xC1\\xE1\\x05':'simple_hash',b'\\x33\\xC1\\x8B\\xC8':'xor_cipher',b'\\x0F\\xAF\\xC1':'multiplication_check'}\r\n+            }\r\n+        elif module_name == 'memory_patch':\r\n+            self.module_data[module_name] = {\r\n+                'patch_history': [],\r\n+                'safe_instructions': {'nop':b'\\x90','ret':b'\\xC3','int3':b'\\xCC','push_eax':b'\\x50','pop_eax':b'\\x58'},\r\n+                'hook_templates': {'entry_redirect':b'\\xE9\\x00\\x00\\x00\\x00','call_replacement':b'\\xE8\\x00\\x00\\x00\\x00','short_jump':b'\\xEB\\x00','infinite_loop':b'\\xEB\\xFE'},\r\n+                'return_value_patches': {'mov_eax_0':b'\\xB8\\x00\\x00\\x00\\x00','mov_eax_1':b'\\xB8\\x01\\x00\\x00\\x00','xor_eax':b'\\x31\\xC0','or_eax_1':b'\\x83\\xC8\\x01'},\r\n+                'risk_levels': ['low','medium','high','critical']\r\n+            }\r\n+        elif module_name == 'string_analysis':\r\n+            self.module_data[module_name] = {\r\n+                'encoding_patterns': {'base64':'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=','hex':'0123456789ABCDEFabcdef','base32':'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567='},\r\n+                'license_keywords': ['license','serial','key','activation','registration','trial','expired','invalid','valid','product','version','user','company','evaluation','demo','full','lite','professional','enterprise','standard'],\r\n+                'error_patterns': ['error','exception','fail','invalid','incorrect','wrong','bad','corrupt','missing','not found','access denied','permission','unable','cannot','could not','timeout','overflow','underflow'],\r\n+                'resource_indicators': ['menu','dialog','button','label','title','message','text','string','resource','icon','bitmap','cursor','accelerator','version','manifest','caption','tooltip','status'],\r\n+                'common_xor_keys': [0x01,0x02,0x04,0x08,0x10,0x20,0x40,0x80,0xFF,0xAA,0x55,0xCC,0x33],\r\n+                'string_categories': {'high_importance':['password','key','secret','token','hash','encrypted'],'medium_importance':['config','setting','option','parameter','path','file'],'low_importance':['debug','info','log','trace','verbose','temp']}\r\n+            }\r\n+    \r\n+    def initialize_module(self, module_name: str) -> None:\r\n+        \"\"\"线程安全的模块初始化\"\"\"\r\n+        if self.is_initialized(module_name):\r\n+            return\r\n+        \r\n+        with self.initialization_lock:\r\n+            if self.is_initialized(module_name):\r\n+                return\r\n+            \r\n+            self._init_module_data(module_name)\r\n+            self.module_states[module_name] = True\r\n+            self.usage_stats[module_name] = 0\r\n+    \r\n+    def get_module_data(self, module_name: str) -> dict[str, Any]:\r\n+        \"\"\"获取模块数据，触发延迟初始化\"\"\"\r\n+        self.initialize_module(module_name)\r\n+        self.usage_stats[module_name] = self.usage_stats.get(module_name, 0) + 1\r\n+        return self.module_data.get(module_name, {})\r\n+    \r\n+    def get_usage_stats(self) -> dict[str, int]:\r\n+        \"\"\"获取模块使用统计\"\"\"\r\n+        return self.usage_stats.copy()\r\n+\r\n+lazy_module_manager = LazyModuleManager()\r\n+\r\n+class CacheEntry(TypedDict):\r\n+    \"\"\"缓存条目类型定义\"\"\"\r\n+    data: Any\r\n+    timestamp: float\r\n+    access_count: int\r\n+    size: int\r\n+\r\n+class AnalysisCache:\r\n+    \"\"\"基于LRU算法的智能缓存系统 - 轻量级分析结果缓存\"\"\"\r\n+    def __init__(self, max_size: int = 1000, max_memory_mb: int = 10, ttl_seconds: int = 3600):\r\n+        self.max_size = max_size\r\n+        self.max_memory_bytes = max_memory_mb * 1024 * 1024\r\n+        self.ttl_seconds = ttl_seconds\r\n+        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()\r\n+        self.current_memory = 0\r\n+        self.stats = {\r\n+            'hits': 0,\r\n+            'misses': 0,\r\n+            'evictions': 0,\r\n+            'memory_evictions': 0\r\n+        }\r\n+        self.lock = threading.RLock()\r\n+    \r\n+    def _calculate_size(self, data: Any) -> int:\r\n+        \"\"\"估算数据大小（字节）\"\"\"\r\n+        try:\r\n+            if isinstance(data, (str, bytes)):\r\n+                return len(data)\r\n+            elif isinstance(data, (list, tuple)):\r\n+                return sum(self._calculate_size(item) for item in data)\r\n+            elif isinstance(data, dict):\r\n+                return sum(self._calculate_size(k) + self._calculate_size(v) for k, v in data.items())\r\n+            else:\r\n+                return sys.getsizeof(data)\r\n+        except (TypeError, RecursionError):\r\n+            return 100  # 默认估算值\r\n+    \r\n+    def _make_key(self, func_name: str, args: tuple, kwargs: dict) -> str:\r\n+        \"\"\"生成缓存键\"\"\"\r\n+        try:\r\n+            key_data = f\"{func_name}:{str(args)}:{str(sorted(kwargs.items()))}\"\r\n+            return hashlib.md5(key_data.encode('utf-8')).hexdigest()[:16]\r\n+        except (TypeError, UnicodeEncodeError):\r\n+            return f\"{func_name}:{id(args)}:{id(kwargs)}\"\r\n+    \r\n+    def _cleanup_expired(self) -> None:\r\n+        \"\"\"清理过期条目\"\"\"\r\n+        current_time = time.time()\r\n+        expired_keys = [\r\n+            key for key, entry in self.cache.items()\r\n+            if current_time - entry['timestamp'] > self.ttl_seconds\r\n+        ]\r\n+        for key in expired_keys:\r\n+            self._remove_entry(key)\r\n+    \r\n+    def _remove_entry(self, key: str) -> None:\r\n+        \"\"\"移除缓存条目并更新内存统计\"\"\"\r\n+        if key in self.cache:\r\n+            entry = self.cache.pop(key)\r\n+            self.current_memory -= entry['size']\r\n+    \r\n+    def _enforce_limits(self) -> None:\r\n+        \"\"\"强制执行缓存大小和内存限制\"\"\"\r\n+        # 内存限制\r\n+        while self.current_memory > self.max_memory_bytes and self.cache:\r\n+            key = next(iter(self.cache))\r\n+            self._remove_entry(key)\r\n+            self.stats['memory_evictions'] += 1\r\n+        \r\n+        # 大小限制（LRU）\r\n+        while len(self.cache) > self.max_size:\r\n+            key = next(iter(self.cache))\r\n+            self._remove_entry(key)\r\n+            self.stats['evictions'] += 1\r\n+    \r\n+    def get(self, func_name: str, args: tuple, kwargs: dict) -> tuple[bool, Any]:\r\n+        \"\"\"获取缓存数据\"\"\"\r\n+        with self.lock:\r\n+            key = self._make_key(func_name, args, kwargs)\r\n+            \r\n+            if key not in self.cache:\r\n+                self.stats['misses'] += 1\r\n+                return False, None\r\n+            \r\n+            # 检查过期\r\n+            entry = self.cache[key]\r\n+            if time.time() - entry['timestamp'] > self.ttl_seconds:\r\n+                self._remove_entry(key)\r\n+                self.stats['misses'] += 1\r\n+                return False, None\r\n+            \r\n+            # 移动到末尾（LRU更新）\r\n+            self.cache.move_to_end(key)\r\n+            entry['access_count'] += 1\r\n+            self.stats['hits'] += 1\r\n+            return True, entry['data']\r\n+    \r\n+    def put(self, func_name: str, args: tuple, kwargs: dict, data: Any) -> None:\r\n+        \"\"\"存储缓存数据\"\"\"\r\n+        with self.lock:\r\n+            key = self._make_key(func_name, args, kwargs)\r\n+            data_size = self._calculate_size(data)\r\n+            \r\n+            # 如果单个数据太大，不缓存\r\n+            if data_size > self.max_memory_bytes // 4:\r\n+                return\r\n+            \r\n+            # 移除旧条目（如果存在）\r\n+            if key in self.cache:\r\n+                self._remove_entry(key)\r\n+            \r\n+            # 创建新条目\r\n+            entry: CacheEntry = {\r\n+                'data': data,\r\n+                'timestamp': time.time(),\r\n+                'access_count': 1,\r\n+                'size': data_size\r\n+            }\r\n+            \r\n+            self.cache[key] = entry\r\n+            self.current_memory += data_size\r\n+            \r\n+            # 清理过期条目\r\n+            self._cleanup_expired()\r\n+            \r\n+            # 强制执行限制\r\n+            self._enforce_limits()\r\n+    \r\n+    def clear(self) -> None:\r\n+        \"\"\"清空缓存\"\"\"\r\n+        with self.lock:\r\n+            self.cache.clear()\r\n+            self.current_memory = 0\r\n+    \r\n+    def get_stats(self) -> dict[str, Any]:\r\n+        \"\"\"获取缓存统计信息\"\"\"\r\n+        with self.lock:\r\n+            total_requests = self.stats['hits'] + self.stats['misses']\r\n+            hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0\r\n+            \r\n+            return {\r\n+                'hit_rate': f\"{hit_rate:.2%}\",\r\n+                'total_entries': len(self.cache),\r\n+                'memory_usage_mb': f\"{self.current_memory / 1024 / 1024:.2f}\",\r\n+                'stats': self.stats.copy()\r\n+            }\r\n+\r\n+analysis_cache = AnalysisCache()\r\n+\r\n+def cached_analysis(cache_ttl: int = 3600, cache_size_limit: int = 100):\r\n+    \"\"\"缓存装饰器 - 自动缓存分析函数结果\"\"\"\r\n+    def decorator(func: Callable) -> Callable:\r\n+        @functools.wraps(func)\r\n+        def wrapper(*args, **kwargs):\r\n+            # 获取缓存\r\n+            hit, cached_result = analysis_cache.get(func.__name__, args, kwargs)\r\n+            if hit:\r\n+                return cached_result\r\n+            \r\n+            # 执行函数\r\n+            result = func(*args, **kwargs)\r\n+            \r\n+            # 存储到缓存\r\n+            analysis_cache.put(func.__name__, args, kwargs, result)\r\n+            \r\n+            return result\r\n+        \r\n+        setattr(wrapper, '_cached', True)\r\n+        setattr(wrapper, '_cache_ttl', cache_ttl)\r\n+        return wrapper\r\n+    return decorator\r\n+\r\n+def lazy_init_module(module_name: str):\r\n+    \"\"\"延迟初始化模块装饰器 - 集成到现有装饰器链\"\"\"\r\n+    def decorator(func: Callable) -> Callable:\r\n+        @functools.wraps(func)\r\n+        def wrapper(*args, **kwargs):\r\n+            # 确保模块已初始化（延迟初始化）\r\n+            lazy_module_manager.initialize_module(module_name)\r\n+            return func(*args, **kwargs)\r\n+        setattr(wrapper, '_lazy_module', module_name)\r\n+        return wrapper\r\n+    return decorator\r\n+\r\n+def jsonrpc(func: Callable) -> Callable:\r\n+    \"\"\"Decorator to register a function as a JSON-RPC method\"\"\"\r\n+    global rpc_registry\r\n+    return rpc_registry.register(func)\r\n+\r\n+def unsafe(func: Callable) -> Callable:\r\n+    \"\"\"Decorator to register mark a function as unsafe\"\"\"\r\n+    return rpc_registry.mark_unsafe(func)\r\n+\r\n+class JSONRPCRequestHandler(http.server.BaseHTTPRequestHandler):\r\n+    def send_jsonrpc_error(self, code: int, message: str, id: Any = None):\r\n+        response = {\r\n+            \"jsonrpc\": \"2.0\",\r\n+            \"error\": {\r\n+                \"code\": code,\r\n+                \"message\": message\r\n+            }\r\n+        }\r\n+        if id is not None:\r\n+            response[\"id\"] = id\r\n+        response_body = json.dumps(response).encode(\"utf-8\")\r\n+        self.send_response(200)\r\n+        self.send_header(\"Content-Type\", \"application/json\")\r\n+        self.send_header(\"Content-Length\", str(len(response_body)))\r\n+        self.end_headers()\r\n+        self.wfile.write(response_body)\r\n+\r\n+    def do_POST(self):\r\n+        global rpc_registry\r\n+\r\n+        parsed_path = urlparse(self.path)\r\n+        if parsed_path.path != \"/mcp\":\r\n+            self.send_jsonrpc_error(-32098, \"Invalid endpoint\", None)\r\n+            return\r\n+\r\n+        content_length = int(self.headers.get(\"Content-Length\", 0))\r\n+        if content_length == 0:\r\n+            self.send_jsonrpc_error(-32700, \"Parse error: missing request body\", None)\r\n+            return\r\n+\r\n+        request_body = self.rfile.read(content_length)\r\n+        try:\r\n+            request = json.loads(request_body)\r\n+        except json.JSONDecodeError:\r\n+            self.send_jsonrpc_error(-32700, \"Parse error: invalid JSON\", None)\r\n+            return\r\n+\r\n+        # Prepare the response\r\n+        response = {\r\n+            \"jsonrpc\": \"2.0\"\r\n+        }\r\n+        if request.get(\"id\") is not None:\r\n+            response[\"id\"] = request.get(\"id\")\r\n+\r\n+        try:\r\n+            # Basic JSON-RPC validation\r\n+            if not isinstance(request, dict):\r\n+                raise JSONRPCError(-32600, \"Invalid Request\")\r\n+            if request.get(\"jsonrpc\") != \"2.0\":\r\n+                raise JSONRPCError(-32600, \"Invalid JSON-RPC version\")\r\n+            if \"method\" not in request:\r\n+                raise JSONRPCError(-32600, \"Method not specified\")\r\n+\r\n+            # Dispatch the method\r\n+            result = rpc_registry.dispatch(request[\"method\"], request.get(\"params\", []))\r\n+            response[\"result\"] = result\r\n+\r\n+        except JSONRPCError as e:\r\n+            response[\"error\"] = {\r\n+                \"code\": e.code,\r\n+                \"message\": e.message\r\n+            }\r\n+            if e.data is not None:\r\n+                response[\"error\"][\"data\"] = e.data\r\n+        except IDAError as e:\r\n+            response[\"error\"] = {\r\n+                \"code\": -32000,\r\n+                \"message\": e.message,\r\n+            }\r\n+        except Exception as e:\r\n+            traceback.print_exc()\r\n+            response[\"error\"] = {\r\n+                \"code\": -32603,\r\n+                \"message\": \"Internal error (please report a bug)\",\r\n+                \"data\": traceback.format_exc(),\r\n+            }\r\n+\r\n+        try:\r\n+            response_body = json.dumps(response).encode(\"utf-8\")\r\n+        except Exception as e:\r\n+            traceback.print_exc()\r\n+            response_body = json.dumps({\r\n+                \"error\": {\r\n+                    \"code\": -32603,\r\n+                    \"message\": \"Internal error (please report a bug)\",\r\n+                    \"data\": traceback.format_exc(),\r\n+                }\r\n+            }).encode(\"utf-8\")\r\n+\r\n+        self.send_response(200)\r\n+        self.send_header(\"Content-Type\", \"application/json\")\r\n+        self.send_header(\"Content-Length\", str(len(response_body)))\r\n+        self.end_headers()\r\n+        self.wfile.write(response_body)\r\n+\r\n+    def log_message(self, format, *args):\r\n+        # Suppress logging\r\n+        pass\r\n+\r\n+class MCPHTTPServer(http.server.HTTPServer):\r\n+    allow_reuse_address = False\r\n+\r\n+class Server:\r\n+    HOST = \"localhost\"\r\n+    PORT = 13337\r\n+\r\n+    def __init__(self):\r\n+        self.server = None\r\n+        self.server_thread = None\r\n+        self.running = False\r\n+\r\n+    def start(self):\r\n+        if self.running:\r\n+            print(\"[MCP] Server is already running\")\r\n+            return\r\n+\r\n+        self.server_thread = threading.Thread(target=self._run_server, daemon=True)\r\n+        self.running = True\r\n+        self.server_thread.start()\r\n+\r\n+    def stop(self):\r\n+        if not self.running:\r\n+            return\r\n+\r\n+        self.running = False\r\n+        if self.server:\r\n+            self.server.shutdown()\r\n+            self.server.server_close()\r\n+        if self.server_thread:\r\n+            self.server_thread.join()\r\n+            self.server = None\r\n+        print(\"[MCP] Server stopped\")\r\n+\r\n+    def _run_server(self):\r\n+        try:\r\n+            # Create server in the thread to handle binding\r\n+            self.server = MCPHTTPServer((Server.HOST, Server.PORT), JSONRPCRequestHandler)\r\n+            print(f\"[MCP] Server started at http://{Server.HOST}:{Server.PORT}\")\r\n+            self.server.serve_forever()\r\n+        except OSError as e:\r\n+            if e.errno == 98 or e.errno == 10048:  # Port already in use (Linux/Windows)\r\n+                print(\"[MCP] Error: Port 13337 is already in use\")\r\n+            else:\r\n+                print(f\"[MCP] Server error: {e}\")\r\n+            self.running = False\r\n+        except Exception as e:\r\n+            print(f\"[MCP] Server error: {e}\")\r\n+        finally:\r\n+            self.running = False\r\n+\r\n+import logging, queue, traceback, functools\r\n+import ida_hexrays, ida_kernwin, ida_funcs, ida_gdl, ida_lines, ida_idaapi, ida_nalt, ida_bytes, ida_typeinf, ida_xref, ida_entry, ida_idd, ida_dbg, ida_name, ida_ida, ida_frame\r\n+import idc, idaapi, idautils\r\n+\r\n+def safe_find_bytes(start_ea: int, end_ea: int, pattern: bytes) -> int:\r\n+    \"\"\"安全的字节搜索，避免类型转换错误\"\"\"\r\n+    try:\r\n+        # 使用ida_search模块进行字节搜索\r\n+        import ida_search\r\n+        # 将bytes转换为十六进制字符串格式，适用于ida_search.find_binary\r\n+        hex_pattern = ' '.join(f'{b:02X}' for b in pattern)\r\n+        result = ida_search.find_binary(start_ea, end_ea, hex_pattern, 0, idaapi.SEARCH_DOWN)\r\n+        return result if result != idaapi.BADADDR else idaapi.BADADDR\r\n+    except Exception:\r\n+        return idaapi.BADADDR\r\n+\r\n+class IDAError(Exception):\r\n+    def __init__(self, message: str):\r\n+        super().__init__(message)\r\n+\r\n+    @property\r\n+    def message(self) -> str:\r\n+        return self.args[0]\r\n+\r\n+class IDASyncError(Exception):\r\n+    pass\r\n+class DecompilerLicenseError(IDAError):\r\n+    pass\r\n+\r\n+logger = logging.getLogger(__name__)\r\n+\r\n+class IDASafety:\r\n+    ida_kernwin.MFF_READ\r\n+    SAFE_NONE = ida_kernwin.MFF_FAST\r\n+    SAFE_READ = ida_kernwin.MFF_READ\r\n+    SAFE_WRITE = ida_kernwin.MFF_WRITE\r\n+\r\n+call_stack: queue.LifoQueue = queue.LifoQueue()\r\n+\r\n+def sync_wrapper(ff, safety_mode: IDASafety):\r\n+    \"\"\"Call a function ff with a specific IDA safety_mode.\"\"\"\r\n+    if safety_mode not in [IDASafety.SAFE_READ, IDASafety.SAFE_WRITE]:\r\n+        error_str = f'Invalid safety mode {safety_mode} over function {ff.__name__}'\r\n+        logger.error(error_str)\r\n+        raise IDASyncError(error_str)\r\n+    res_container: queue.Queue = queue.Queue()\r\n+    def runned():\r\n+        if not call_stack.empty():\r\n+            last_func_name = call_stack.get()\r\n+            error_str = f'Call stack is not empty while calling the function {ff.__name__} from {last_func_name}'\r\n+            raise IDASyncError(error_str)\r\n+        call_stack.put((ff.__name__))\r\n+        try:\r\n+            res_container.put(ff())\r\n+        except Exception as x:\r\n+            res_container.put(x)\r\n+        finally:\r\n+            call_stack.get()\r\n+    ret_val = idaapi.execute_sync(runned, safety_mode)\r\n+    res = res_container.get()\r\n+    if isinstance(res, Exception):\r\n+        raise res\r\n+    return res\r\n+\r\n+def idawrite(f):\r\n+    \"\"\"Decorator for marking a function as modifying the IDB.\"\"\"\r\n+    @functools.wraps(f)\r\n+    def wrapper(*args, **kwargs):\r\n+        ff = functools.partial(f, *args, **kwargs)\r\n+        ff.__name__ = f.__name__\r\n+        return sync_wrapper(ff, idaapi.MFF_WRITE)\r\n+    return wrapper\r\n+\r\n+def idaread(f):\r\n+    \"\"\"Decorator for marking a function as reading from the IDB.\"\"\"\r\n+    @functools.wraps(f)\r\n+    def wrapper(*args, **kwargs):\r\n+        ff = functools.partial(f, *args, **kwargs)\r\n+        ff.__name__ = f.__name__\r\n+        return sync_wrapper(ff, idaapi.MFF_READ)\r\n+    return wrapper\r\n+\r\n+def is_window_active():\r\n+    \"\"\"Returns whether IDA is currently active\"\"\"\r\n+    try:\r\n+        from PyQt5.QtWidgets import QApplication\r\n+    except ImportError:\r\n+        return False\r\n+\r\n+    app = QApplication.instance()\r\n+    if app is None:\r\n+        return False\r\n+\r\n+    for widget in app.topLevelWidgets():\r\n+        if widget.isActiveWindow():\r\n+            return True\r\n+    return False\r\n+\r\n+class Metadata(TypedDict):\r\n+    path: str\r\n+    module: str\r\n+    base: str\r\n+    size: str\r\n+    md5: str\r\n+    sha256: str\r\n+    crc32: str\r\n+    filesize: str\r\n+\r\n+def get_image_size() -> int:\r\n+    try:\r\n+        info = idaapi.get_inf_structure()\r\n+        omin_ea = info.omin_ea\r\n+        omax_ea = info.omax_ea\r\n+    except AttributeError:\r\n+        import ida_ida\r\n+        omin_ea = ida_ida.inf_get_omin_ea()\r\n+        omax_ea = ida_ida.inf_get_omax_ea()\r\n+    image_size = omax_ea - omin_ea\r\n+    header = idautils.peutils_t().header()\r\n+    if header and header[:4] == b\"PE\\0\\0\":\r\n+        image_size = struct.unpack(\"<I\", header[0x50:0x54])[0]\r\n+    return image_size\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_metadata() -> Metadata:\r\n+    \"\"\"Get metadata about the current IDB\"\"\"\r\n+    def hash(f):\r\n+        try:\r\n+            return f().hex()\r\n+        except:\r\n+            return None\r\n+\r\n+    return Metadata(path=idaapi.get_input_file_path(),\r\n+                    module=idaapi.get_root_filename(),\r\n+                    base=hex(idaapi.get_imagebase()),\r\n+                    size=hex(get_image_size()),\r\n+                    md5=str(hash(ida_nalt.retrieve_input_file_md5() or b\"\")),\r\n+                    sha256=str(hash(ida_nalt.retrieve_input_file_sha256() or b\"\")),\r\n+                    crc32=hex(ida_nalt.retrieve_input_file_crc32()),\r\n+                    filesize=hex(ida_nalt.retrieve_input_file_size()))\r\n+\r\n+def get_prototype(fn: ida_funcs.func_t) -> Optional[str]:\r\n+    try:\r\n+        prototype: ida_typeinf.tinfo_t = fn.get_prototype()\r\n+        if prototype is not None:\r\n+            return str(prototype)\r\n+        else:\r\n+            return None\r\n+    except AttributeError:\r\n+        try:\r\n+            return idc.get_type(fn.start_ea)\r\n+        except:\r\n+            tif = ida_typeinf.tinfo_t()\r\n+            if ida_nalt.get_tinfo(tif, fn.start_ea):\r\n+                return str(tif)\r\n+            return None\r\n+    except Exception as e:\r\n+        print(f\"Error getting function prototype: {e}\")\r\n+        return None\r\n+\r\n+class Function(TypedDict):\r\n+    address: str\r\n+    name: str\r\n+    size: str\r\n+\r\n+def parse_address(address: str) -> int:\r\n+    try:\r\n+        return int(address, 0)\r\n+    except ValueError:\r\n+        for ch in address:\r\n+            if ch not in \"0123456789abcdefABCDEF\":\r\n+                raise IDAError(f\"Failed to parse address: {address}\")\r\n+        raise IDAError(f\"Failed to parse address (missing 0x prefix): {address}\")\r\n+\r\n+def get_function(address: int, *, raise_error=True) -> Optional[Function]:\r\n+    fn = idaapi.get_func(address)\r\n+    if fn is None:\r\n+        if raise_error:\r\n+            raise IDAError(f\"No function found at address {hex(address)}\")\r\n+        return None\r\n+\r\n+    try:\r\n+        name = fn.get_name()\r\n+    except AttributeError:\r\n+        name = ida_funcs.get_func_name(fn.start_ea)\r\n+\r\n+    return Function(address=hex(address), name=name, size=hex(fn.end_ea - fn.start_ea))\r\n+\r\n+DEMANGLED_TO_EA = {}\r\n+\r\n+def create_demangled_to_ea_map():\r\n+    for ea in idautils.Functions():\r\n+        # Get the function name and demangle it\r\n+        # MNG_NODEFINIT inhibits everything except the main name\r\n+        # where default demangling adds the function signature\r\n+        # and decorators (if any)\r\n+        demangled = idaapi.demangle_name(\r\n+            idc.get_name(ea, 0), idaapi.MNG_NODEFINIT)\r\n+        if demangled:\r\n+            DEMANGLED_TO_EA[demangled] = ea\r\n+\r\n+\r\n+def get_type_by_name(type_name: str) -> ida_typeinf.tinfo_t:\r\n+    # 8-bit integers\r\n+    if type_name in ('int8', '__int8', 'int8_t', 'char', 'signed char'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT8)\r\n+    elif type_name in ('uint8', '__uint8', 'uint8_t', 'unsigned char', 'byte', 'BYTE'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT8)\r\n+\r\n+    # 16-bit integers\r\n+    elif type_name in ('int16', '__int16', 'int16_t', 'short', 'short int', 'signed short', 'signed short int'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT16)\r\n+    elif type_name in ('uint16', '__uint16', 'uint16_t', 'unsigned short', 'unsigned short int', 'word', 'WORD'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT16)\r\n+\r\n+    # 32-bit integers\r\n+    elif type_name in ('int32', '__int32', 'int32_t', 'int', 'signed int', 'long', 'long int', 'signed long', 'signed long int'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT32)\r\n+    elif type_name in ('uint32', '__uint32', 'uint32_t', 'unsigned int', 'unsigned long', 'unsigned long int', 'dword', 'DWORD'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT32)\r\n+\r\n+    # 64-bit integers\r\n+    elif type_name in ('int64', '__int64', 'int64_t', 'long long', 'long long int', 'signed long long', 'signed long long int'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT64)\r\n+    elif type_name in ('uint64', '__uint64', 'uint64_t', 'unsigned int64', 'unsigned long long', 'unsigned long long int', 'qword', 'QWORD'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT64)\r\n+\r\n+    # 128-bit integers\r\n+    elif type_name in ('int128', '__int128', 'int128_t', '__int128_t'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_INT128)\r\n+    elif type_name in ('uint128', '__uint128', 'uint128_t', '__uint128_t', 'unsigned int128'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_UINT128)\r\n+\r\n+    # Floating point types\r\n+    elif type_name in ('float', ):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_FLOAT)\r\n+    elif type_name in ('double', ):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_DOUBLE)\r\n+    elif type_name in ('long double', 'ldouble'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_LDOUBLE)\r\n+\r\n+    # Boolean type\r\n+    elif type_name in ('bool', '_Bool', 'boolean'):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_BOOL)\r\n+\r\n+    # Void type\r\n+    elif type_name in ('void', ):\r\n+        return ida_typeinf.tinfo_t(ida_typeinf.BTF_VOID)\r\n+\r\n+    # If not a standard type, try to get a named type\r\n+    tif = ida_typeinf.tinfo_t()\r\n+    if tif.get_named_type(None, type_name, ida_typeinf.BTF_STRUCT):\r\n+        return tif\r\n+\r\n+    if tif.get_named_type(None, type_name, ida_typeinf.BTF_TYPEDEF):\r\n+        return tif\r\n+\r\n+    if tif.get_named_type(None, type_name, ida_typeinf.BTF_ENUM):\r\n+        return tif\r\n+\r\n+    if tif.get_named_type(None, type_name, ida_typeinf.BTF_UNION):\r\n+        return tif\r\n+\r\n+    if tif := ida_typeinf.tinfo_t(type_name):\r\n+        return tif\r\n+\r\n+    raise IDAError(f\"Unable to retrieve {type_name} type info object\")\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_function_by_name(\r\n+    name: Annotated[str, \"Name of the function to get\"]\r\n+) -> Function:\r\n+    \"\"\"Get a function by its name\"\"\"\r\n+    function_address = idaapi.get_name_ea(idaapi.BADADDR, name)\r\n+    if function_address == idaapi.BADADDR:\r\n+        # If map has not been created yet, create it\r\n+        if len(DEMANGLED_TO_EA) == 0:\r\n+            create_demangled_to_ea_map()\r\n+        # Try to find the function in the map, else raise an error\r\n+        if name in DEMANGLED_TO_EA:\r\n+            function_address = DEMANGLED_TO_EA[name]\r\n+        else:\r\n+            raise IDAError(f\"No function found with name {name}\")\r\n+    \r\n+    result = get_function(function_address)\r\n+    if result is None:\r\n+        raise IDAError(f\"No function found at address {hex(function_address)}\")\r\n+    return result\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_function_by_address(\r\n+    address: Annotated[str, \"Address of the function to get\"],\r\n+) -> Function:\r\n+    \"\"\"Get a function by its address\"\"\"\r\n+    result = get_function(parse_address(address))\r\n+    if result is None:\r\n+        raise IDAError(f\"No function found at address {address}\")\r\n+    return result\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_current_address() -> str:\r\n+    \"\"\"Get the address currently selected by the user\"\"\"\r\n+    return hex(idaapi.get_screen_ea())\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_current_function() -> Optional[Function]:\r\n+    \"\"\"Get the function currently selected by the user\"\"\"\r\n+    return get_function(idaapi.get_screen_ea())\r\n+\r\n+class ConvertedNumber(TypedDict):\r\n+    decimal: str\r\n+    hexadecimal: str\r\n+    bytes: str\r\n+    ascii: Optional[str]\r\n+    binary: str\r\n+\r\n+@jsonrpc\r\n+def convert_number(\r\n+    text: Annotated[str, \"Textual representation of the number to convert\"],\r\n+    size: Annotated[Optional[int], \"Size of the variable in bytes\"],\r\n+) -> ConvertedNumber:\r\n+    \"\"\"Convert a number (decimal, hexadecimal) to different representations\"\"\"\r\n+    try:\r\n+        value = int(text, 0)\r\n+    except ValueError:\r\n+        raise IDAError(f\"Invalid number: {text}\")\r\n+\r\n+    # Estimate the size of the number\r\n+    if not size:\r\n+        size = 0\r\n+        n = abs(value)\r\n+        while n:\r\n+            size += 1\r\n+            n >>= 1\r\n+        size += 7\r\n+        size //= 8\r\n+\r\n+    # Convert the number to bytes\r\n+    try:\r\n+        bytes = value.to_bytes(size, \"little\", signed=True)\r\n+    except OverflowError:\r\n+        raise IDAError(f\"Number {text} is too big for {size} bytes\")\r\n+\r\n+    # Convert the bytes to ASCII\r\n+    ascii = \"\"\r\n+    for byte in bytes.rstrip(b\"\\x00\"):\r\n+        if byte >= 32 and byte <= 126:\r\n+            ascii += chr(byte)\r\n+        else:\r\n+            ascii = None\r\n+            break\r\n+\r\n+    return ConvertedNumber(\r\n+        decimal=str(value),\r\n+        hexadecimal=hex(value),\r\n+        bytes=bytes.hex(\" \"),\r\n+        ascii=ascii,\r\n+        binary=bin(value),\r\n+    )\r\n+\r\n+T = TypeVar(\"T\")\r\n+\r\n+class Page(TypedDict, Generic[T]):\r\n+    data: list[T]\r\n+    next_offset: Optional[int]\r\n+\r\n+def paginate(data: list[T], offset: int, count: int) -> Page[T]:\r\n+    if count == 0:\r\n+        count = len(data)\r\n+    next_offset = offset + count\r\n+    if next_offset >= len(data):\r\n+        next_offset = None\r\n+    return {\r\n+        \"data\": data[offset:offset + count],\r\n+        \"next_offset\": next_offset,\r\n+    }\r\n+\r\n+def pattern_filter(data: list[T], pattern: str, key: str) -> list[T]:\r\n+    if not pattern:\r\n+        return data\r\n+\r\n+    # TODO: implement /regex/ matching\r\n+\r\n+    def matches(item: T) -> bool:\r\n+        return pattern.lower() in item[key].lower()\r\n+    return list(filter(matches, data))\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def list_functions(\r\n+    offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+    count: Annotated[int, \"Number of functions to list (100 is a good default, 0 means remainder)\"],\r\n+) -> Page[Function]:\r\n+    \"\"\"List all functions in the database (paginated)\"\"\"\r\n+    functions = [get_function(address) for address in idautils.Functions()]\r\n+    return paginate(functions, offset, count)\r\n+\r\n+class Global(TypedDict):\r\n+    address: str\r\n+    name: str\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def list_globals_filter(\r\n+    offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+    count: Annotated[int, \"Number of globals to list (100 is a good default, 0 means remainder)\"],\r\n+    filter: Annotated[str, \"Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax\"],\r\n+) -> Page[Global]:\r\n+    \"\"\"List matching globals in the database (paginated, filtered)\"\"\"\r\n+    globals = []\r\n+    for addr, name in idautils.Names():\r\n+        # Skip functions\r\n+        if not idaapi.get_func(addr):\r\n+            globals += [Global(address=hex(addr), name=name)]\r\n+\r\n+    globals = pattern_filter(globals, filter, \"name\")\r\n+    return paginate(globals, offset, count)\r\n+\r\n+@jsonrpc\r\n+def list_globals(\r\n+    offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+    count: Annotated[int, \"Number of globals to list (100 is a good default, 0 means remainder)\"],\r\n+) -> Page[Global]:\r\n+    \"\"\"List all globals in the database (paginated)\"\"\"\r\n+    return list_globals_filter(offset, count, \"\")\r\n+\r\n+class Import(TypedDict):\r\n+    address: str\r\n+    imported_name: str\r\n+    module: str\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def list_imports(\r\n+        offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+        count: Annotated[int, \"Number of imports to list (100 is a good default, 0 means remainder)\"],\r\n+) -> Page[Import]:\r\n+    \"\"\" List all imported symbols with their name and module (paginated) \"\"\"\r\n+    nimps = ida_nalt.get_import_module_qty()\r\n+\r\n+    rv = []\r\n+    for i in range(nimps):\r\n+        module_name = ida_nalt.get_import_module_name(i)\r\n+        if not module_name:\r\n+            module_name = \"<unnamed>\"\r\n+\r\n+        def imp_cb(ea, symbol_name, ordinal, acc):\r\n+            if not symbol_name:\r\n+                symbol_name = f\"#{ordinal}\"\r\n+\r\n+            acc += [Import(address=hex(ea), imported_name=symbol_name, module=module_name)]\r\n+\r\n+            return True\r\n+\r\n+        imp_cb_w_context = lambda ea, symbol_name, ordinal: imp_cb(ea, symbol_name, ordinal, rv)\r\n+        ida_nalt.enum_import_names(i, imp_cb_w_context)\r\n+\r\n+    return paginate(rv, offset, count)\r\n+\r\n+class String(TypedDict):\r\n+    address: str\r\n+    length: int\r\n+    string: str\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def list_strings_filter(\r\n+    offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+    count: Annotated[int, \"Number of strings to list (100 is a good default, 0 means remainder)\"],\r\n+    filter: Annotated[str, \"Filter to apply to the list (required parameter, empty string for no filter). Case-insensitive contains or /regex/ syntax\"],\r\n+) -> Page[String]:\r\n+    \"\"\"List matching strings in the database (paginated, filtered)\"\"\"\r\n+    strings = []\r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string = str(item)\r\n+            if string:\r\n+                strings += [\r\n+                    String(address=hex(item.ea), length=item.length, string=string),\r\n+                ]\r\n+        except:\r\n+            continue\r\n+    strings = pattern_filter(strings, filter, \"string\")\r\n+    return paginate(strings, offset, count)\r\n+\r\n+@jsonrpc\r\n+def list_strings(\r\n+    offset: Annotated[int, \"Offset to start listing from (start at 0)\"],\r\n+    count: Annotated[int, \"Number of strings to list (100 is a good default, 0 means remainder)\"],\r\n+) -> Page[String]:\r\n+    \"\"\"List all strings in the database (paginated)\"\"\"\r\n+    return list_strings_filter(offset, count, \"\")\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def list_local_types():\r\n+    \"\"\"List all Local types in the database\"\"\"\r\n+    error = ida_hexrays.hexrays_failure_t()\r\n+    locals = []\r\n+    idati = ida_typeinf.get_idati()\r\n+    type_count = ida_typeinf.get_ordinal_limit(idati)\r\n+    for ordinal in range(1, type_count):\r\n+        try:\r\n+            tif = ida_typeinf.tinfo_t()\r\n+            if tif.get_numbered_type(idati, ordinal):\r\n+                type_name = tif.get_type_name()\r\n+                if not type_name:\r\n+                    type_name = f\"<Anonymous Type #{ordinal}>\"\r\n+                locals.append(f\"\\nType #{ordinal}: {type_name}\")\r\n+                if tif.is_udt():\r\n+                    c_decl_flags = (ida_typeinf.PRTYPE_MULTI | ida_typeinf.PRTYPE_TYPE | ida_typeinf.PRTYPE_SEMI | ida_typeinf.PRTYPE_DEF | ida_typeinf.PRTYPE_METHODS | ida_typeinf.PRTYPE_OFFSETS)\r\n+                    c_decl_output = tif._print(None, c_decl_flags)\r\n+                    if c_decl_output:\r\n+                        locals.append(f\"  C declaration:\\n{c_decl_output}\")\r\n+                else:\r\n+                    simple_decl = tif._print(None, ida_typeinf.PRTYPE_1LINE | ida_typeinf.PRTYPE_TYPE | ida_typeinf.PRTYPE_SEMI)\r\n+                    if simple_decl:\r\n+                        locals.append(f\"  Simple declaration:\\n{simple_decl}\")  \r\n+            else:\r\n+                message = f\"\\nType #{ordinal}: Failed to retrieve information.\"\r\n+                if error.str:\r\n+                    message += f\": {error.str}\"\r\n+                if error.errea != idaapi.BADADDR:\r\n+                    message += f\"from (address: {hex(error.errea)})\"\r\n+                raise IDAError(message)\r\n+        except:\r\n+            continue\r\n+    return locals\r\n+\r\n+def decompile_checked(address: int) -> ida_hexrays.cfunc_t:\r\n+    if not ida_hexrays.init_hexrays_plugin():\r\n+        raise IDAError(\"Hex-Rays decompiler is not available\")\r\n+    error = ida_hexrays.hexrays_failure_t()\r\n+    cfunc: ida_hexrays.cfunc_t = ida_hexrays.decompile_func(address, error, ida_hexrays.DECOMP_WARNINGS)\r\n+    if not cfunc:\r\n+        if error.code == ida_hexrays.MERR_LICENSE:\r\n+            raise DecompilerLicenseError(\"Decompiler licence is not available. Use `disassemble_function` to get the assembly code instead.\")\r\n+\r\n+        message = f\"Decompilation failed at {hex(address)}\"\r\n+        if error.str:\r\n+            message += f\": {error.str}\"\r\n+        if error.errea != idaapi.BADADDR:\r\n+            message += f\" (address: {hex(error.errea)})\"\r\n+        raise IDAError(message)\r\n+    return cfunc\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def decompile_function(\r\n+    address: Annotated[str, \"Address of the function to decompile\"],\r\n+) -> str:\r\n+    \"\"\"Decompile a function at the given address\"\"\"\r\n+    addr_ea = parse_address(address)\r\n+    cfunc = decompile_checked(addr_ea)\r\n+    if is_window_active():\r\n+        ida_hexrays.open_pseudocode(addr_ea, ida_hexrays.OPF_REUSE)\r\n+    sv = cfunc.get_pseudocode()\r\n+    pseudocode = \"\"\r\n+    for i, sl in enumerate(sv):\r\n+        sl: ida_kernwin.simpleline_t\r\n+        item = ida_hexrays.ctree_item_t()\r\n+        addr = None if i > 0 else cfunc.entry_ea\r\n+        if cfunc.get_line_item(sl.line, 0, False, None, item, None):\r\n+            ds = item.dstr().split(\": \")\r\n+            if len(ds) == 2:\r\n+                try:\r\n+                    addr = int(ds[0], 16)\r\n+                except ValueError:\r\n+                    pass\r\n+        line = ida_lines.tag_remove(sl.line)\r\n+        if len(pseudocode) > 0:\r\n+            pseudocode += \"\\n\"\r\n+        if not addr:\r\n+            pseudocode += f\"/* line: {i} */ {line}\"\r\n+        else:\r\n+            pseudocode += f\"/* line: {i}, address: {hex(addr)} */ {line}\"\r\n+\r\n+    return pseudocode\r\n+\r\n+class DisassemblyLine(TypedDict):\r\n+    segment: NotRequired[str]\r\n+    address: str\r\n+    label: NotRequired[str]\r\n+    instruction: str\r\n+    comments: NotRequired[list[str]]\r\n+\r\n+class Argument(TypedDict):\r\n+    name: str\r\n+    type: str\r\n+\r\n+class DisassemblyFunction(TypedDict):\r\n+    name: str\r\n+    start_ea: str\r\n+    return_type: NotRequired[str]\r\n+    arguments: NotRequired[list[Argument]]\r\n+    stack_frame: list[dict]\r\n+    lines: list[DisassemblyLine]\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def disassemble_function(\r\n+    start_address: Annotated[str, \"Address of the function to disassemble\"],\r\n+) -> DisassemblyFunction:\r\n+    \"\"\"Get assembly code for a function\"\"\"\r\n+    start = parse_address(start_address)\r\n+    func: ida_funcs.func_t = idaapi.get_func(start)\r\n+    if not func:\r\n+        raise IDAError(f\"No function found containing address {start_address}\")\r\n+    if is_window_active():\r\n+        ida_kernwin.jumpto(start)\r\n+\r\n+    # 获取函数名\r\n+    try:\r\n+        func_name = func.get_name()\r\n+    except AttributeError:\r\n+        func_name = ida_funcs.get_func_name(func.start_ea) or f\"sub_{func.start_ea:X}\"\r\n+\r\n+    lines = []\r\n+    for address in ida_funcs.func_item_iterator_t(func):\r\n+        seg = idaapi.getseg(address)\r\n+        segment = idaapi.get_segm_name(seg) if seg else None\r\n+\r\n+        label = idc.get_name(address, 0)\r\n+        if label and label == func_name and address == func.start_ea:\r\n+            label = None\r\n+        if label == \"\":\r\n+            label = None\r\n+\r\n+        comments = []\r\n+        if comment := idaapi.get_cmt(address, False):\r\n+            comments += [comment]\r\n+        if comment := idaapi.get_cmt(address, True):\r\n+            comments += [comment]\r\n+\r\n+        raw_instruction = idaapi.generate_disasm_line(address, 0)\r\n+        tls = ida_kernwin.tagged_line_sections_t()\r\n+        ida_kernwin.parse_tagged_line_sections(tls, raw_instruction)\r\n+        insn_section = tls.first(ida_lines.COLOR_INSN)\r\n+\r\n+        operands = []\r\n+        for op_tag in range(ida_lines.COLOR_OPND1, ida_lines.COLOR_OPND8 + 1):\r\n+            op_n = tls.first(op_tag)\r\n+            if not op_n:\r\n+                break\r\n+\r\n+            op: str = op_n.substr(raw_instruction)\r\n+            op_str = ida_lines.tag_remove(op)\r\n+\r\n+            # Do a lot of work to add address comments for symbols\r\n+            for idx in range(len(op) - 2):\r\n+                if op[idx] != idaapi.COLOR_ON:\r\n+                    continue\r\n+\r\n+                idx += 1\r\n+                if ord(op[idx]) != idaapi.COLOR_ADDR:\r\n+                    continue\r\n+\r\n+                idx += 1\r\n+                addr_string = op[idx:idx + idaapi.COLOR_ADDR_SIZE]\r\n+                idx += idaapi.COLOR_ADDR_SIZE\r\n+\r\n+                addr = int(addr_string, 16)\r\n+\r\n+                # Find the next color and slice until there\r\n+                symbol = op[idx:op.find(idaapi.COLOR_OFF, idx)]\r\n+\r\n+                if symbol == '':\r\n+                    # We couldn't figure out the symbol, so use the whole op_str\r\n+                    symbol = op_str\r\n+\r\n+                comments += [f\"{symbol}={addr:#x}\"]\r\n+\r\n+                # print its value if its type is available\r\n+                try:\r\n+                    value = get_global_variable_value_internal(addr)\r\n+                except:\r\n+                    continue\r\n+\r\n+                comments += [f\"*{symbol}={value}\"]\r\n+\r\n+            operands += [op_str]\r\n+\r\n+        mnem = ida_lines.tag_remove(insn_section.substr(raw_instruction)) if insn_section else \"\"\r\n+        mnem = mnem or \"nop\"  # 确保mnem不为空\r\n+        instruction = f\"{mnem} {', '.join(operands)}\" if operands else mnem\r\n+\r\n+        line = DisassemblyLine(\r\n+            address=f\"{address:#x}\",\r\n+            instruction=instruction,\r\n+        )\r\n+\r\n+        if len(comments) > 0:\r\n+            line.update(comments=comments)\r\n+\r\n+        if segment:\r\n+            line.update(segment=segment)\r\n+\r\n+        if label:\r\n+            line.update(label=label)\r\n+\r\n+        lines += [line]\r\n+\r\n+    # 获取函数原型信息\r\n+    try:\r\n+        prototype = func.get_prototype()\r\n+        if prototype:\r\n+            arguments: list[Argument] = []\r\n+            try:\r\n+                for arg in prototype.iter_func():\r\n+                    arguments.append(Argument(name=arg.name, type=f\"{arg.type}\"))\r\n+            except (AttributeError, TypeError):\r\n+                arguments = []\r\n+        else:\r\n+            arguments = []\r\n+    except (AttributeError, TypeError):\r\n+        prototype = None\r\n+        arguments = []\r\n+\r\n+    disassembly_function = DisassemblyFunction(\r\n+        name=func_name,\r\n+        start_ea=f\"{func.start_ea:#x}\",\r\n+        stack_frame=get_stack_frame_variables_internal(func.start_ea),\r\n+        lines=lines\r\n+    )\r\n+\r\n+    if prototype:\r\n+        try:\r\n+            return_type = f\"{prototype.get_rettype()}\"\r\n+            disassembly_function.update(return_type=return_type)\r\n+        except (AttributeError, TypeError):\r\n+            pass\r\n+\r\n+    if arguments:\r\n+        disassembly_function.update(arguments=arguments)\r\n+\r\n+    return disassembly_function\r\n+\r\n+class Xref(TypedDict):\r\n+    address: str\r\n+    type: str\r\n+    function: Optional[Function]\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_xrefs_to(\r\n+    address: Annotated[str, \"Address to get cross references to\"],\r\n+) -> list[Xref]:\r\n+    \"\"\"Get all cross references to the given address\"\"\"\r\n+    xrefs = []\r\n+    xref: ida_xref.xrefblk_t\r\n+    for xref in idautils.XrefsTo(parse_address(address)):\r\n+        xrefs += [\r\n+            Xref(address=hex(xref.frm),\r\n+                 type=\"code\" if xref.iscode else \"data\",\r\n+                 function=get_function(xref.frm, raise_error=False))\r\n+        ]\r\n+    return xrefs\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_xrefs_to_field(\r\n+    struct_name: Annotated[str, \"Name of the struct (type) containing the field\"],\r\n+    field_name: Annotated[str, \"Name of the field (member) to get xrefs to\"],\r\n+) -> list[Xref]:\r\n+    \"\"\"Get all cross references to a named struct field (member)\"\"\"\r\n+\r\n+    # Get the type library\r\n+    til = ida_typeinf.get_idati()\r\n+    if not til:\r\n+        raise IDAError(\"Failed to retrieve type library.\")\r\n+\r\n+    # Get the structure type info\r\n+    tif = ida_typeinf.tinfo_t()\r\n+    if not tif.get_named_type(til, struct_name, ida_typeinf.BTF_STRUCT, True, False):\r\n+        print(f\"Structure '{struct_name}' not found.\")\r\n+        return []\r\n+\r\n+    # Get The field index\r\n+    idx = ida_typeinf.get_udm_by_fullname(None, struct_name + '.' + field_name)\r\n+    if idx == -1:\r\n+        print(f\"Field '{field_name}' not found in structure '{struct_name}'.\")\r\n+        return []\r\n+\r\n+    # Get the type identifier\r\n+    tid = tif.get_udm_tid(idx)\r\n+    if tid == ida_idaapi.BADADDR:\r\n+        raise IDAError(f\"Unable to get tid for structure '{struct_name}' and field '{field_name}'.\")\r\n+\r\n+    # Get xrefs to the tid\r\n+    xrefs = []\r\n+    xref: ida_xref.xrefblk_t\r\n+    for xref in idautils.XrefsTo(tid):\r\n+\r\n+        xrefs += [\r\n+            Xref(address=hex(xref.frm),\r\n+                 type=\"code\" if xref.iscode else \"data\",\r\n+                 function=get_function(xref.frm, raise_error=False))\r\n+        ]\r\n+    return xrefs\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_entry_points() -> list[Function]:\r\n+    \"\"\"Get all entry points in the database\"\"\"\r\n+    result = []\r\n+    for i in range(ida_entry.get_entry_qty()):\r\n+        ordinal = ida_entry.get_entry_ordinal(i)\r\n+        address = ida_entry.get_entry(ordinal)\r\n+        func = get_function(address, raise_error=False)\r\n+        if func is not None:\r\n+            result.append(func)\r\n+    return result\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def set_comment(\r\n+    address: Annotated[str, \"Address in the function to set the comment for\"],\r\n+    comment: Annotated[str, \"Comment text\"],\r\n+):\r\n+    \"\"\"Set a comment for a given address in the function disassembly and pseudocode\"\"\"\r\n+    addr_ea = parse_address(address)\r\n+\r\n+    if not idaapi.set_cmt(addr_ea, comment, False):\r\n+        raise IDAError(f\"Failed to set disassembly comment at {hex(addr_ea)}\")\r\n+\r\n+    if not ida_hexrays.init_hexrays_plugin():\r\n+        return\r\n+\r\n+    # Reference: https://cyber.wtf/2019/03/22/using-ida-python-to-analyze-trickbot/\r\n+    # Check if the address corresponds to a line\r\n+    try:\r\n+        cfunc = decompile_checked(addr_ea)\r\n+    except DecompilerLicenseError:\r\n+        # We failed to decompile the function due to a decompiler license error\r\n+        return\r\n+\r\n+    # Special case for function entry comments\r\n+    if addr_ea == cfunc.entry_ea:\r\n+        idc.set_func_cmt(addr_ea, comment, True)\r\n+        cfunc.refresh_func_ctext()\r\n+        return\r\n+\r\n+    eamap = cfunc.get_eamap()\r\n+    if addr_ea not in eamap:\r\n+        print(f\"Failed to set decompiler comment at {hex(addr_ea)}\")\r\n+        return\r\n+    nearest_ea = eamap[addr_ea][0].ea\r\n+\r\n+    # Remove existing orphan comments\r\n+    if cfunc.has_orphan_cmts():\r\n+        cfunc.del_orphan_cmts()\r\n+        cfunc.save_user_cmts()\r\n+\r\n+    # Set the comment by trying all possible item types\r\n+    tl = idaapi.treeloc_t()\r\n+    tl.ea = nearest_ea\r\n+    for itp in range(idaapi.ITP_SEMI, idaapi.ITP_COLON):\r\n+        tl.itp = itp\r\n+        cfunc.set_user_cmt(tl, comment)\r\n+        cfunc.save_user_cmts()\r\n+        cfunc.refresh_func_ctext()\r\n+        if not cfunc.has_orphan_cmts():\r\n+            return\r\n+        cfunc.del_orphan_cmts()\r\n+        cfunc.save_user_cmts()\r\n+    print(f\"Failed to set decompiler comment at {hex(addr_ea)}\")\r\n+\r\n+def refresh_decompiler_widget():\r\n+    widget = ida_kernwin.get_current_widget()\r\n+    if widget is not None:\r\n+        vu = ida_hexrays.get_widget_vdui(widget)\r\n+        if vu is not None:\r\n+            vu.refresh_ctext()\r\n+\r\n+def refresh_decompiler_ctext(function_address: int):\r\n+    error = ida_hexrays.hexrays_failure_t()\r\n+    cfunc: ida_hexrays.cfunc_t = ida_hexrays.decompile_func(function_address, error, ida_hexrays.DECOMP_WARNINGS)\r\n+    if cfunc:\r\n+        cfunc.refresh_func_ctext()\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def rename_local_variable(\r\n+    function_address: Annotated[str, \"Address of the function containing the variable\"],\r\n+    old_name: Annotated[str, \"Current name of the variable\"],\r\n+    new_name: Annotated[str, \"New name for the variable (empty for a default name)\"],\r\n+):\r\n+    \"\"\"Rename a local variable in a function\"\"\"\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+    if not ida_hexrays.rename_lvar(func.start_ea, old_name, new_name):\r\n+        raise IDAError(f\"Failed to rename local variable {old_name} in function {hex(func.start_ea)}\")\r\n+    refresh_decompiler_ctext(func.start_ea)\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def rename_global_variable(\r\n+    old_name: Annotated[str, \"Current name of the global variable\"],\r\n+    new_name: Annotated[str, \"New name for the global variable (empty for a default name)\"],\r\n+):\r\n+    \"\"\"Rename a global variable\"\"\"\r\n+    ea = idaapi.get_name_ea(idaapi.BADADDR, old_name)\r\n+    if not idaapi.set_name(ea, new_name):\r\n+        raise IDAError(f\"Failed to rename global variable {old_name} to {new_name}\")\r\n+    refresh_decompiler_ctext(ea)\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def set_global_variable_type(\r\n+    variable_name: Annotated[str, \"Name of the global variable\"],\r\n+    new_type: Annotated[str, \"New type for the variable\"],\r\n+):\r\n+    \"\"\"Set a global variable's type\"\"\"\r\n+    ea = idaapi.get_name_ea(idaapi.BADADDR, variable_name)\r\n+    tif = get_type_by_name(new_type)\r\n+    if not tif:\r\n+        raise IDAError(f\"Parsed declaration is not a variable type\")\r\n+    if not ida_typeinf.apply_tinfo(ea, tif, ida_typeinf.PT_SIL):\r\n+        raise IDAError(f\"Failed to apply type\")\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_global_variable_value_by_name(variable_name: Annotated[str, \"Name of the global variable\"]) -> str:\r\n+    \"\"\"\r\n+    Read a global variable's value (if known at compile-time)\r\n+\r\n+    Prefer this function over the `data_read_*` functions.\r\n+    \"\"\"\r\n+    ea = idaapi.get_name_ea(idaapi.BADADDR, variable_name)\r\n+    if ea == idaapi.BADADDR:\r\n+        raise IDAError(f\"Global variable {variable_name} not found\")\r\n+\r\n+    return get_global_variable_value_internal(ea)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_global_variable_value_at_address(ea: Annotated[str, \"Address of the global variable\"]) -> str:\r\n+    \"\"\"\r\n+    Read a global variable's value by its address (if known at compile-time)\r\n+\r\n+    Prefer this function over the `data_read_*` functions.\r\n+    \"\"\"\r\n+    addr_ea = parse_address(ea)\r\n+    return get_global_variable_value_internal(addr_ea)\r\n+\r\n+def get_global_variable_value_internal(ea: int) -> str:\r\n+     # Get the type information for the variable\r\n+     tif = ida_typeinf.tinfo_t()\r\n+     if not ida_nalt.get_tinfo(tif, ea):\r\n+         # No type info, maybe we can figure out its size by its name\r\n+         if not ida_bytes.has_any_name(ea):\r\n+             raise IDAError(f\"Failed to get type information for variable at {ea:#x}\")\r\n+\r\n+         size = ida_bytes.get_item_size(ea)\r\n+         if size == 0:\r\n+             raise IDAError(f\"Failed to get type information for variable at {ea:#x}\")\r\n+     else:\r\n+         # Determine the size of the variable\r\n+         size = tif.get_size()\r\n+\r\n+     # Read the value based on the size\r\n+     if size == 0 and tif.is_array() and tif.get_array_element().is_decl_char():\r\n+         return_string = idaapi.get_strlit_contents(ea, -1, 0).decode(\"utf-8\").strip()\r\n+         return f\"\\\"{return_string}\\\"\"\r\n+     elif size == 1:\r\n+         return hex(ida_bytes.get_byte(ea))\r\n+     elif size == 2:\r\n+         return hex(ida_bytes.get_word(ea))\r\n+     elif size == 4:\r\n+         return hex(ida_bytes.get_dword(ea))\r\n+     elif size == 8:\r\n+         return hex(ida_bytes.get_qword(ea))\r\n+     else:\r\n+         # For other sizes, return the raw bytes\r\n+         return ' '.join(hex(x) for x in ida_bytes.get_bytes(ea, size))\r\n+\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def rename_function(\r\n+    function_address: Annotated[str, \"Address of the function to rename\"],\r\n+    new_name: Annotated[str, \"New name for the function (empty for a default name)\"],\r\n+):\r\n+    \"\"\"Rename a function\"\"\"\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+    if not idaapi.set_name(func.start_ea, new_name):\r\n+        raise IDAError(f\"Failed to rename function {hex(func.start_ea)} to {new_name}\")\r\n+    refresh_decompiler_ctext(func.start_ea)\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def set_function_prototype(\r\n+    function_address: Annotated[str, \"Address of the function\"],\r\n+    prototype: Annotated[str, \"New function prototype\"],\r\n+):\r\n+    \"\"\"Set a function's prototype\"\"\"\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+    try:\r\n+        tif = ida_typeinf.tinfo_t(prototype, None, ida_typeinf.PT_SIL)\r\n+        if not tif.is_func():\r\n+            raise IDAError(f\"Parsed declaration is not a function type\")\r\n+        if not ida_typeinf.apply_tinfo(func.start_ea, tif, ida_typeinf.PT_SIL):\r\n+            raise IDAError(f\"Failed to apply type\")\r\n+        refresh_decompiler_ctext(func.start_ea)\r\n+    except Exception as e:\r\n+        raise IDAError(f\"Failed to parse prototype string: {prototype}\")\r\n+\r\n+class my_modifier_t(ida_hexrays.user_lvar_modifier_t):\r\n+    def __init__(self, var_name: str, new_type: ida_typeinf.tinfo_t):\r\n+        ida_hexrays.user_lvar_modifier_t.__init__(self)\r\n+        self.var_name = var_name\r\n+        self.new_type = new_type\r\n+\r\n+    def modify_lvars(self, lvars):\r\n+        for lvar_saved in lvars.lvvec:\r\n+            lvar_saved: ida_hexrays.lvar_saved_info_t\r\n+            if lvar_saved.name == self.var_name:\r\n+                lvar_saved.type = self.new_type\r\n+                return True\r\n+        return False\r\n+\r\n+# NOTE: This is extremely hacky, but necessary to get errors out of IDA\r\n+def parse_decls_ctypes(decls: str, hti_flags: int) -> tuple[int, str]:\r\n+    if sys.platform == \"win32\":\r\n+        import ctypes\r\n+\r\n+        assert isinstance(decls, str), \"decls must be a string\"\r\n+        assert isinstance(hti_flags, int), \"hti_flags must be an int\"\r\n+        c_decls = decls.encode(\"utf-8\")\r\n+        c_til = None\r\n+        ida_dll = ctypes.CDLL(\"ida\")\r\n+        ida_dll.parse_decls.argtypes = [\r\n+            ctypes.c_void_p,\r\n+            ctypes.c_char_p,\r\n+            ctypes.c_void_p,\r\n+            ctypes.c_int,\r\n+        ]\r\n+        ida_dll.parse_decls.restype = ctypes.c_int\r\n+\r\n+        messages = []\r\n+\r\n+        @ctypes.CFUNCTYPE(ctypes.c_int, ctypes.c_char_p, ctypes.c_char_p)\r\n+        def magic_printer(fmt: bytes, arg1: bytes):\r\n+            if fmt.count(b\"%\") == 1 and b\"%s\" in fmt:\r\n+                formatted = fmt.replace(b\"%s\", arg1)\r\n+                messages.append(formatted.decode(\"utf-8\"))\r\n+                return len(formatted) + 1\r\n+            else:\r\n+                messages.append(f\"unsupported magic_printer fmt: {repr(fmt)}\")\r\n+                return 0\r\n+\r\n+        errors = ida_dll.parse_decls(c_til, c_decls, magic_printer, hti_flags)\r\n+    else:\r\n+        # NOTE: The approach above could also work on other platforms, but it's\r\n+        # not been tested and there are differences in the vararg ABIs.\r\n+        errors = ida_typeinf.parse_decls(None, decls, False, hti_flags)\r\n+        messages = []\r\n+    return errors, messages\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def declare_c_type(\r\n+    c_declaration: Annotated[str, \"C declaration of the type. Examples include: typedef int foo_t; struct bar { int a; bool b; };\"],\r\n+):\r\n+    \"\"\"Create or update a local type from a C declaration\"\"\"\r\n+    # PT_SIL: Suppress warning dialogs (although it seems unnecessary here)\r\n+    # PT_EMPTY: Allow empty types (also unnecessary?)\r\n+    # PT_TYP: Print back status messages with struct tags\r\n+    flags = ida_typeinf.PT_SIL | ida_typeinf.PT_EMPTY | ida_typeinf.PT_TYP\r\n+    errors, messages = parse_decls_ctypes(c_declaration, flags)\r\n+\r\n+    pretty_messages = \"\\n\".join(messages)\r\n+    if errors > 0:\r\n+        raise IDAError(f\"Failed to parse type:\\n{c_declaration}\\n\\nErrors:\\n{pretty_messages}\")\r\n+    return f\"success\\n\\nInfo:\\n{pretty_messages}\"\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def set_local_variable_type(\r\n+    function_address: Annotated[str, \"Address of the decompiled function containing the variable\"],\r\n+    variable_name: Annotated[str, \"Name of the variable\"],\r\n+    new_type: Annotated[str, \"New type for the variable\"],\r\n+):\r\n+    \"\"\"Set a local variable's type\"\"\"\r\n+    try:\r\n+        # Some versions of IDA don't support this constructor\r\n+        new_tif = ida_typeinf.tinfo_t(new_type, None, ida_typeinf.PT_SIL)\r\n+    except Exception:\r\n+        try:\r\n+            new_tif = ida_typeinf.tinfo_t()\r\n+            # parse_decl requires semicolon for the type\r\n+            ida_typeinf.parse_decl(new_tif, None, new_type + \";\", ida_typeinf.PT_SIL)\r\n+        except Exception:\r\n+            raise IDAError(f\"Failed to parse type: {new_type}\")\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+    if not ida_hexrays.rename_lvar(func.start_ea, variable_name, variable_name):\r\n+        raise IDAError(f\"Failed to find local variable: {variable_name}\")\r\n+    modifier = my_modifier_t(variable_name, new_tif)\r\n+    if not ida_hexrays.modify_user_lvars(func.start_ea, modifier):\r\n+        raise IDAError(f\"Failed to modify local variable: {variable_name}\")\r\n+    refresh_decompiler_ctext(func.start_ea)\r\n+\r\n+class StackFrameVariable(TypedDict):\r\n+    name: str\r\n+    offset: str\r\n+    size: str\r\n+    type: str\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_stack_frame_variables(\r\n+        function_address: Annotated[str, \"Address of the disassembled function to retrieve the stack frame variables\"]\r\n+) -> list[StackFrameVariable]:\r\n+    \"\"\" Retrieve the stack frame variables for a given function \"\"\"\r\n+    return get_stack_frame_variables_internal(parse_address(function_address))\r\n+\r\n+def get_stack_frame_variables_internal(function_address: int) -> list[dict]:\r\n+    func = idaapi.get_func(function_address)\r\n+    if not func:\r\n+        return []  # 返回空列表而不是抛出异常\r\n+\r\n+    members = []\r\n+    try:\r\n+        tif = ida_typeinf.tinfo_t()\r\n+        if not tif.get_type_by_tid(func.frame) or not tif.is_udt():\r\n+            return []\r\n+\r\n+        udt = ida_typeinf.udt_type_data_t()\r\n+        tif.get_udt_details(udt)\r\n+        for udm in udt:\r\n+            if not udm.is_gap():\r\n+                name = udm.name or f\"var_{udm.offset:X}\"\r\n+                offset = udm.offset // 8\r\n+                size = udm.size // 8\r\n+                type_str = str(udm.type) if udm.type else \"unknown\"\r\n+\r\n+                members.append({\r\n+                    \"name\": name,\r\n+                    \"offset\": hex(offset),\r\n+                    \"size\": hex(size),\r\n+                    \"type\": type_str\r\n+                })\r\n+    except Exception:\r\n+        # 如果获取堆栈帧信息失败，返回空列表\r\n+        return []\r\n+\r\n+    return members\r\n+\r\n+\r\n+class StructureMember(TypedDict):\r\n+    name: str\r\n+    offset: str\r\n+    size: str\r\n+    type: str\r\n+\r\n+class StructureDefinition(TypedDict):\r\n+    name: str\r\n+    size: str\r\n+    members: list[StructureMember]\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_defined_structures() -> list[StructureDefinition]:\r\n+    \"\"\" Returns a list of all defined structures \"\"\"\r\n+\r\n+    rv = []\r\n+    limit = ida_typeinf.get_ordinal_limit()\r\n+    for ordinal in range(1, limit):\r\n+        tif = ida_typeinf.tinfo_t()\r\n+        tif.get_numbered_type(None, ordinal)\r\n+        if tif.is_udt():\r\n+            udt = ida_typeinf.udt_type_data_t()\r\n+            members = []\r\n+            if tif.get_udt_details(udt):\r\n+                members = [\r\n+                    StructureMember(name=x.name,\r\n+                                    offset=hex(x.offset // 8),\r\n+                                    size=hex(x.size // 8),\r\n+                                    type=str(x.type))\r\n+                    for _, x in enumerate(udt)\r\n+                ]\r\n+\r\n+            rv += [StructureDefinition(name=tif.get_type_name(),\r\n+                                       size=hex(tif.get_size()),\r\n+                                       members=members)]\r\n+\r\n+    return rv\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def rename_stack_frame_variable(\r\n+        function_address: Annotated[str, \"Address of the disassembled function to set the stack frame variables\"],\r\n+        old_name: Annotated[str, \"Current name of the variable\"],\r\n+        new_name: Annotated[str, \"New name for the variable (empty for a default name)\"]\r\n+):\r\n+    \"\"\" Change the name of a stack variable for an IDA function \"\"\"\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+\r\n+    frame_tif = ida_typeinf.tinfo_t()\r\n+    if not ida_frame.get_func_frame(frame_tif, func):\r\n+        raise IDAError(\"No frame returned.\")\r\n+\r\n+    idx, udm = frame_tif.get_udm(old_name)\r\n+    if not udm:\r\n+        raise IDAError(f\"{old_name} not found.\")\r\n+\r\n+    tid = frame_tif.get_udm_tid(idx)\r\n+    if ida_frame.is_special_frame_member(tid):\r\n+        raise IDAError(f\"{old_name} is a special frame member. Will not change the name.\")\r\n+\r\n+    udm = ida_typeinf.udm_t()\r\n+    frame_tif.get_udm_by_tid(udm, tid)\r\n+    offset = udm.offset // 8\r\n+    if ida_frame.is_funcarg_off(func, offset):\r\n+        raise IDAError(f\"{old_name} is an argument member. Will not change the name.\")\r\n+\r\n+    sval = ida_frame.soff_to_fpoff(func, offset)\r\n+    if not ida_frame.define_stkvar(func, new_name, sval, udm.type):\r\n+        raise IDAError(\"failed to rename stack frame variable\")\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def create_stack_frame_variable(\r\n+        function_address: Annotated[str, \"Address of the disassembled function to set the stack frame variables\"],\r\n+        offset: Annotated[str, \"Offset of the stack frame variable\"],\r\n+        variable_name: Annotated[str, \"Name of the stack variable\"],\r\n+        type_name: Annotated[str, \"Type of the stack variable\"]\r\n+):\r\n+    \"\"\" For a given function, create a stack variable at an offset and with a specific type \"\"\"\r\n+\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+\r\n+    offset = parse_address(offset)\r\n+\r\n+    frame_tif = ida_typeinf.tinfo_t()\r\n+    if not ida_frame.get_func_frame(frame_tif, func):\r\n+        raise IDAError(\"No frame returned.\")\r\n+\r\n+    tif = get_type_by_name(type_name)\r\n+    if not ida_frame.define_stkvar(func, variable_name, offset, tif):\r\n+        raise IDAError(\"failed to define stack frame variable\")\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def set_stack_frame_variable_type(\r\n+        function_address: Annotated[str, \"Address of the disassembled function to set the stack frame variables\"],\r\n+        variable_name: Annotated[str, \"Name of the stack variable\"],\r\n+        type_name: Annotated[str, \"Type of the stack variable\"]\r\n+):\r\n+    \"\"\" For a given disassembled function, set the type of a stack variable \"\"\"\r\n+\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+\r\n+    frame_tif = ida_typeinf.tinfo_t()\r\n+    if not ida_frame.get_func_frame(frame_tif, func):\r\n+        raise IDAError(\"No frame returned.\")\r\n+\r\n+    idx, udm = frame_tif.get_udm(variable_name)\r\n+    if not udm:\r\n+        raise IDAError(f\"{variable_name} not found.\")\r\n+\r\n+    tid = frame_tif.get_udm_tid(idx)\r\n+    udm = ida_typeinf.udm_t()\r\n+    frame_tif.get_udm_by_tid(udm, tid)\r\n+    offset = udm.offset // 8\r\n+\r\n+    tif = get_type_by_name(type_name)\r\n+    if not ida_frame.set_frame_member_type(func, offset, tif):\r\n+        raise IDAError(\"failed to set stack frame variable type\")\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def delete_stack_frame_variable(\r\n+        function_address: Annotated[str, \"Address of the function to set the stack frame variables\"],\r\n+        variable_name: Annotated[str, \"Name of the stack variable\"]\r\n+):\r\n+    \"\"\" Delete the named stack variable for a given function \"\"\"\r\n+\r\n+    func = idaapi.get_func(parse_address(function_address))\r\n+    if not func:\r\n+        raise IDAError(f\"No function found at address {function_address}\")\r\n+\r\n+    frame_tif = ida_typeinf.tinfo_t()\r\n+    if not ida_frame.get_func_frame(frame_tif, func):\r\n+        raise IDAError(\"No frame returned.\")\r\n+\r\n+    idx, udm = frame_tif.get_udm(variable_name)\r\n+    if not udm:\r\n+        raise IDAError(f\"{variable_name} not found.\")\r\n+\r\n+    tid = frame_tif.get_udm_tid(idx)\r\n+    if ida_frame.is_special_frame_member(tid):\r\n+        raise IDAError(f\"{variable_name} is a special frame member. Will not delete.\")\r\n+\r\n+    udm = ida_typeinf.udm_t()\r\n+    frame_tif.get_udm_by_tid(udm, tid)\r\n+    offset = udm.offset // 8\r\n+    size = udm.size // 8\r\n+    if ida_frame.is_funcarg_off(func, offset):\r\n+        raise IDAError(f\"{variable_name} is an argument member. Will not delete.\")\r\n+\r\n+    if not ida_frame.delete_frame_members(func, offset, offset+size):\r\n+        raise IDAError(\"failed to delete stack frame variable\")\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def read_memory_bytes(\r\n+        memory_address: Annotated[str, \"Address of the memory value to be read\"],\r\n+        size: Annotated[int, \"size of memory to read\"]\r\n+) -> str:\r\n+    \"\"\"\r\n+    Read bytes at a given address.\r\n+\r\n+    Only use this function if `get_global_variable_at` and `get_global_variable_by_name`\r\n+    both failed.\r\n+    \"\"\"\r\n+    return ' '.join(f'{x:#02x}' for x in ida_bytes.get_bytes(parse_address(memory_address), size))\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def data_read_byte(\r\n+    address: Annotated[str, \"Address to get 1 byte value from\"],\r\n+) -> int:\r\n+    \"\"\"\r\n+    Read the 1 byte value at the specified address.\r\n+\r\n+    Only use this function if `get_global_variable_at` failed.\r\n+    \"\"\"\r\n+    ea = parse_address(address)\r\n+    return ida_bytes.get_wide_byte(ea)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def data_read_word(\r\n+    address: Annotated[str, \"Address to get 2 bytes value from\"],\r\n+) -> int:\r\n+    \"\"\"\r\n+    Read the 2 byte value at the specified address as a WORD.\r\n+\r\n+    Only use this function if `get_global_variable_at` failed.\r\n+    \"\"\"\r\n+    ea = parse_address(address)\r\n+    return ida_bytes.get_wide_word(ea)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def data_read_dword(\r\n+    address: Annotated[str, \"Address to get 4 bytes value from\"],\r\n+) -> int:\r\n+    \"\"\"\r\n+    Read the 4 byte value at the specified address as a DWORD.\r\n+\r\n+    Only use this function if `get_global_variable_at` failed.\r\n+    \"\"\"\r\n+    ea = parse_address(address)\r\n+    return ida_bytes.get_wide_dword(ea)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def data_read_qword(\r\n+        address: Annotated[str, \"Address to get 8 bytes value from\"]\r\n+) -> int:\r\n+    \"\"\"\r\n+    Read the 8 byte value at the specified address as a QWORD.\r\n+\r\n+    Only use this function if `get_global_variable_at` failed.\r\n+    \"\"\"\r\n+    ea = parse_address(address)\r\n+    return ida_bytes.get_qword(ea)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def data_read_string(\r\n+        address: Annotated[str, \"Address to get string from\"]\r\n+) -> str:\r\n+    \"\"\"\r\n+    Read the string at the specified address.\r\n+\r\n+    Only use this function if `get_global_variable_at` failed.\r\n+    \"\"\"\r\n+    try:\r\n+        return idaapi.get_strlit_contents(parse_address(address),-1,0).decode(\"utf-8\")\r\n+    except Exception as e:\r\n+        return \"Error:\" + str(e)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_get_registers() -> list[dict[str, str]]:\r\n+    \"\"\"Get all registers and their values. This function is only available when debugging.\"\"\"\r\n+    result = []\r\n+    dbg = ida_idd.get_dbg()\r\n+    # TODO: raise an exception when not debugging?\r\n+    for thread_index in range(ida_dbg.get_thread_qty()):\r\n+        tid = ida_dbg.getn_thread(thread_index)\r\n+        regs = []\r\n+        regvals = ida_dbg.get_reg_vals(tid)\r\n+        for reg_index, rv in enumerate(regvals):\r\n+            reg_info = dbg.regs(reg_index)\r\n+            reg_value = rv.pyval(reg_info.dtype)\r\n+            if isinstance(reg_value, int):\r\n+                reg_value = hex(reg_value)\r\n+            if isinstance(reg_value, bytes):\r\n+                reg_value = reg_value.hex(\" \")\r\n+            regs.append({\r\n+                \"name\": reg_info.name,\r\n+                \"value\": reg_value,\r\n+            })\r\n+        result.append({\r\n+            \"thread_id\": tid,\r\n+            \"registers\": regs,\r\n+        })\r\n+    return result\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_get_call_stack() -> list[dict[str, str]]:\r\n+    \"\"\"Get the current call stack.\"\"\"\r\n+    callstack = []\r\n+    try:\r\n+        tid = ida_dbg.get_current_thread()\r\n+        trace = ida_idd.call_stack_t()\r\n+\r\n+        if not ida_dbg.collect_stack_trace(tid, trace):\r\n+            return []\r\n+        for frame in trace:\r\n+            frame_info = {\r\n+                \"address\": hex(frame.callea),\r\n+            }\r\n+            try:\r\n+                module_info = ida_idd.modinfo_t()\r\n+                if ida_dbg.get_module_info(frame.callea, module_info):\r\n+                    frame_info[\"module\"] = os.path.basename(module_info.name)\r\n+                else:\r\n+                    frame_info[\"module\"] = \"<unknown>\"\r\n+\r\n+                name = (\r\n+                    ida_name.get_nice_colored_name(\r\n+                        frame.callea,\r\n+                        ida_name.GNCN_NOCOLOR\r\n+                        | ida_name.GNCN_NOLABEL\r\n+                        | ida_name.GNCN_NOSEG\r\n+                        | ida_name.GNCN_PREFDBG,\r\n+                    )\r\n+                    or \"<unnamed>\"\r\n+                )\r\n+                frame_info[\"symbol\"] = name\r\n+\r\n+            except Exception as e:\r\n+                frame_info[\"module\"] = \"<error>\"\r\n+                frame_info[\"symbol\"] = str(e)\r\n+\r\n+            callstack.append(frame_info)\r\n+\r\n+    except Exception as e:\r\n+        pass\r\n+    return callstack\r\n+\r\n+def list_breakpoints():\r\n+    ea = ida_ida.inf_get_min_ea()\r\n+    end_ea = ida_ida.inf_get_max_ea()\r\n+    breakpoints = []\r\n+    while ea <= end_ea:\r\n+        bpt = ida_dbg.bpt_t()\r\n+        if ida_dbg.get_bpt(ea, bpt):\r\n+            breakpoints.append(\r\n+                {\r\n+                    \"ea\": hex(bpt.ea),\r\n+                    \"type\": bpt.type,\r\n+                    \"enabled\": bpt.flags & ida_dbg.BPT_ENABLED,\r\n+                    \"condition\": bpt.condition if bpt.condition else None,\r\n+                }\r\n+            )\r\n+        ea = ida_bytes.next_head(ea, end_ea)\r\n+    return breakpoints\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_list_breakpoints():\r\n+    \"\"\"List all breakpoints in the program.\"\"\"\r\n+    return list_breakpoints()\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_start_process() -> str:\r\n+    \"\"\"Start the debugger\"\"\"\r\n+    if idaapi.start_process(\"\", \"\", \"\"):\r\n+        return \"Debugger started\"\r\n+    return \"Failed to start debugger\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_exit_process() -> str:\r\n+    \"\"\"Exit the debugger\"\"\"\r\n+    if idaapi.exit_process():\r\n+        return \"Debugger exited\"\r\n+    return \"Failed to exit debugger\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_continue_process() -> str:\r\n+    \"\"\"Continue the debugger\"\"\"\r\n+    if idaapi.continue_process():\r\n+        return \"Debugger continued\"\r\n+    return \"Failed to continue debugger\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_run_to(\r\n+    address: Annotated[str, \"Run the debugger to the specified address\"],\r\n+) -> str:\r\n+    \"\"\"Run the debugger to the specified address\"\"\"\r\n+    ea = parse_address(address)\r\n+    if idaapi.run_to(ea):\r\n+        return f\"Debugger run to {hex(ea)}\"\r\n+    return f\"Failed to run to address {hex(ea)}\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_set_breakpoint(\r\n+    address: Annotated[str, \"Set a breakpoint at the specified address\"],\r\n+) -> str:\r\n+    \"\"\"Set a breakpoint at the specified address\"\"\"\r\n+    ea = parse_address(address)\r\n+    if idaapi.add_bpt(ea, 0, idaapi.BPT_SOFT):\r\n+        return f\"Breakpoint set at {hex(ea)}\"\r\n+    breakpoints = list_breakpoints()\r\n+    for bpt in breakpoints:\r\n+        if bpt[\"ea\"] == hex(ea):\r\n+            return f\"Breakpoint already exists at {hex(ea)}\"\r\n+    return f\"Failed to set breakpoint at address {hex(ea)}\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_delete_breakpoint(\r\n+    address: Annotated[str, \"del a breakpoint at the specified address\"],\r\n+) -> str:\r\n+    \"\"\"del a breakpoint at the specified address\"\"\"\r\n+    ea = parse_address(address)\r\n+    if idaapi.del_bpt(ea):\r\n+        return f\"Breakpoint deleted at {hex(ea)}\"\r\n+    return f\"Failed to delete breakpoint at address {hex(ea)}\"\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+@unsafe\r\n+def dbg_enable_breakpoint(\r\n+    address: Annotated[str, \"Enable or disable a breakpoint at the specified address\"],\r\n+    enable: Annotated[bool, \"Enable or disable a breakpoint\"],\r\n+) -> str:\r\n+    \"\"\"Enable or disable a breakpoint at the specified address\"\"\"\r\n+    ea = parse_address(address)\r\n+    if idaapi.enable_bpt(ea, enable):\r\n+        return f\"Breakpoint {'enabled' if enable else 'disabled'} at {hex(ea)}\"\r\n+    return f\"Failed to {'' if enable else 'disable '}breakpoint at address {hex(ea)}\"\r\n+\r\n+class MCP(idaapi.plugin_t):\r\n+    flags = idaapi.PLUGIN_KEEP\r\n+    comment = \"MCP Plugin\"\r\n+    help = \"MCP\"\r\n+    wanted_name = \"MCP\"\r\n+    wanted_hotkey = \"Ctrl-Alt-M\"\r\n+\r\n+    def init(self):\r\n+        self.server = Server()\r\n+        hotkey = MCP.wanted_hotkey.replace(\"-\", \"+\")\r\n+        if sys.platform == \"darwin\":\r\n+            hotkey = hotkey.replace(\"Alt\", \"Option\")\r\n+        print(f\"[MCP] Plugin loaded, use Edit -> Plugins -> MCP ({hotkey}) to start the server\")\r\n+        return idaapi.PLUGIN_KEEP\r\n+\r\n+    def run(self, args):\r\n+        self.server.start()\r\n+\r\n+    def term(self):\r\n+        self.server.stop()\r\n+\r\n+def PLUGIN_ENTRY():\r\n+    return MCP()\r\n+\r\n+# 控制流分析模块 - 破解导向的控制流分析功能\r\n+class VerificationPoint(TypedDict):\r\n+    address: str\r\n+    type: str\r\n+    instruction: str\r\n+    target: Optional[str]\r\n+\r\n+class JumpCondition(TypedDict):\r\n+    address: str\r\n+    instruction: str\r\n+    condition: str\r\n+    true_target: str\r\n+    false_target: str\r\n+\r\n+class CallChainEntry(TypedDict):\r\n+    caller: str\r\n+    callee: str\r\n+    call_site: str\r\n+\r\n+class TamperingPoint(TypedDict):\r\n+    address: str\r\n+    function: str\r\n+    return_type: str\r\n+    modification_hint: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存，最多100个条目\r\n+@lazy_init_module('control_flow')\r\n+@idaread\r\n+def identify_verification_points(function_address: Annotated[str, \"函数地址\"]) -> list[VerificationPoint]:\r\n+    \"\"\"识别关键验证逻辑点\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"未找到函数: {function_address}\")\r\n+    \r\n+    patterns = lazy_module_manager.get_module_data('control_flow')['verification_patterns']\r\n+    points = []\r\n+    \r\n+    for addr in ida_funcs.func_item_iterator_t(func):\r\n+        raw_bytes = ida_bytes.get_bytes(addr, 8)\r\n+        if any(pattern in raw_bytes for pattern in patterns):\r\n+            disasm_line = idc.generate_disasm_line(addr, 0)\r\n+            mnem = disasm_line.split()[0].lower()\r\n+            if mnem in ['cmp', 'test', 'sub', 'xor']:\r\n+                points.append(VerificationPoint(\r\n+                    address=hex(addr),\r\n+                    type=\"comparison\",\r\n+                    instruction=disasm_line,\r\n+                    target=None\r\n+                ))\r\n+    \r\n+    return points[:10]  # 限制返回数量\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('control_flow')\r\n+@idaread \r\n+def analyze_jump_conditions(function_address: Annotated[str, \"函数地址\"]) -> list[JumpCondition]:\r\n+    \"\"\"分析条件跳转逻辑\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"未找到函数: {function_address}\")\r\n+    \r\n+    jump_opcodes = lazy_module_manager.get_module_data('control_flow')['jump_opcodes']\r\n+    conditions = []\r\n+    \r\n+    for addr in ida_funcs.func_item_iterator_t(func):\r\n+        raw_bytes = ida_bytes.get_bytes(addr, 1)\r\n+        if any(raw_bytes.startswith(opcode) for opcode in jump_opcodes):\r\n+            disasm_line = idc.generate_disasm_line(addr, 0)\r\n+            mnem = disasm_line.split()[0].lower()\r\n+            if mnem.startswith('j') and mnem != 'jmp':\r\n+                # 尝试获取目标地址\r\n+                try:\r\n+                    operand = idc.get_operand_value(addr, 0)\r\n+                    true_target = hex(operand) if operand != idaapi.BADADDR else \"unknown\"\r\n+                except:\r\n+                    true_target = \"unknown\"\r\n+                false_target = hex(addr + idc.get_item_size(addr))\r\n+                \r\n+                conditions.append(JumpCondition(\r\n+                    address=hex(addr),\r\n+                    instruction=disasm_line,\r\n+                    condition=mnem,\r\n+                    true_target=true_target,\r\n+                    false_target=false_target\r\n+                ))\r\n+    \r\n+    return conditions[:8]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('control_flow')\r\n+@idaread\r\n+def trace_function_call_chain(start_address: Annotated[str, \"起始地址\"]) -> list[CallChainEntry]:\r\n+    \"\"\"追踪函数调用链\"\"\"\r\n+    ea = parse_address(start_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"未找到函数: {start_address}\")\r\n+    \r\n+    chain = []\r\n+    caller_name = ida_funcs.get_func_name(func.start_ea)\r\n+    \r\n+    for addr in ida_funcs.func_item_iterator_t(func):\r\n+        disasm_line = idc.generate_disasm_line(addr, 0)\r\n+        mnem = disasm_line.split()[0].lower()\r\n+        \r\n+        if mnem == 'call':\r\n+            try:\r\n+                callee_addr = idc.get_operand_value(addr, 0)\r\n+                if callee_addr != idaapi.BADADDR:\r\n+                    callee_func = idaapi.get_func(callee_addr)\r\n+                    if callee_func:\r\n+                        callee_name = ida_funcs.get_func_name(callee_func.start_ea)\r\n+                        chain.append(CallChainEntry(\r\n+                            caller=caller_name,\r\n+                            callee=callee_name,\r\n+                            call_site=hex(addr)\r\n+                        ))\r\n+            except:\r\n+                continue\r\n+    \r\n+    return chain[:12]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('control_flow')\r\n+@idaread\r\n+def identify_return_tampering_points(function_address: Annotated[str, \"函数地址\"]) -> list[TamperingPoint]:\r\n+    \"\"\"定位返回值篡改点\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"未找到函数: {function_address}\")\r\n+    \r\n+    points = []\r\n+    func_name = ida_funcs.get_func_name(func.start_ea)\r\n+    \r\n+    for addr in ida_funcs.func_item_iterator_t(func):\r\n+        disasm_line = idc.generate_disasm_line(addr, 0)\r\n+        mnem = disasm_line.split()[0].lower()\r\n+        \r\n+        if mnem in ['ret', 'retn']:\r\n+            # 检查返回前的指令\r\n+            prev_addr = idaapi.prev_head(addr, func.start_ea)\r\n+            if prev_addr != idaapi.BADADDR:\r\n+                prev_disasm = idc.generate_disasm_line(prev_addr, 0)\r\n+                prev_mnem = prev_disasm.split()[0].lower()\r\n+                if prev_mnem in ['mov', 'xor', 'sub', 'add']:\r\n+                    # 检查是否修改返回寄存器 (简化检查)\r\n+                    if any(reg in prev_disasm.lower() for reg in ['eax', 'rax', 'al', 'ax']):\r\n+                        points.append(TamperingPoint(\r\n+                            address=hex(prev_addr),\r\n+                            function=func_name,\r\n+                            return_type=\"register_modification\",\r\n+                            modification_hint=\"修改返回寄存器值以绕过验证\"\r\n+                        ))\r\n+    \r\n+    return points[:6]\r\n+\r\n+# 加密算法识别模块 - 自动识别加密算法特征\r\n+class CryptoMatch(TypedDict):\r\n+    algorithm: str\r\n+    confidence: float\r\n+    address: str\r\n+    signature_type: str\r\n+\r\n+class CryptoKey(TypedDict):\r\n+    address: str\r\n+    algorithm: str\r\n+    key_size: int\r\n+    confidence: float\r\n+\r\n+class CryptoFlow(TypedDict):\r\n+    algorithm: str\r\n+    operations: list[str]\r\n+    data_flow: list[str]\r\n+    confidence: float\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=1800, cache_size_limit=50)  # 30分钟缓存，最多50个条目\r\n+@lazy_init_module('crypto')\r\n+@idaread\r\n+def identify_crypto_algorithms(search_area: Annotated[Optional[str], \"搜索区域地址（可选）\"] = None) -> list[CryptoMatch]:\r\n+    \"\"\"识别加密算法特征\"\"\"\r\n+    signatures = lazy_module_manager.get_module_data('crypto')\r\n+    matches = []\r\n+    \r\n+    if search_area:\r\n+        start_ea = parse_address(search_area)\r\n+        end_ea = start_ea + 0x1000  # 搜索4KB范围\r\n+    else:\r\n+        start_ea = idaapi.get_imagebase()\r\n+        end_ea = start_ea + 0x10000  # 搜索64KB范围\r\n+    \r\n+    # AES S-box识别\r\n+    aes_sbox = signatures['aes_sbox']\r\n+    # 使用安全的字节搜索函数\r\n+    addr = safe_find_bytes(start_ea, end_ea, aes_sbox[:8])\r\n+    if addr != idaapi.BADADDR:\r\n+        matches.append(CryptoMatch(\r\n+            algorithm=\"AES\",\r\n+            confidence=0.85,\r\n+            address=hex(addr),\r\n+            signature_type=\"sbox\"\r\n+        ))\r\n+    \r\n+    # DES S-box识别\r\n+    des_sbox = signatures['des_sbox']\r\n+    addr = safe_find_bytes(start_ea, end_ea, des_sbox[:8])\r\n+    if addr != idaapi.BADADDR:\r\n+        matches.append(CryptoMatch(\r\n+            algorithm=\"DES\",\r\n+            confidence=0.80,\r\n+            address=hex(addr),\r\n+            signature_type=\"sbox\"\r\n+        ))\r\n+    \r\n+    # RSA特征识别\r\n+    for pattern in signatures['rsa_patterns']:\r\n+        addr = safe_find_bytes(start_ea, end_ea, pattern)\r\n+        if addr != idaapi.BADADDR:\r\n+            matches.append(CryptoMatch(\r\n+                algorithm=\"RSA\",\r\n+                confidence=0.70,\r\n+                address=hex(addr),\r\n+                signature_type=\"asn1_header\"\r\n+            ))\r\n+    \r\n+    return matches[:8]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('crypto')\r\n+@idaread\r\n+def locate_crypto_keys(algorithm: Annotated[str, \"算法类型\"], search_address: Annotated[str, \"搜索起始地址\"]) -> list[CryptoKey]:\r\n+    \"\"\"定位加密密钥\"\"\"\r\n+    ea = parse_address(search_address)\r\n+    keys = []\r\n+    \r\n+    if algorithm.upper() == \"AES\":\r\n+        # AES密钥通常是16/24/32字节的连续数据\r\n+        for key_size in [16, 24, 32]:\r\n+            for offset in range(0, 0x1000, 4):  # 搜索4KB范围\r\n+                addr = ea + offset\r\n+                key_data = ida_bytes.get_bytes(addr, key_size)\r\n+                if key_data and len(key_data) == key_size:\r\n+                    # 简单启发式：检查密钥熵\r\n+                    entropy = len(set(key_data)) / len(key_data)\r\n+                    if entropy > 0.6:  # 高熵值可能是密钥\r\n+                        keys.append(CryptoKey(\r\n+                            address=hex(addr),\r\n+                            algorithm=\"AES\",\r\n+                            key_size=key_size,\r\n+                            confidence=entropy\r\n+                        ))\r\n+    \r\n+    elif algorithm.upper() == \"DES\":\r\n+        # DES密钥是8字节\r\n+        for offset in range(0, 0x800, 4):\r\n+            addr = ea + offset\r\n+            key_data = ida_bytes.get_bytes(addr, 8)\r\n+            if key_data and len(key_data) == 8:\r\n+                entropy = len(set(key_data)) / len(key_data)\r\n+                if entropy > 0.5:\r\n+                    keys.append(CryptoKey(\r\n+                        address=hex(addr),\r\n+                        algorithm=\"DES\",\r\n+                        key_size=8,\r\n+                        confidence=entropy\r\n+                    ))\r\n+    \r\n+    return keys[:6]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('crypto')\r\n+@idaread\r\n+def analyze_crypto_flow(function_address: Annotated[str, \"函数地址\"]) -> CryptoFlow:\r\n+    \"\"\"分析加密流程\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"未找到函数: {function_address}\")\r\n+    \r\n+    operations = []\r\n+    data_flow = []\r\n+    algorithm = \"unknown\"\r\n+    confidence = 0.0\r\n+    \r\n+    # 分析函数内的操作模式\r\n+    xor_count = 0\r\n+    shift_count = 0\r\n+    loop_count = 0\r\n+    \r\n+    for addr in ida_funcs.func_item_iterator_t(func):\r\n+        disasm = idc.generate_disasm_line(addr, 0)\r\n+        mnem = disasm.split()[0].lower()\r\n+        \r\n+        if mnem == 'xor':\r\n+            xor_count += 1\r\n+            operations.append(\"xor\")\r\n+        elif mnem in ['shl', 'shr', 'rol', 'ror']:\r\n+            shift_count += 1\r\n+            operations.append(\"shift\")\r\n+        elif mnem in ['loop', 'loope', 'loopne']:\r\n+            loop_count += 1\r\n+            operations.append(\"loop\")\r\n+        elif mnem == 'call':\r\n+            operations.append(\"call\")\r\n+            data_flow.append(hex(addr))\r\n+    \r\n+    # 简单的算法推断\r\n+    if xor_count > 5 and shift_count > 3:\r\n+        algorithm = \"stream_cipher\"\r\n+        confidence = min(0.8, (xor_count + shift_count) / 20.0)\r\n+    elif loop_count > 0 and xor_count > 3:\r\n+        algorithm = \"block_cipher\"\r\n+        confidence = min(0.7, (loop_count + xor_count) / 15.0)\r\n+    elif shift_count > 8:\r\n+        algorithm = \"hash_function\"\r\n+        confidence = min(0.6, shift_count / 20.0)\r\n+    \r\n+    return CryptoFlow(\r\n+        algorithm=algorithm,\r\n+        operations=operations[:10],\r\n+        data_flow=data_flow[:8],\r\n+        confidence=confidence\r\n+    )\r\n+\r\n+# 反调试检测模块 - 自动检测和绕过反调试技术\r\n+class AntiDebugDetection(TypedDict):\r\n+    technique: str\r\n+    address: str\r\n+    api_name: str\r\n+    risk_level: str\r\n+    bypass_suggestion: str\r\n+\r\n+class BypassStrategy(TypedDict):\r\n+    target: str\r\n+    method: str\r\n+    description: str\r\n+    risk_level: str\r\n+    patch_bytes: Optional[str]\r\n+\r\n+class PatchResult(TypedDict):\r\n+    address: str\r\n+    original_bytes: str\r\n+    patched_bytes: str\r\n+    status: str\r\n+    backup_info: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=75)\r\n+@lazy_init_module('anti_debug')\r\n+@idaread\r\n+def detect_anti_debug_techniques(search_area: Annotated[Optional[str], \"搜索区域（可选）\"] = None) -> list[AntiDebugDetection]:\r\n+    \"\"\"检测反调试技术\"\"\"\r\n+    signatures = lazy_module_manager.get_module_data('anti_debug')\r\n+    detections = []\r\n+    if search_area:\r\n+        start_ea = parse_address(search_area)\r\n+        end_ea = start_ea + 0x2000\r\n+    else:\r\n+        start_ea = idaapi.get_imagebase()\r\n+        end_ea = start_ea + 0x20000\r\n+    for api_name in signatures['api_signatures']:\r\n+        for addr, name in idautils.Names():\r\n+            if start_ea <= addr <= end_ea and api_name.lower() in name.lower():\r\n+                for xref in idautils.XrefsTo(addr):\r\n+                    if xref.type == ida_xref.fl_CN:\r\n+                        detections.append(AntiDebugDetection(technique=\"api_call\",address=hex(xref.frm),api_name=api_name,risk_level=\"high\",bypass_suggestion=f\"在{hex(xref.frm)}处NOP掉对{api_name}的调用\"))\r\n+    for pattern in signatures['time_check_patterns']:\r\n+        addr = safe_find_bytes(start_ea, end_ea, pattern)\r\n+        if addr != idaapi.BADADDR:\r\n+            detections.append(AntiDebugDetection(technique=\"timing_check\",address=hex(addr),api_name=\"rdtsc/GetTickCount\",risk_level=\"medium\",bypass_suggestion=\"修改时间检查逻辑或固定时间差值\"))\r\n+    addr = start_ea\r\n+    while addr < end_ea:\r\n+        byte_val = ida_bytes.get_byte(addr)\r\n+        if byte_val == 0xCC:\r\n+            func = idaapi.get_func(addr)\r\n+            if func:\r\n+                detections.append(AntiDebugDetection(technique=\"int3_check\",address=hex(addr),api_name=\"INT3\",risk_level=\"medium\",bypass_suggestion=\"将INT3指令替换为NOP(0x90)\"))\r\n+        addr += 1\r\n+        if len(detections) >= 5:\r\n+            break\r\n+    \r\n+    return detections[:10]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('anti_debug')\r\n+@idaread\r\n+def generate_bypass_strategies(detection_address: Annotated[str, \"检测点地址\"]) -> list[BypassStrategy]:\r\n+    \"\"\"生成绕过策略\"\"\"\r\n+    ea = parse_address(detection_address)\r\n+    strategies = []\r\n+    \r\n+    # 获取当前指令\r\n+    disasm = idc.generate_disasm_line(ea, 0)\r\n+    mnem = disasm.split()[0].lower()\r\n+    \r\n+    if 'call' in mnem:\r\n+        # API调用绕过策略\r\n+        strategies.append(BypassStrategy(\r\n+            target=hex(ea),\r\n+            method=\"nop_instruction\",\r\n+            description=\"将CALL指令替换为NOP指令\",\r\n+            risk_level=\"low\",\r\n+            patch_bytes=\"90\" * idc.get_item_size(ea)\r\n+        ))\r\n+        \r\n+        strategies.append(BypassStrategy(\r\n+            target=hex(ea),\r\n+            method=\"return_zero\",\r\n+            description=\"修改函数返回值为0（未检测到调试器）\",\r\n+            risk_level=\"medium\",\r\n+            patch_bytes=\"31C0C3\"  # xor eax,eax; ret\r\n+        ))\r\n+    \r\n+    elif mnem == 'int':\r\n+        # 中断指令绕过\r\n+        strategies.append(BypassStrategy(\r\n+            target=hex(ea),\r\n+            method=\"nop_replace\",\r\n+            description=\"将INT指令替换为NOP\",\r\n+            risk_level=\"low\",\r\n+            patch_bytes=\"90\"\r\n+        ))\r\n+    \r\n+    elif mnem in ['rdtsc', 'cpuid']:\r\n+        # 时间检查绕过\r\n+        strategies.append(BypassStrategy(\r\n+            target=hex(ea),\r\n+            method=\"constant_value\",\r\n+            description=\"返回固定的时间值\",\r\n+            risk_level=\"medium\",\r\n+            patch_bytes=\"B800000000C3\"  # mov eax, 0; ret\r\n+        ))\r\n+    \r\n+    # 通用策略\r\n+    strategies.append(BypassStrategy(\r\n+        target=hex(ea),\r\n+        method=\"conditional_jump\",\r\n+        description=\"修改条件跳转，强制跳过反调试检查\",\r\n+        risk_level=\"high\",\r\n+        patch_bytes=\"EB\"  # JMP short\r\n+    ))\r\n+    \r\n+    return strategies[:6]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('anti_debug')\r\n+@idawrite\r\n+def apply_anti_debug_patches(patch_address: Annotated[str, \"补丁地址\"], patch_method: Annotated[str, \"补丁方法\"]) -> PatchResult:\r\n+    \"\"\"应用反调试绕过补丁\"\"\"\r\n+    ea = parse_address(patch_address)\r\n+    original_size = idc.get_item_size(ea)\r\n+    original_bytes = ida_bytes.get_bytes(ea, original_size)\r\n+    if not original_bytes:\r\n+        raise IDAError(f\"无法读取地址{patch_address}的原始字节\")\r\n+    backup_info = f\"{hex(ea)}:{original_bytes.hex()}\"\r\n+    try:\r\n+        if patch_method == \"nop_instruction\":\r\n+            patch_bytes = b'\\x90' * original_size\r\n+        elif patch_method == \"return_zero\":\r\n+            if original_size >= 3:\r\n+                patch_bytes = b'\\x31\\xC0\\xC3' + b'\\x90' * (original_size - 3)\r\n+            else:\r\n+                patch_bytes = b'\\x90' * original_size\r\n+        elif patch_method == \"nop_replace\":\r\n+            patch_bytes = b'\\x90' * original_size\r\n+        elif patch_method == \"conditional_jump\":\r\n+            if original_size >= 2:\r\n+                patch_bytes = b'\\xEB\\x00' + b'\\x90' * (original_size - 2)\r\n+            else:\r\n+                patch_bytes = b'\\x90' * original_size\r\n+        else:\r\n+            raise IDAError(f\"不支持的补丁方法: {patch_method}\")\r\n+        for i, byte_val in enumerate(patch_bytes):\r\n+            ida_bytes.patch_byte(ea + i, byte_val)\r\n+        return PatchResult(address=hex(ea),original_bytes=original_bytes.hex(),patched_bytes=patch_bytes.hex(),status=\"success\",backup_info=backup_info)\r\n+    except Exception as e:\r\n+        return PatchResult(address=hex(ea),original_bytes=original_bytes.hex(),patched_bytes=\"\",status=f\"failed: {str(e)}\",backup_info=backup_info)\r\n+\r\n+# 许可证验证分析模块 - 用于分析软件许可证验证逻辑的破解导向工具\r\n+class LicenseValidation(TypedDict):\r\n+    location: str\r\n+    validation_type: str\r\n+    algorithm_pattern: str\r\n+    input_format: str\r\n+    validation_strength: str\r\n+    bypass_suggestion: str\r\n+\r\n+class SerialFormat(TypedDict):\r\n+    pattern: str\r\n+    length: int\r\n+    charset: str\r\n+    validation_address: str\r\n+    algorithm_hint: str\r\n+    sample_format: str\r\n+\r\n+class TimeRestriction(TypedDict):\r\n+    check_type: str\r\n+    address: str\r\n+    limit_value: str\r\n+    comparison_method: str\r\n+    bypass_strategy: str\r\n+    risk_level: str\r\n+\r\n+class KeygenHint(TypedDict):\r\n+    algorithm_type: str\r\n+    input_constraints: str\r\n+    validation_steps: list[str]\r\n+    key_generation_strategy: str\r\n+    success_indicators: str\r\n+    difficulty_level: str\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('license')\r\n+@idaread\r\n+def analyze_license_validation(target_area: Annotated[Optional[str], \"目标分析区域（可选）\"] = None) -> list[LicenseValidation]:\r\n+    \"\"\"分析许可证验证逻辑\"\"\"\r\n+    patterns = lazy_module_manager.get_module_data('license')\r\n+    validations = []\r\n+    \r\n+    if target_area:\r\n+        start_ea = parse_address(target_area)\r\n+        end_ea = start_ea + 0x5000  # 搜索20KB范围\r\n+    else:\r\n+        start_ea = idaapi.get_imagebase()\r\n+        end_ea = start_ea + 0x50000  # 搜索320KB范围\r\n+    \r\n+    # 搜索许可证相关字符串\r\n+    for license_str in patterns['license_keywords']:\r\n+        string_refs = []\r\n+        for s in idautils.Strings():\r\n+            if license_str.lower() in str(s).lower() and start_ea <= s.ea <= end_ea:\r\n+                string_refs.append(s.ea)\r\n+        \r\n+        for str_addr in string_refs:\r\n+            # 查找引用此字符串的代码\r\n+            for xref in idautils.XrefsTo(str_addr):\r\n+                if xref.type == ida_xref.fl_CN:  # 代码引用\r\n+                    func = idaapi.get_func(xref.frm)\r\n+                    if func:\r\n+                        # 分析函数中的验证模式\r\n+                        validation_type = \"string_check\"\r\n+                        if \"serial\" in license_str.lower():\r\n+                            validation_type = \"serial_validation\"\r\n+                        elif \"register\" in license_str.lower():\r\n+                            validation_type = \"registration_check\"\r\n+                        elif \"license\" in license_str.lower():\r\n+                            validation_type = \"license_verification\"\r\n+                        \r\n+                        validations.append(LicenseValidation(\r\n+                            location=hex(xref.frm),\r\n+                            validation_type=validation_type,\r\n+                            algorithm_pattern=\"string_comparison\",\r\n+                            input_format=\"user_input_string\",\r\n+                            validation_strength=\"low\",\r\n+                            bypass_suggestion=f\"在{hex(xref.frm)}处修改比较结果或跳过验证\"\r\n+                        ))\r\n+    \r\n+    # 检测常见的许可证验证算法模式\r\n+    for pattern in patterns['validation_patterns']:\r\n+        addr = safe_find_bytes(start_ea, end_ea, pattern)\r\n+        if addr != idaapi.BADADDR:\r\n+            # 分析周围的代码模式\r\n+            func = idaapi.get_func(addr)\r\n+            if func:\r\n+                validations.append(LicenseValidation(\r\n+                    location=hex(addr),\r\n+                    validation_type=\"algorithmic_check\",\r\n+                    algorithm_pattern=\"checksum_validation\",\r\n+                    input_format=\"numeric_calculation\",\r\n+                    validation_strength=\"medium\",\r\n+                    bypass_suggestion=\"识别算法逻辑，构造有效的验证值\"\r\n+                ))\r\n+    \r\n+    return validations[:8]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('license')\r\n+@idaread\r\n+def trace_serial_validation(serial_input_area: Annotated[str, \"序列号输入处理区域地址\"]) -> list[SerialFormat]:\r\n+    \"\"\"追踪序列号验证过程\"\"\"\r\n+    ea = parse_address(serial_input_area)\r\n+    patterns = lazy_module_manager.get_module_data('license')\r\n+    formats = []\r\n+    \r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{serial_input_area}不在有效函数内\")\r\n+    \r\n+    # 分析函数中的数据流\r\n+    for addr in range(func.start_ea, func.end_ea, 4):\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            # 检测字符串长度检查\r\n+            if insn.itype == idaapi.NN_cmp and insn.Op2.value in range(8, 64):\r\n+                formats.append(SerialFormat(\r\n+                    pattern=\"fixed_length\",\r\n+                    length=insn.Op2.value,\r\n+                    charset=\"alphanumeric\",\r\n+                    validation_address=hex(addr),\r\n+                    algorithm_hint=\"length_check\",\r\n+                    sample_format=f\"{'X' * insn.Op2.value}\"\r\n+                ))\r\n+            \r\n+            # 检测分隔符模式\r\n+            if insn.itype == idaapi.NN_cmp and insn.Op2.value in [ord('-'), ord('_'), ord('.')]:\r\n+                formats.append(SerialFormat(\r\n+                    pattern=\"separated_groups\",\r\n+                    length=0,\r\n+                    charset=\"with_separators\",\r\n+                    validation_address=hex(addr),\r\n+                    algorithm_hint=\"format_validation\",\r\n+                    sample_format=\"XXXX-XXXX-XXXX-XXXX\"\r\n+                ))\r\n+    \r\n+    # 检测数字范围验证\r\n+    for pattern in patterns['serial_patterns']:\r\n+        if safe_find_bytes(func.start_ea, func.end_ea, pattern) != idaapi.BADADDR:\r\n+            formats.append(SerialFormat(\r\n+                pattern=\"numeric_range\",\r\n+                length=16,\r\n+                charset=\"digits_only\",\r\n+                validation_address=hex(func.start_ea),\r\n+                algorithm_hint=\"range_validation\",\r\n+                sample_format=\"1234567890123456\"\r\n+            ))\r\n+    \r\n+    return formats[:6]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('license')\r\n+@idaread\r\n+def detect_time_limitations(search_scope: Annotated[Optional[str], \"搜索范围（可选）\"] = None) -> list[TimeRestriction]:\r\n+    \"\"\"检测时间限制机制\"\"\"\r\n+    patterns = lazy_module_manager.get_module_data('license')\r\n+    restrictions = []\r\n+    \r\n+    if search_scope:\r\n+        start_ea = parse_address(search_scope)\r\n+        end_ea = start_ea + 0x3000  # 搜索12KB范围\r\n+    else:\r\n+        start_ea = idaapi.get_imagebase()\r\n+        end_ea = start_ea + 0x30000  # 搜索192KB范围\r\n+    \r\n+    # 检测时间相关API调用\r\n+    for time_api in patterns['time_apis']:\r\n+        for addr, name in idautils.Names():\r\n+            if start_ea <= addr <= end_ea and time_api.lower() in name.lower():\r\n+                for xref in idautils.XrefsTo(addr):\r\n+                    if xref.type == ida_xref.fl_CN:\r\n+                        restrictions.append(TimeRestriction(\r\n+                            check_type=\"api_time_check\",\r\n+                            address=hex(xref.frm),\r\n+                            limit_value=\"unknown\",\r\n+                            comparison_method=\"system_time\",\r\n+                            bypass_strategy=\"Hook API返回固定时间或修改比较逻辑\",\r\n+                            risk_level=\"medium\"\r\n+                        ))\r\n+    \r\n+    # 检测时间戳比较模式\r\n+    for pattern in patterns['time_check_patterns']:\r\n+        addr = safe_find_bytes(start_ea, end_ea, pattern)\r\n+        if addr != idaapi.BADADDR:\r\n+            restrictions.append(TimeRestriction(\r\n+                check_type=\"timestamp_comparison\",\r\n+                address=hex(addr),\r\n+                limit_value=\"embedded_timestamp\",\r\n+                comparison_method=\"direct_comparison\",\r\n+                bypass_strategy=\"修改内置时间戳或跳过比较\",\r\n+                risk_level=\"low\"\r\n+            ))\r\n+    \r\n+    # 检测试用期计数器\r\n+    for pattern in patterns['trial_patterns']:\r\n+        addr = safe_find_bytes(start_ea, end_ea, pattern)\r\n+        if addr != idaapi.BADADDR:\r\n+            restrictions.append(TimeRestriction(\r\n+                check_type=\"usage_counter\",\r\n+                address=hex(addr),\r\n+                limit_value=\"usage_limit\",\r\n+                comparison_method=\"counter_decrement\",\r\n+                bypass_strategy=\"重置计数器或修改比较条件\",\r\n+                risk_level=\"high\"\r\n+            ))\r\n+    \r\n+    return restrictions[:5]\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('license')\r\n+@idaread\r\n+def generate_keygen_hints(validation_function: Annotated[str, \"验证函数地址\"]) -> KeygenHint:\r\n+    \"\"\"生成注册机提示信息\"\"\"\r\n+    ea = parse_address(validation_function)\r\n+    patterns = lazy_module_manager.get_module_data('license')\r\n+    \r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{validation_function}不是有效的函数\")\r\n+    \r\n+    # 分析函数复杂度和验证步骤\r\n+    validation_steps = []\r\n+    algorithm_type = \"unknown\"\r\n+    difficulty = \"medium\"\r\n+    \r\n+    # 检测算法类型\r\n+    for algo_pattern, algo_name in patterns['algorithm_signatures'].items():\r\n+        if safe_find_bytes(func.start_ea, func.end_ea, algo_pattern) != idaapi.BADADDR:\r\n+            algorithm_type = algo_name\r\n+            break\r\n+    \r\n+    # 分析验证步骤\r\n+    instruction_count = 0\r\n+    has_complex_math = False\r\n+    has_string_ops = False\r\n+    \r\n+    for addr in range(func.start_ea, func.end_ea, 4):\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            instruction_count += 1\r\n+            \r\n+            # 检测复杂数学运算\r\n+            if insn.itype in [idaapi.NN_mul, idaapi.NN_div, idaapi.NN_imul, idaapi.NN_idiv]:\r\n+                has_complex_math = True\r\n+                validation_steps.append(\"执行数学运算验证\")\r\n+            \r\n+            # 检测字符串操作\r\n+            if insn.itype in [idaapi.NN_rep, idaapi.NN_movs, idaapi.NN_cmps]:\r\n+                has_string_ops = True\r\n+                validation_steps.append(\"进行字符串格式检查\")\r\n+    \r\n+    # 确定难度级别\r\n+    if instruction_count > 100 or has_complex_math:\r\n+        difficulty = \"high\"\r\n+    elif instruction_count < 20 and not has_complex_math:\r\n+        difficulty = \"low\"\r\n+    \r\n+    # 生成输入约束\r\n+    input_constraints = \"标准字符串输入\"\r\n+    if has_string_ops:\r\n+        input_constraints = \"格式化字符串，可能包含分隔符\"\r\n+    if has_complex_math:\r\n+        input_constraints = \"数值输入，需满足特定数学关系\"\r\n+    \r\n+    return KeygenHint(\r\n+        algorithm_type=algorithm_type,\r\n+        input_constraints=input_constraints,\r\n+        validation_steps=validation_steps[:5],\r\n+        key_generation_strategy=\"反向工程验证算法，构造满足条件的输入\",\r\n+        success_indicators=\"函数返回非零值或跳转到成功分支\",\r\n+        difficulty_level=difficulty\r\n+    )\r\n+\r\n+# 内存补丁与代码修改模块 - 运行时补丁和代码修改的高级破解技术\r\n+class MemoryPatch(TypedDict):\r\n+    patch_id: str\r\n+    address: str\r\n+    original_bytes: str\r\n+    patched_bytes: str\r\n+    size: int\r\n+    timestamp: str\r\n+    description: str\r\n+    is_active: bool\r\n+\r\n+class InstructionMod(TypedDict):\r\n+    address: str\r\n+    original_instruction: str\r\n+    modified_instruction: str\r\n+    modification_type: str\r\n+    risk_level: str\r\n+    rollback_data: str\r\n+\r\n+class FunctionHook(TypedDict):\r\n+    target_function: str\r\n+    hook_address: str\r\n+    hook_type: str\r\n+    original_entry: str\r\n+    redirect_target: str\r\n+    status: str\r\n+\r\n+class ReturnValuePatch(TypedDict):\r\n+    function_address: str\r\n+    original_return_type: str\r\n+    patched_return_value: str\r\n+    patch_method: str\r\n+    success_rate: str\r\n+\r\n+class PatchHistoryEntry(TypedDict):\r\n+    operation_id: str\r\n+    operation_type: str\r\n+    target_address: str\r\n+    timestamp: str\r\n+    operation_data: str\r\n+    rollback_possible: bool\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('memory_patch')\r\n+@idawrite\r\n+def apply_memory_patch(target_address: Annotated[str, \"目标地址\"], patch_data: Annotated[str, \"补丁数据（十六进制）\"], description: Annotated[str, \"补丁描述\"]) -> MemoryPatch:\r\n+    \"\"\"应用内存补丁\"\"\"\r\n+    ea = parse_address(target_address)\r\n+    patch_bytes = bytes.fromhex(patch_data.replace(' ', ''))\r\n+    patch_size = len(patch_bytes)\r\n+    \r\n+    # 读取原始字节\r\n+    original_bytes = ida_bytes.get_bytes(ea, patch_size)\r\n+    if not original_bytes:\r\n+        raise IDAError(f\"无法读取地址{target_address}的原始数据\")\r\n+    \r\n+    # 生成补丁ID\r\n+    import time\r\n+    patch_id = f\"patch_{int(time.time())}_{ea:x}\"\r\n+    timestamp = time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+    \r\n+    try:\r\n+        # 应用补丁\r\n+        for i, byte_val in enumerate(patch_bytes):\r\n+            ida_bytes.patch_byte(ea + i, byte_val)\r\n+        \r\n+        # 记录补丁信息\r\n+        patch_info = MemoryPatch(\r\n+            patch_id=patch_id,\r\n+            address=hex(ea),\r\n+            original_bytes=original_bytes.hex(),\r\n+            patched_bytes=patch_data,\r\n+            size=patch_size,\r\n+            timestamp=timestamp,\r\n+            description=description,\r\n+            is_active=True\r\n+        )\r\n+        \r\n+        # 存储到补丁历史（使用模块数据存储）\r\n+        module_data = lazy_module_manager.get_module_data('memory_patch')\r\n+        module_data['patch_history'].append(patch_info)\r\n+        \r\n+        return patch_info\r\n+        \r\n+    except Exception as e:\r\n+        raise IDAError(f\"补丁应用失败: {str(e)}\")\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('memory_patch')\r\n+@idawrite\r\n+def modify_instruction(instruction_address: Annotated[str, \"指令地址\"], new_instruction: Annotated[str, \"新指令\"]) -> InstructionMod:\r\n+    \"\"\"修改单条指令\"\"\"\r\n+    ea = parse_address(instruction_address)\r\n+    \r\n+    # 获取原始指令\r\n+    original_disasm = idc.generate_disasm_line(ea, 0)\r\n+    original_size = idc.get_item_size(ea)\r\n+    original_bytes = ida_bytes.get_bytes(ea, original_size)\r\n+    \r\n+    if not original_bytes:\r\n+        raise IDAError(f\"无法读取地址{instruction_address}的指令\")\r\n+    \r\n+    # 简化的指令修改（实际实现会更复杂）\r\n+    modification_type = \"unknown\"\r\n+    risk_level = \"medium\"\r\n+    \r\n+    # 检测修改类型\r\n+    if \"nop\" in new_instruction.lower():\r\n+        modification_type = \"nop_replacement\"\r\n+        risk_level = \"low\"\r\n+        # NOP指令\r\n+        new_bytes = b'\\x90' * original_size\r\n+    elif \"ret\" in new_instruction.lower():\r\n+        modification_type = \"return_injection\"\r\n+        risk_level = \"high\"\r\n+        # RET指令\r\n+        new_bytes = b'\\xC3' + b'\\x90' * (original_size - 1)\r\n+    elif \"jmp\" in new_instruction.lower():\r\n+        modification_type = \"jump_modification\"\r\n+        risk_level = \"high\"\r\n+        # 简单的短跳转\r\n+        new_bytes = b'\\xEB\\x00' + b'\\x90' * (original_size - 2)\r\n+    else:\r\n+        # 默认用NOP填充\r\n+        modification_type = \"custom_modification\"\r\n+        risk_level = \"high\"\r\n+        new_bytes = b'\\x90' * original_size\r\n+    \r\n+    try:\r\n+        # 应用修改\r\n+        for i, byte_val in enumerate(new_bytes):\r\n+            ida_bytes.patch_byte(ea + i, byte_val)\r\n+        \r\n+        return InstructionMod(\r\n+            address=hex(ea),\r\n+            original_instruction=original_disasm,\r\n+            modified_instruction=new_instruction,\r\n+            modification_type=modification_type,\r\n+            risk_level=risk_level,\r\n+            rollback_data=original_bytes.hex()\r\n+        )\r\n+        \r\n+    except Exception as e:\r\n+        raise IDAError(f\"指令修改失败: {str(e)}\")\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('memory_patch')\r\n+@idawrite\r\n+def hook_function_calls(function_name: Annotated[str, \"函数名称\"], hook_method: Annotated[str, \"Hook方法\"]) -> FunctionHook:\r\n+    \"\"\"Hook函数调用\"\"\"\r\n+    # 查找函数地址\r\n+    func_addr = idaapi.get_name_ea(idaapi.BADADDR, function_name)\r\n+    if func_addr == idaapi.BADADDR:\r\n+        raise IDAError(f\"函数{function_name}未找到\")\r\n+    \r\n+    func = idaapi.get_func(func_addr)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{hex(func_addr)}不是有效函数\")\r\n+    \r\n+    # 读取原始入口点\r\n+    original_entry_bytes = ida_bytes.get_bytes(func.start_ea, 5)  # 读取5字节用于跳转\r\n+    if not original_entry_bytes:\r\n+        raise IDAError(f\"无法读取函数{function_name}的入口点\")\r\n+    \r\n+    hook_address = hex(func.start_ea)\r\n+    status = \"pending\"\r\n+    \r\n+    try:\r\n+        if hook_method == \"entry_redirect\":\r\n+            # 在函数入口点插入跳转指令（简化实现）\r\n+            # 实际应用中需要更复杂的重定向逻辑\r\n+            # 这里只是演示概念\r\n+            jmp_instruction = b'\\xEB\\xFE'  # jmp short $-2 (无限循环，防止执行)\r\n+            ida_bytes.patch_byte(func.start_ea, jmp_instruction[0])\r\n+            ida_bytes.patch_byte(func.start_ea + 1, jmp_instruction[1])\r\n+            status = \"active\"\r\n+            \r\n+        elif hook_method == \"call_interception\":\r\n+            # 拦截对此函数的调用（需要扫描所有调用点）\r\n+            call_count = 0\r\n+            for xref in idautils.XrefsTo(func.start_ea):\r\n+                if xref.type == ida_xref.fl_CN:  # 代码调用\r\n+                    call_count += 1\r\n+            status = f\"intercepted_{call_count}_calls\"\r\n+            \r\n+        return FunctionHook(\r\n+            target_function=function_name,\r\n+            hook_address=hook_address,\r\n+            hook_type=hook_method,\r\n+            original_entry=original_entry_bytes.hex(),\r\n+            redirect_target=\"hook_handler\",\r\n+            status=status\r\n+        )\r\n+        \r\n+    except Exception as e:\r\n+        raise IDAError(f\"函数Hook失败: {str(e)}\")\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('memory_patch')\r\n+@idawrite\r\n+def patch_return_values(function_address: Annotated[str, \"函数地址\"], return_value: Annotated[str, \"返回值\"]) -> ReturnValuePatch:\r\n+    \"\"\"修改函数返回值\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{function_address}不是有效函数\")\r\n+    \r\n+    # 分析函数的返回指令\r\n+    ret_instructions = []\r\n+    for addr in range(func.start_ea, func.end_ea):\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            if insn.itype == idaapi.NN_ret or insn.itype == idaapi.NN_retn:\r\n+                ret_instructions.append(addr)\r\n+    \r\n+    if not ret_instructions:\r\n+        raise IDAError(f\"函数{function_address}中未找到返回指令\")\r\n+    \r\n+    # 确定返回值类型和补丁方法\r\n+    original_return_type = \"unknown\"\r\n+    patch_method = \"register_modification\"\r\n+    \r\n+    try:\r\n+        # 解析返回值\r\n+        ret_val = int(return_value, 0)\r\n+        \r\n+        # 在每个返回指令前插入设置返回值的代码\r\n+        success_count = 0\r\n+        for ret_addr in ret_instructions:\r\n+            # 简化实现：在返回前设置EAX寄存器\r\n+            if ret_val == 0:\r\n+                # mov eax, 0\r\n+                patch_bytes = b'\\xB8\\x00\\x00\\x00\\x00'\r\n+            elif ret_val == 1:\r\n+                # mov eax, 1\r\n+                patch_bytes = b'\\xB8\\x01\\x00\\x00\\x00'\r\n+            else:\r\n+                # mov eax, immediate\r\n+                patch_bytes = b'\\xB8' + ret_val.to_bytes(4, 'little')\r\n+            \r\n+            # 检查是否有足够空间（需要5字节）\r\n+            if ret_addr - func.start_ea >= 5:\r\n+                # 在返回指令前插入（简化实现）\r\n+                for i, byte_val in enumerate(patch_bytes):\r\n+                    ida_bytes.patch_byte(ret_addr - 5 + i, byte_val)\r\n+                success_count += 1\r\n+        \r\n+        success_rate = f\"{success_count}/{len(ret_instructions)}\"\r\n+        \r\n+        return ReturnValuePatch(\r\n+            function_address=hex(func.start_ea),\r\n+            original_return_type=original_return_type,\r\n+            patched_return_value=return_value,\r\n+            patch_method=patch_method,\r\n+            success_rate=success_rate\r\n+        )\r\n+        \r\n+    except ValueError:\r\n+        raise IDAError(f\"无效的返回值格式: {return_value}\")\r\n+    except Exception as e:\r\n+        raise IDAError(f\"返回值补丁失败: {str(e)}\")\r\n+\r\n+@jsonrpc\r\n+@lazy_init_module('memory_patch')\r\n+@idaread\r\n+def manage_patch_history(operation: Annotated[str, \"操作类型: list/rollback/clear\"]) -> list[PatchHistoryEntry]:\r\n+    \"\"\"管理补丁历史记录\"\"\"\r\n+    import time\r\n+    if operation == \"list\":\r\n+        module_data = lazy_module_manager.get_module_data('memory_patch')\r\n+        history = []\r\n+        for i, patch in enumerate(module_data['patch_history']):\r\n+            history.append(PatchHistoryEntry(operation_id=patch['patch_id'],operation_type=\"memory_patch\",target_address=patch['address'],timestamp=patch['timestamp'],operation_data=f\"Size: {patch['size']}, Desc: {patch['description']}\",rollback_possible=patch['is_active']))\r\n+        return history\r\n+    elif operation == \"rollback\":\r\n+        module_data = lazy_module_manager.get_module_data('memory_patch')\r\n+        if module_data['patch_history']:\r\n+            last_patch = module_data['patch_history'][-1]\r\n+            if last_patch['is_active']:\r\n+                try:\r\n+                    ea = int(last_patch['address'], 16)\r\n+                    original_bytes = bytes.fromhex(last_patch['original_bytes'])\r\n+                    for i, byte_val in enumerate(original_bytes):\r\n+                        ida_bytes.patch_byte(ea + i, byte_val)\r\n+                    last_patch['is_active'] = False\r\n+                    return [PatchHistoryEntry(operation_id=last_patch['patch_id'],operation_type=\"rollback\",target_address=last_patch['address'],timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),operation_data=\"Patch rolled back successfully\",rollback_possible=False)]\r\n+                except Exception as e:\r\n+                    raise IDAError(f\"回滚失败: {str(e)}\")\r\n+        return []\r\n+    elif operation == \"clear\":\r\n+        module_data = lazy_module_manager.get_module_data('memory_patch')\r\n+        module_data['patch_history'].clear()\r\n+        return [PatchHistoryEntry(operation_id=\"clear_operation\",operation_type=\"clear_history\",target_address=\"N/A\",timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),operation_data=\"All patch history cleared\",rollback_possible=False)]\r\n+    else:\r\n+        raise IDAError(f\"不支持的操作类型: {operation}\")\r\n+\r\n+# 测试延迟初始化框架的函数\r\n+@jsonrpc\r\n+@lazy_init_module('test_module')\r\n+@idaread\r\n+def test_lazy_initialization() -> dict[str, Any]:\r\n+    \"\"\"测试延迟初始化框架功能\"\"\"\r\n+    return {\r\n+        \"message\": \"延迟初始化框架测试成功\",\r\n+        \"module_states\": lazy_module_manager.module_states,\r\n+        \"usage_stats\": lazy_module_manager.get_usage_stats()\r\n+    }\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_lazy_module_stats() -> dict[str, Any]:\r\n+    \"\"\"获取延迟初始化模块统计信息\"\"\"\r\n+    return {\r\n+        \"initialized_modules\": [name for name, state in lazy_module_manager.module_states.items() if state],\r\n+        \"usage_statistics\": lazy_module_manager.get_usage_stats(),\r\n+        \"total_modules\": len(lazy_module_manager.module_states)\r\n+    }\r\n+\r\n+@jsonrpc\r\n+def get_cache_statistics() -> dict[str, Any]:\r\n+    \"\"\"获取智能缓存系统统计信息\"\"\"\r\n+    return analysis_cache.get_stats()\r\n+\r\n+@jsonrpc  \r\n+def clear_analysis_cache() -> dict[str, str]:\r\n+    \"\"\"清空分析缓存\"\"\"\r\n+    analysis_cache.clear()\r\n+    return {\"status\": \"success\", \"message\": \"分析缓存已清空\"}\r\n+\r\n+@jsonrpc\r\n+def configure_cache_settings(\r\n+    max_size: Annotated[int, \"缓存最大条目数\"],\r\n+    max_memory_mb: Annotated[int, \"缓存最大内存使用量（MB）\"],\r\n+    ttl_seconds: Annotated[int, \"缓存条目存活时间（秒）\"]\r\n+) -> dict[str, str]:\r\n+    \"\"\"配置缓存设置\"\"\"\r\n+    global analysis_cache\r\n+    \r\n+    # 验证参数\r\n+    if max_size <= 0 or max_memory_mb <= 0 or ttl_seconds <= 0:\r\n+        raise IDAError(\"缓存参数必须为正数\")\r\n+    \r\n+    if max_memory_mb > 100:  # 限制最大内存使用\r\n+        raise IDAError(\"缓存内存限制不能超过100MB\")\r\n+    \r\n+    # 重新创建缓存实例\r\n+    analysis_cache = AnalysisCache(max_size, max_memory_mb, ttl_seconds)\r\n+    \r\n+    return {\r\n+        \"status\": \"success\", \r\n+        \"message\": f\"缓存配置已更新：最大{max_size}条目，{max_memory_mb}MB内存，{ttl_seconds}秒TTL\"\r\n+    }\r\n+\r\n+# ==================== 字符串分析增强模块 ====================\r\n+\r\n+class EncodedString(TypedDict):\r\n+    \"\"\"编码字符串结果\"\"\"\r\n+    original: str\r\n+    decoded: str\r\n+    encoding_type: str\r\n+    confidence: float\r\n+    address: str\r\n+\r\n+class LicenseString(TypedDict):\r\n+    \"\"\"许可证字符串结果\"\"\"\r\n+    string: str\r\n+    address: str\r\n+    category: str\r\n+    importance: str\r\n+    keywords_found: list[str]\r\n+\r\n+class ErrorMessage(TypedDict):\r\n+    \"\"\"错误消息分析结果\"\"\"\r\n+    message: str\r\n+    address: str\r\n+    severity: str\r\n+    category: str\r\n+    potential_cause: str\r\n+\r\n+class ResourceString(TypedDict):\r\n+    \"\"\"资源字符串结果\"\"\"\r\n+    string: str\r\n+    address: str\r\n+    resource_type: str\r\n+    context: str\r\n+    usage_hint: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=1800, cache_size_limit=50)  # 30分钟缓存\r\n+@lazy_init_module('string_analysis')\r\n+@idaread\r\n+def decrypt_encoded_strings(\r\n+    min_length: Annotated[int, \"最小字符串长度\"] = 8,\r\n+    max_count: Annotated[int, \"最大返回数量\"] = 100\r\n+) -> list[EncodedString]:\r\n+    \"\"\"解密和识别编码字符串\"\"\"\r\n+    module_data = lazy_module_manager.get_module_data('string_analysis')\r\n+    encoded_strings = []\r\n+    \r\n+    # 获取所有字符串\r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            original_str = str(item)\r\n+            if len(original_str) < min_length:\r\n+                continue\r\n+                \r\n+            # 尝试Base64解码\r\n+            if _is_base64_like(original_str, module_data['encoding_patterns']['base64']):\r\n+                try:\r\n+                    decoded = base64.b64decode(original_str + '==').decode('utf-8', errors='ignore')\r\n+                    if decoded and all(ord(c) >= 32 and ord(c) <= 126 for c in decoded[:20]):\r\n+                        encoded_strings.append(EncodedString(\r\n+                            original=original_str,\r\n+                            decoded=decoded,\r\n+                            encoding_type=\"Base64\",\r\n+                            confidence=0.9,\r\n+                            address=hex(item.ea)\r\n+                        ))\r\n+                        continue\r\n+                except (ValueError, UnicodeDecodeError):\r\n+                    pass\r\n+            \r\n+            # 尝试简单XOR解码\r\n+            xor_result = _try_xor_decode(original_str, module_data['common_xor_keys'])\r\n+            if xor_result:\r\n+                encoded_strings.append(EncodedString(\r\n+                    original=original_str,\r\n+                    decoded=xor_result['decoded'],\r\n+                    encoding_type=f\"XOR-{xor_result['key']:02X}\",\r\n+                    confidence=xor_result['confidence'],\r\n+                    address=hex(item.ea)\r\n+                ))\r\n+                continue\r\n+            \r\n+            # 尝试ROT13解码\r\n+            rot13_decoded = original_str.encode().decode('rot13', errors='ignore')\r\n+            if rot13_decoded != original_str and _is_meaningful_text(rot13_decoded):\r\n+                encoded_strings.append(EncodedString(\r\n+                    original=original_str,\r\n+                    decoded=rot13_decoded,\r\n+                    encoding_type=\"ROT13\",\r\n+                    confidence=0.7,\r\n+                    address=hex(item.ea)\r\n+                ))\r\n+            \r\n+        except Exception:\r\n+            continue\r\n+        \r\n+        if len(encoded_strings) >= max_count:\r\n+            break\r\n+    \r\n+    return sorted(encoded_strings, key=lambda x: x['confidence'], reverse=True)\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存\r\n+@lazy_init_module('string_analysis')\r\n+@idaread  \r\n+def extract_license_strings(\r\n+    include_trial: Annotated[bool, \"包含试用版相关字符串\"] = True\r\n+) -> list[LicenseString]:\r\n+    \"\"\"提取和分类许可证相关字符串\"\"\"\r\n+    module_data = lazy_module_manager.get_module_data('string_analysis')\r\n+    license_strings = []\r\n+    \r\n+    keywords = module_data['license_keywords']\r\n+    categories = module_data['string_categories']\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item).lower()\r\n+            if len(string_content) < 5:\r\n+                continue\r\n+            \r\n+            # 查找匹配的关键词\r\n+            found_keywords = [kw for kw in keywords if kw in string_content]\r\n+            if not found_keywords:\r\n+                continue\r\n+            \r\n+            # 确定类别和重要性\r\n+            category = \"license_general\"\r\n+            importance = \"medium\"\r\n+            \r\n+            if any(kw in string_content for kw in ['serial', 'key', 'activation']):\r\n+                category = \"license_key\"\r\n+                importance = \"high\"\r\n+            elif any(kw in string_content for kw in ['trial', 'demo', 'evaluation']):\r\n+                category = \"trial_version\"\r\n+                importance = \"high\" if include_trial else \"medium\"\r\n+            elif any(kw in string_content for kw in ['expired', 'invalid', 'error']):\r\n+                category = \"license_error\"\r\n+                importance = \"high\"\r\n+            \r\n+            # 根据字符串类别调整重要性\r\n+            for category_name, cat_keywords in categories.items():\r\n+                if any(kw in string_content for kw in cat_keywords):\r\n+                    if category_name == 'high_importance':\r\n+                        importance = \"high\"\r\n+                    elif category_name == 'low_importance' and importance != \"high\":\r\n+                        importance = \"low\"\r\n+            \r\n+            license_strings.append(LicenseString(\r\n+                string=str(item),\r\n+                address=hex(item.ea),\r\n+                category=category,\r\n+                importance=importance,\r\n+                keywords_found=found_keywords\r\n+            ))\r\n+            \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按重要性和地址排序\r\n+    importance_order = {\"high\": 0, \"medium\": 1, \"low\": 2}\r\n+    return sorted(license_strings, \r\n+                 key=lambda x: (importance_order[x['importance']], x['address']))\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=75)  # 40分钟缓存\r\n+@lazy_init_module('string_analysis')\r\n+@idaread\r\n+def analyze_error_messages(\r\n+    severity_filter: Annotated[Optional[str], \"严重性过滤器 (critical/high/medium/low)\"] = None\r\n+) -> list[ErrorMessage]:\r\n+    \"\"\"分析错误消息和异常信息\"\"\"\r\n+    module_data = lazy_module_manager.get_module_data('string_analysis')\r\n+    error_messages = []\r\n+    \r\n+    error_patterns = module_data['error_patterns']\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item).lower()\r\n+            if len(string_content) < 6:\r\n+                continue\r\n+            \r\n+            # 查找错误模式\r\n+            matched_patterns = [pattern for pattern in error_patterns if pattern in string_content]\r\n+            if not matched_patterns:\r\n+                continue\r\n+            \r\n+            # 确定严重性\r\n+            severity = \"medium\"\r\n+            if any(pattern in string_content for pattern in ['fail', 'error', 'exception', 'corrupt']):\r\n+                severity = \"high\"\r\n+            elif any(pattern in string_content for pattern in ['access denied', 'permission', 'timeout']):\r\n+                severity = \"critical\"\r\n+            elif any(pattern in string_content for pattern in ['invalid', 'wrong', 'bad']):\r\n+                severity = \"medium\"\r\n+            else:\r\n+                severity = \"low\"\r\n+            \r\n+            # 应用过滤器\r\n+            if severity_filter and severity != severity_filter:\r\n+                continue\r\n+            \r\n+            # 确定错误类别和潜在原因\r\n+            category = \"general_error\"\r\n+            potential_cause = \"程序逻辑错误\"\r\n+            \r\n+            if any(word in string_content for word in ['file', 'not found', 'missing']):\r\n+                category = \"file_error\"\r\n+                potential_cause = \"文件访问或路径问题\"\r\n+            elif any(word in string_content for word in ['memory', 'overflow', 'allocation']):\r\n+                category = \"memory_error\"\r\n+                potential_cause = \"内存管理问题\"\r\n+            elif any(word in string_content for word in ['network', 'connection', 'timeout']):\r\n+                category = \"network_error\"\r\n+                potential_cause = \"网络连接问题\"\r\n+            elif any(word in string_content for word in ['permission', 'access', 'denied']):\r\n+                category = \"permission_error\"\r\n+                potential_cause = \"权限或安全限制\"\r\n+            \r\n+            error_messages.append(ErrorMessage(\r\n+                message=str(item),\r\n+                address=hex(item.ea),\r\n+                severity=severity,\r\n+                category=category,\r\n+                potential_cause=potential_cause\r\n+            ))\r\n+            \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按严重性排序\r\n+    severity_order = {\"critical\": 0, \"high\": 1, \"medium\": 2, \"low\": 3}\r\n+    return sorted(error_messages, key=lambda x: severity_order[x['severity']])\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=100)  # 1小时缓存\r\n+@lazy_init_module('string_analysis')\r\n+@idaread\r\n+def find_resource_strings(\r\n+    resource_type: Annotated[Optional[str], \"资源类型过滤器\"] = None\r\n+) -> list[ResourceString]:\r\n+    \"\"\"查找和分类GUI资源字符串\"\"\"\r\n+    module_data = lazy_module_manager.get_module_data('string_analysis')\r\n+    resource_strings = []\r\n+    \r\n+    resource_indicators = module_data['resource_indicators']\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            string_lower = string_content.lower()\r\n+            \r\n+            if len(string_content) < 4:\r\n+                continue\r\n+            \r\n+            # 检查是否包含资源指示符\r\n+            matched_indicators = [indicator for indicator in resource_indicators \r\n+                                if indicator in string_lower]\r\n+            \r\n+            # 检查字符串特征\r\n+            detected_type = \"unknown\"\r\n+            context = \"general\"\r\n+            usage_hint = \"可能用于用户界面显示\"\r\n+            \r\n+            # 识别GUI元素\r\n+            if any(word in string_lower for word in ['button', 'menu', 'dialog']):\r\n+                detected_type = \"gui_element\"\r\n+                context = \"用户界面控件\"\r\n+                usage_hint = \"按钮、菜单或对话框文本\"\r\n+            elif any(word in string_lower for word in ['error', 'warning', 'info']):\r\n+                detected_type = \"message\"\r\n+                context = \"消息提示\"\r\n+                usage_hint = \"用户提示或错误消息\"\r\n+            elif any(word in string_lower for word in ['title', 'caption']):\r\n+                detected_type = \"title\"\r\n+                context = \"标题文本\"\r\n+                usage_hint = \"窗口或控件标题\"\r\n+            elif any(word in string_lower for word in ['version', 'copyright', '©']):\r\n+                detected_type = \"version_info\"\r\n+                context = \"版本信息\"\r\n+                usage_hint = \"程序版本或版权信息\"\r\n+            elif re.match(r'^[A-Z][a-z]+(\\s+[A-Z][a-z]+)*$', string_content):\r\n+                detected_type = \"label\"\r\n+                context = \"标签文本\"\r\n+                usage_hint = \"界面标签或描述文本\"\r\n+            elif matched_indicators:\r\n+                detected_type = \"resource_reference\"\r\n+                context = \"资源引用\"\r\n+                usage_hint = f\"可能与{matched_indicators[0]}相关的资源\"\r\n+            \r\n+            # 应用类型过滤器\r\n+            if resource_type and detected_type != resource_type:\r\n+                continue\r\n+            \r\n+            # 只保留可能的资源字符串\r\n+            if detected_type != \"unknown\" or matched_indicators:\r\n+                resource_strings.append(ResourceString(\r\n+                    string=string_content,\r\n+                    address=hex(item.ea),\r\n+                    resource_type=detected_type,\r\n+                    context=context,\r\n+                    usage_hint=usage_hint\r\n+                ))\r\n+            \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按类型和地址排序\r\n+    type_order = {\r\n+        \"version_info\": 0, \"title\": 1, \"gui_element\": 2, \r\n+        \"message\": 3, \"label\": 4, \"resource_reference\": 5, \"unknown\": 6\r\n+    }\r\n+    return sorted(resource_strings, \r\n+                 key=lambda x: (type_order.get(x['resource_type'], 6), x['address']))\r\n+\r\n+# ==================== 自动化工作流引擎 ====================\r\n+\r\n+class ProtectionType(TypedDict):\r\n+    \"\"\"保护类型检测结果\"\"\"\r\n+    protection_name: str\r\n+    confidence: float\r\n+    detected_features: list[str]\r\n+    analysis_strategy: str\r\n+    priority: str\r\n+\r\n+class AnalysisStrategy(TypedDict):\r\n+    \"\"\"分析策略\"\"\"\r\n+    strategy_name: str\r\n+    target_modules: list[str]\r\n+    execution_order: list[str]\r\n+    parameters: dict[str, Any]\r\n+    estimated_time: str\r\n+    success_probability: float\r\n+\r\n+class BatchTask(TypedDict):\r\n+    \"\"\"批处理任务\"\"\"\r\n+    task_id: str\r\n+    task_type: str\r\n+    target: str\r\n+    status: str\r\n+    progress: float\r\n+    results: Optional[dict[str, Any]]\r\n+    start_time: str\r\n+    end_time: Optional[str]\r\n+\r\n+class CrackReport(TypedDict):\r\n+    \"\"\"破解分析报告\"\"\"\r\n+    report_id: str\r\n+    analysis_summary: str\r\n+    protection_analysis: dict[str, Any]\r\n+    vulnerability_points: list[dict[str, Any]]\r\n+    recommended_approach: str\r\n+    success_indicators: list[str]\r\n+    risk_assessment: str\r\n+    generated_time: str\r\n+\r\n+class WorkflowEngine:\r\n+    \"\"\"智能工作流引擎 - 自动化破解分析流程管理\"\"\"\r\n+    \r\n+    def __init__(self):\r\n+        self.running_tasks: dict[str, BatchTask] = {}\r\n+        self.completed_tasks: list[BatchTask] = []\r\n+        self.strategy_cache: dict[str, AnalysisStrategy] = {}\r\n+        \r\n+    def _generate_task_id(self) -> str:\r\n+        \"\"\"生成唯一任务ID\"\"\"\r\n+        import time\r\n+        return f\"task_{int(time.time())}_{len(self.running_tasks)}\"\r\n+    \r\n+    def _analyze_protection_patterns(self, analysis_area: Optional[int] = None) -> list[str]:\r\n+        \"\"\"分析保护模式特征\"\"\"\r\n+        detected_features = []\r\n+        \r\n+        if analysis_area:\r\n+            start_ea = analysis_area\r\n+            end_ea = start_ea + 0x10000  # 64KB范围\r\n+        else:\r\n+            start_ea = idaapi.get_imagebase()\r\n+            end_ea = start_ea + 0x50000  # 320KB范围\r\n+        \r\n+        # 检测常见保护特征\r\n+        protection_signatures = {\r\n+            'VirtualProtect': 'dynamic_protection',\r\n+            'IsDebuggerPresent': 'anti_debug',\r\n+            'GetTickCount': 'timing_check',\r\n+            'CheckRemoteDebuggerPresent': 'remote_debug_detection',\r\n+            'OutputDebugString': 'debug_output_check',\r\n+            '.rsrc': 'resource_protection',\r\n+            'GetModuleHandle': 'module_detection',\r\n+            'CreateToolhelp32Snapshot': 'process_enumeration'\r\n+        }\r\n+        \r\n+        for signature_str, feature_name in protection_signatures.items():\r\n+            # 使用idc.find_text进行字符串搜索，确保字符串编码正确\r\n+            try:\r\n+                signature_bytes = signature_str.encode('utf-8') if isinstance(signature_str, str) else signature_str\r\n+                found_addr = idc.find_text(start_ea, idaapi.SEARCH_DOWN | idaapi.SEARCH_CASE, 0, 0, signature_bytes)\r\n+                if found_addr != idaapi.BADADDR:\r\n+                    detected_features.append(feature_name)\r\n+            except (UnicodeEncodeError, TypeError):\r\n+                # 如果字符串编码失败，跳过此签名\r\n+                continue\r\n+        \r\n+        return detected_features\r\n+    \r\n+    def _calculate_strategy_confidence(self, protection_features: list[str]) -> float:\r\n+        \"\"\"计算策略可信度\"\"\"\r\n+        if not protection_features:\r\n+            return 0.3\r\n+        \r\n+        confidence_map = {\r\n+            'anti_debug': 0.8,\r\n+            'dynamic_protection': 0.9,\r\n+            'timing_check': 0.6,\r\n+            'resource_protection': 0.5,\r\n+            'process_enumeration': 0.7\r\n+        }\r\n+        \r\n+        total_confidence = sum(confidence_map.get(feature, 0.4) for feature in protection_features)\r\n+        return min(total_confidence / len(protection_features), 0.95)\r\n+\r\n+# 创建全局工作流引擎实例\r\n+workflow_engine = WorkflowEngine()\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=1800, cache_size_limit=30)  # 30分钟缓存\r\n+@idaread\r\n+def detect_protection_type(\r\n+    analysis_area: Annotated[Optional[str], \"分析区域地址（可选）\"] = None\r\n+) -> list[ProtectionType]:\r\n+    \"\"\"检测程序保护类型和特征\"\"\"\r\n+    global workflow_engine\r\n+    \r\n+    area_ea = None\r\n+    if analysis_area:\r\n+        area_ea = parse_address(analysis_area)\r\n+    \r\n+    detected_features = workflow_engine._analyze_protection_patterns(area_ea)\r\n+    protection_types = []\r\n+    \r\n+    # 基于检测到的特征推断保护类型\r\n+    if any(feature in detected_features for feature in ['anti_debug', 'timing_check', 'remote_debug_detection']):\r\n+        confidence = workflow_engine._calculate_strategy_confidence(['anti_debug', 'timing_check'])\r\n+        protection_types.append(ProtectionType(\r\n+            protection_name=\"反调试保护\",\r\n+            confidence=confidence,\r\n+            detected_features=[f for f in detected_features if 'debug' in f or 'timing' in f],\r\n+            analysis_strategy=\"anti_debug_bypass\",\r\n+            priority=\"high\"\r\n+        ))\r\n+    \r\n+    if 'dynamic_protection' in detected_features:\r\n+        protection_types.append(ProtectionType(\r\n+            protection_name=\"动态代码保护\",\r\n+            confidence=0.85,\r\n+            detected_features=['dynamic_protection'],\r\n+            analysis_strategy=\"memory_analysis\", \r\n+            priority=\"high\"\r\n+        ))\r\n+    \r\n+    if 'resource_protection' in detected_features:\r\n+        protection_types.append(ProtectionType(\r\n+            protection_name=\"资源加密保护\",\r\n+            confidence=0.6,\r\n+            detected_features=['resource_protection'],\r\n+            analysis_strategy=\"resource_extraction\",\r\n+            priority=\"medium\"\r\n+        ))\r\n+    \r\n+    if 'process_enumeration' in detected_features:\r\n+        protection_types.append(ProtectionType(\r\n+            protection_name=\"进程检测保护\",\r\n+            confidence=0.7,\r\n+            detected_features=['process_enumeration'],\r\n+            analysis_strategy=\"process_hiding\",\r\n+            priority=\"medium\"\r\n+        ))\r\n+    \r\n+    # 如果没有检测到明显保护，提供通用分析策略\r\n+    if not protection_types:\r\n+        protection_types.append(ProtectionType(\r\n+            protection_name=\"轻度保护或无保护\",\r\n+            confidence=0.4,\r\n+            detected_features=detected_features if detected_features else [\"basic_analysis\"],\r\n+            analysis_strategy=\"standard_analysis\",\r\n+            priority=\"low\"\r\n+        ))\r\n+    \r\n+    return sorted(protection_types, key=lambda x: x['confidence'], reverse=True)\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def generate_analysis_strategy(\r\n+    protection_info: Annotated[str, \"保护信息JSON字符串\"]\r\n+) -> AnalysisStrategy:\r\n+    \"\"\"根据保护类型生成分析策略\"\"\"\r\n+    global workflow_engine\r\n+    \r\n+    try:\r\n+        protection_data = json.loads(protection_info)\r\n+        protection_name = protection_data.get('protection_name', 'unknown')\r\n+        detected_features = protection_data.get('detected_features', [])\r\n+    except (json.JSONDecodeError, KeyError):\r\n+        protection_name = 'unknown'\r\n+        detected_features = []\r\n+    \r\n+    # 检查策略缓存\r\n+    cache_key = f\"{protection_name}_{hash(tuple(detected_features))}\"\r\n+    if cache_key in workflow_engine.strategy_cache:\r\n+        return workflow_engine.strategy_cache[cache_key]\r\n+    \r\n+    # 根据保护类型生成策略\r\n+    if \"反调试保护\" in protection_name:\r\n+        strategy = AnalysisStrategy(\r\n+            strategy_name=\"反调试绕过分析流程\",\r\n+            target_modules=[\"anti_debug\", \"control_flow\", \"memory_patch\"],\r\n+            execution_order=[\r\n+                \"检测反调试技术\",\r\n+                \"生成绕过策略\", \r\n+                \"应用内存补丁\",\r\n+                \"验证绕过效果\"\r\n+            ],\r\n+            parameters={\r\n+                \"detection_depth\": \"comprehensive\",\r\n+                \"bypass_method\": \"intelligent_patching\",\r\n+                \"verification_mode\": \"runtime_testing\"\r\n+            },\r\n+            estimated_time=\"15-30分钟\",\r\n+            success_probability=0.85\r\n+        )\r\n+    \r\n+    elif \"动态代码保护\" in protection_name:\r\n+        strategy = AnalysisStrategy(\r\n+            strategy_name=\"动态代码分析流程\",\r\n+            target_modules=[\"crypto\", \"memory_patch\", \"control_flow\"],\r\n+            execution_order=[\r\n+                \"识别加密算法\",\r\n+                \"追踪解密流程\",\r\n+                \"定位密钥位置\",\r\n+                \"分析控制流\",\r\n+                \"应用解密补丁\"\r\n+            ],\r\n+            parameters={\r\n+                \"crypto_analysis\": \"deep_scan\",\r\n+                \"memory_tracing\": \"enabled\",\r\n+                \"flow_analysis\": \"comprehensive\"\r\n+            },\r\n+            estimated_time=\"30-60分钟\", \r\n+            success_probability=0.7\r\n+        )\r\n+    \r\n+    elif \"资源加密保护\" in protection_name:\r\n+        strategy = AnalysisStrategy(\r\n+            strategy_name=\"资源解密分析流程\",\r\n+            target_modules=[\"string_analysis\", \"crypto\", \"license\"],\r\n+            execution_order=[\r\n+                \"扫描加密资源\",\r\n+                \"分析解密算法\",\r\n+                \"提取资源数据\",\r\n+                \"重构原始资源\"\r\n+            ],\r\n+            parameters={\r\n+                \"resource_scanning\": \"full_binary\",\r\n+                \"decryption_mode\": \"automatic\",\r\n+                \"extraction_format\": \"original\"\r\n+            },\r\n+            estimated_time=\"20-40分钟\",\r\n+            success_probability=0.75\r\n+        )\r\n+    \r\n+    else:\r\n+        # 默认通用策略\r\n+        strategy = AnalysisStrategy(\r\n+            strategy_name=\"标准逆向分析流程\",\r\n+            target_modules=[\"control_flow\", \"string_analysis\", \"license\"],\r\n+            execution_order=[\r\n+                \"分析程序结构\",\r\n+                \"识别关键函数\",\r\n+                \"分析字符串信息\",\r\n+                \"检查许可证逻辑\"\r\n+            ],\r\n+            parameters={\r\n+                \"analysis_depth\": \"standard\",\r\n+                \"function_detection\": \"automatic\",\r\n+                \"string_analysis\": \"comprehensive\"\r\n+            },\r\n+            estimated_time=\"10-20分钟\",\r\n+            success_probability=0.9\r\n+        )\r\n+    \r\n+    # 缓存策略\r\n+    workflow_engine.strategy_cache[cache_key] = strategy\r\n+    return strategy\r\n+\r\n+@jsonrpc\r\n+@idaread  \r\n+def execute_batch_analysis(\r\n+    task_list: Annotated[str, \"任务列表JSON字符串\"],\r\n+    execution_mode: Annotated[str, \"执行模式：sequential/parallel\"] = \"sequential\"\r\n+) -> list[BatchTask]:\r\n+    \"\"\"执行批量分析任务\"\"\"\r\n+    global workflow_engine\r\n+    import time\r\n+    import json\r\n+    \r\n+    try:\r\n+        tasks_data = json.loads(task_list)\r\n+        if not isinstance(tasks_data, list):\r\n+            raise ValueError(\"任务列表必须是数组格式\")\r\n+    except (json.JSONDecodeError, ValueError) as e:\r\n+        raise IDAError(f\"任务列表格式错误: {str(e)}\")\r\n+    \r\n+    batch_tasks = []\r\n+    current_time = time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+    \r\n+    for task_data in tasks_data:\r\n+        task_id = workflow_engine._generate_task_id()\r\n+        task_type = task_data.get('type', 'unknown')\r\n+        target = task_data.get('target', 'unknown')\r\n+        \r\n+        # 创建批处理任务\r\n+        batch_task = BatchTask(\r\n+            task_id=task_id,\r\n+            task_type=task_type,\r\n+            target=target,\r\n+            status=\"queued\",\r\n+            progress=0.0,\r\n+            results=None,\r\n+            start_time=current_time,\r\n+            end_time=None\r\n+        )\r\n+        \r\n+        # 模拟任务执行（实际实现中会调用相应的分析函数）\r\n+        try:\r\n+            batch_task['status'] = \"running\"\r\n+            batch_task['progress'] = 0.3\r\n+            \r\n+            # 根据任务类型执行相应的分析\r\n+            if task_type == \"protection_detection\":\r\n+                results = detect_protection_type(target if target != 'unknown' else None)\r\n+                batch_task['results'] = {\"protection_types\": results}\r\n+                \r\n+            elif task_type == \"anti_debug_scan\":\r\n+                results = detect_anti_debug_techniques(target if target != 'unknown' else None)\r\n+                batch_task['results'] = {\"anti_debug_detections\": results}\r\n+                \r\n+            elif task_type == \"string_analysis\":\r\n+                encoded_results = decrypt_encoded_strings()\r\n+                license_results = extract_license_strings()\r\n+                batch_task['results'] = {\r\n+                    \"encoded_strings\": encoded_results[:5],  # 限制返回数量\r\n+                    \"license_strings\": license_results[:5]\r\n+                }\r\n+                \r\n+            elif task_type == \"crypto_analysis\":\r\n+                crypto_results = identify_crypto_algorithms(target if target != 'unknown' else None)\r\n+                batch_task['results'] = {\"crypto_algorithms\": crypto_results}\r\n+                \r\n+            else:\r\n+                # 默认执行基本分析\r\n+                batch_task['results'] = {\"message\": f\"已完成{task_type}类型的基本分析\"}\r\n+            \r\n+            batch_task['status'] = \"completed\"\r\n+            batch_task['progress'] = 1.0\r\n+            batch_task['end_time'] = time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+            \r\n+        except Exception as e:\r\n+            batch_task['status'] = \"failed\"\r\n+            batch_task['results'] = {\"error\": str(e)}\r\n+            batch_task['end_time'] = time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+        \r\n+        # 将任务添加到列表\r\n+        batch_tasks.append(batch_task)\r\n+        workflow_engine.running_tasks[task_id] = batch_task\r\n+        \r\n+        # 如果是sequential模式，任务间稍作延迟\r\n+        if execution_mode == \"sequential\" and len(batch_tasks) < len(tasks_data):\r\n+            time.sleep(0.1)  # 100ms延迟\r\n+    \r\n+    # 将完成的任务移到完成列表\r\n+    for task in batch_tasks:\r\n+        if task['status'] in ['completed', 'failed']:\r\n+            workflow_engine.completed_tasks.append(task)\r\n+            workflow_engine.running_tasks.pop(task['task_id'], None)\r\n+    \r\n+    return batch_tasks\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def generate_crack_report(\r\n+    analysis_results: Annotated[str, \"分析结果JSON字符串\"],\r\n+    report_type: Annotated[str, \"报告类型：summary/detailed\"] = \"summary\"\r\n+) -> CrackReport:\r\n+    \"\"\"生成破解分析报告\"\"\"\r\n+    import time\r\n+    import json\r\n+    \r\n+    try:\r\n+        results_data = json.loads(analysis_results)\r\n+    except json.JSONDecodeError:\r\n+        raise IDAError(\"分析结果格式错误\")\r\n+    \r\n+    report_id = f\"report_{int(time.time())}\"\r\n+    generated_time = time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+    \r\n+    # 汇总分析结果\r\n+    protection_analysis = {}\r\n+    vulnerability_points = []\r\n+    \r\n+    # 处理保护类型检测结果\r\n+    if 'protection_types' in results_data:\r\n+        protection_analysis['detected_protections'] = results_data['protection_types']\r\n+        high_confidence_protections = [\r\n+            p for p in results_data['protection_types'] \r\n+            if p.get('confidence', 0) > 0.7\r\n+        ]\r\n+        protection_analysis['high_confidence_count'] = len(high_confidence_protections)\r\n+    \r\n+    # 处理反调试检测结果\r\n+    if 'anti_debug_detections' in results_data:\r\n+        anti_debug_data = results_data['anti_debug_detections']\r\n+        for detection in anti_debug_data:\r\n+            vulnerability_points.append({\r\n+                \"type\": \"反调试检测点\",\r\n+                \"address\": detection.get('address', 'unknown'),\r\n+                \"technique\": detection.get('technique', 'unknown'),\r\n+                \"bypass_suggestion\": detection.get('bypass_suggestion', '需要进一步分析'),\r\n+                \"risk_level\": detection.get('risk_level', 'medium')\r\n+            })\r\n+    \r\n+    # 处理加密算法结果\r\n+    if 'crypto_algorithms' in results_data:\r\n+        crypto_data = results_data['crypto_algorithms']\r\n+        protection_analysis['crypto_algorithms'] = len(crypto_data)\r\n+        for crypto in crypto_data:\r\n+            vulnerability_points.append({\r\n+                \"type\": \"加密算法\",\r\n+                \"address\": crypto.get('address', 'unknown'),\r\n+                \"algorithm\": crypto.get('algorithm', 'unknown'),\r\n+                \"confidence\": crypto.get('confidence', 0),\r\n+                \"risk_level\": \"high\" if crypto.get('confidence', 0) > 0.8 else \"medium\"\r\n+            })\r\n+    \r\n+    # 生成分析摘要\r\n+    total_vulnerabilities = len(vulnerability_points)\r\n+    high_risk_count = len([v for v in vulnerability_points if v.get('risk_level') == 'high'])\r\n+    \r\n+    if report_type == \"detailed\":\r\n+        analysis_summary = f\"\"\"\r\n+详细破解分析报告\r\n+=================\r\n+\r\n+检测到 {total_vulnerabilities} 个分析点，其中 {high_risk_count} 个高风险点。\r\n+\r\n+保护机制分析：\r\n+- 检测到的保护类型：{protection_analysis.get('detected_protections', [])}\r\n+- 高可信度保护：{protection_analysis.get('high_confidence_count', 0)} 个\r\n+- 发现的加密算法：{protection_analysis.get('crypto_algorithms', 0)} 个\r\n+\r\n+漏洞点详情：\r\n+{chr(10).join([f\"- {v['type']} at {v['address']}: {v.get('technique', 'N/A')}\" for v in vulnerability_points[:10]])}\r\n+        \"\"\".strip()\r\n+    else:\r\n+        analysis_summary = f\"检测到 {total_vulnerabilities} 个分析点，{high_risk_count} 个高风险。保护类型：{protection_analysis.get('high_confidence_count', 0)} 个高可信度保护。\"\r\n+    \r\n+    # 生成推荐方法\r\n+    if high_risk_count > 5:\r\n+        recommended_approach = \"建议采用动态分析结合静态补丁的综合方法，优先处理高风险点\"\r\n+    elif high_risk_count > 0:\r\n+        recommended_approach = \"可采用静态分析为主的方法，重点关注已识别的高风险点\"\r\n+    else:\r\n+        recommended_approach = \"程序保护较弱，可直接进行静态分析和简单补丁\"\r\n+    \r\n+    # 生成成功指标\r\n+    success_indicators = [\r\n+        \"成功绕过反调试检测\",\r\n+        \"正确识别并处理加密算法\",\r\n+        \"定位关键验证函数\",\r\n+        \"成功修改程序行为\"\r\n+    ]\r\n+    \r\n+    # 风险评估\r\n+    if high_risk_count > 8:\r\n+        risk_assessment = \"高风险：保护机制复杂，需要高级破解技术\"\r\n+    elif high_risk_count > 3:\r\n+        risk_assessment = \"中等风险：有一定保护，需要仔细分析\"\r\n+    else:\r\n+        risk_assessment = \"低风险：保护较弱，破解难度不大\"\r\n+    \r\n+    return CrackReport(\r\n+        report_id=report_id,\r\n+        analysis_summary=analysis_summary,\r\n+        protection_analysis=protection_analysis,\r\n+        vulnerability_points=vulnerability_points[:15],  # 限制返回数量\r\n+        recommended_approach=recommended_approach,\r\n+        success_indicators=success_indicators,\r\n+        risk_assessment=risk_assessment,\r\n+        generated_time=generated_time\r\n+    )\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def get_workflow_status() -> dict[str, Any]:\r\n+    \"\"\"获取工作流引擎状态\"\"\"\r\n+    global workflow_engine\r\n+    \r\n+    return {\r\n+        \"running_tasks\": len(workflow_engine.running_tasks),\r\n+        \"completed_tasks\": len(workflow_engine.completed_tasks),\r\n+        \"cached_strategies\": len(workflow_engine.strategy_cache),\r\n+        \"task_details\": {\r\n+            \"running\": list(workflow_engine.running_tasks.keys()),\r\n+            \"completed_recent\": [\r\n+                {\"id\": task['task_id'], \"type\": task['task_type'], \"status\": task['status']}\r\n+                for task in workflow_engine.completed_tasks[-5:]  # 最近5个完成的任务\r\n+            ]\r\n+        }\r\n+    }\r\n+\r\n+# 辅助函数\r\n+def _is_base64_like(s: str, base64_chars: str) -> bool:\r\n+    \"\"\"检查字符串是否像Base64编码\"\"\"\r\n+    if len(s) < 8 or len(s) % 4 != 0:\r\n+        return False\r\n+    return all(c in base64_chars for c in s)\r\n+\r\n+def _try_xor_decode(s: str, xor_keys: list[int]) -> Optional[dict]:\r\n+    \"\"\"尝试XOR解码\"\"\"\r\n+    for key in xor_keys:\r\n+        try:\r\n+            decoded_bytes = bytes(ord(c) ^ key for c in s[:50])  # 只测试前50字符\r\n+            decoded = decoded_bytes.decode('utf-8', errors='ignore')\r\n+            if decoded and _is_meaningful_text(decoded):\r\n+                return {'decoded': decoded, 'key': key, 'confidence': 0.8}\r\n+        except (ValueError, UnicodeDecodeError):\r\n+            continue\r\n+    return None\r\n+\r\n+def _is_meaningful_text(s: str) -> bool:\r\n+    \"\"\"检查文本是否有意义\"\"\"\r\n+    if len(s) < 4:\r\n+        return False\r\n+    \r\n+    # 检查可打印字符比例\r\n+    printable_ratio = sum(1 for c in s if c.isprintable()) / len(s)\r\n+    if printable_ratio < 0.8:\r\n+        return False\r\n+    \r\n+    # 检查是否包含常见英文单词\r\n+    common_words = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'man', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'its', 'let', 'put', 'say', 'she', 'too', 'use']\r\n+    s_lower = s.lower()\r\n+    word_matches = sum(1 for word in common_words if word in s_lower)\r\n+    \r\n+    return word_matches > 0 or len(s) > 20  # 有常见单词或较长字符串\r\n+\r\n+# ==================== Web应用逆向分析模块 ====================\r\n+\r\n+class JavaScriptPattern(TypedDict):\r\n+    \"\"\"JavaScript模式匹配结果\"\"\"\r\n+    pattern_type: str\r\n+    location: str\r\n+    code_snippet: str\r\n+    security_level: str\r\n+    description: str\r\n+    exploitation_hint: str\r\n+\r\n+class APIEndpoint(TypedDict):\r\n+    \"\"\"API端点发现结果\"\"\"\r\n+    endpoint_url: str\r\n+    method: str\r\n+    parameters: list[str]\r\n+    location: str\r\n+    authentication_required: bool\r\n+    potential_vulnerability: str\r\n+\r\n+class WebResource(TypedDict):\r\n+    \"\"\"Web资源分析结果\"\"\"\r\n+    resource_type: str\r\n+    location: str\r\n+    size_bytes: int\r\n+    encoding: str\r\n+    content_preview: str\r\n+    security_impact: str\r\n+\r\n+class ClientSideValidation(TypedDict):\r\n+    \"\"\"客户端验证分析结果\"\"\"\r\n+    validation_type: str\r\n+    location: str\r\n+    validation_logic: str\r\n+    bypass_difficulty: str\r\n+    bypass_suggestion: str\r\n+\r\n+class WebCryptoPattern(TypedDict):\r\n+    \"\"\"Web加密模式结果\"\"\"\r\n+    crypto_type: str\r\n+    implementation: str\r\n+    location: str\r\n+    strength_assessment: str\r\n+    weakness_description: str\r\n+    attack_vector: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=40)\r\n+@idaread\r\n+def analyze_javascript_patterns() -> list[JavaScriptPattern]:\r\n+    \"\"\"分析JavaScript代码模式和安全问题\"\"\"\r\n+    patterns = []\r\n+    \r\n+    # JavaScript相关的字符串模式\r\n+    js_security_patterns = {\r\n+        'eval': ('代码注入风险', 'high', '避免使用eval，可能导致代码注入'),\r\n+        'document.write': ('XSS风险', 'medium', '可能存在XSS漏洞，检查输入过滤'),\r\n+        'innerHTML': ('DOM操作风险', 'medium', '直接DOM操作可能导致XSS'),\r\n+        'setTimeout': ('代码执行风险', 'medium', '动态代码执行，检查参数来源'),\r\n+        'setInterval': ('代码执行风险', 'medium', '周期性代码执行，检查参数'),\r\n+        'new Function': ('动态函数风险', 'high', '动态函数创建，可能被恶意利用'),\r\n+        'localStorage': ('数据存储', 'low', '本地存储数据，检查敏感信息'),\r\n+        'sessionStorage': ('会话存储', 'low', '会话数据存储，注意数据泄露'),\r\n+        'XMLHttpRequest': ('AJAX请求', 'medium', '异步请求，检查端点安全'),\r\n+        'fetch': ('现代请求', 'medium', 'Fetch API请求，验证目标安全'),\r\n+        'postMessage': ('跨窗口通信', 'high', '跨域通信，可能存在安全风险'),\r\n+        'btoa': ('Base64编码', 'low', 'Base64编码，可能用于数据隐藏'),\r\n+        'atob': ('Base64解码', 'low', 'Base64解码，检查解码数据'),\r\n+        'crypto.subtle': ('Web Crypto API', 'medium', '加密操作，分析加密实现'),\r\n+        'WebAssembly': ('WASM', 'high', 'WebAssembly代码，需要专门分析')\r\n+    }\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            string_lower = string_content.lower()\r\n+            \r\n+            # 检查JavaScript模式\r\n+            for pattern, (desc, level, hint) in js_security_patterns.items():\r\n+                if pattern.lower() in string_lower:\r\n+                    # 提取代码片段上下文\r\n+                    snippet = string_content\r\n+                    if len(snippet) > 100:\r\n+                        snippet = snippet[:97] + \"...\"\r\n+                    \r\n+                    patterns.append(JavaScriptPattern(\r\n+                        pattern_type=pattern,\r\n+                        location=hex(item.ea),\r\n+                        code_snippet=snippet,\r\n+                        security_level=level,\r\n+                        description=desc,\r\n+                        exploitation_hint=hint\r\n+                    ))\r\n+                    break  # 每个字符串只匹配一个模式\r\n+                    \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按安全级别排序\r\n+    level_order = {\"high\": 0, \"medium\": 1, \"low\": 2}\r\n+    return sorted(patterns, key=lambda x: level_order[x['security_level']])[:15]\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=1800, cache_size_limit=30)\r\n+@idaread\r\n+def discover_api_endpoints() -> list[APIEndpoint]:\r\n+    \"\"\"发现API端点和网络请求\"\"\"\r\n+    endpoints = []\r\n+    \r\n+    # API端点模式\r\n+    api_patterns = [\r\n+        (r'/api/', 'REST API'),\r\n+        (r'/v[0-9]+/', '版本化API'),\r\n+        (r'\\.json', 'JSON端点'),\r\n+        (r'\\.xml', 'XML端点'),\r\n+        (r'/graphql', 'GraphQL端点'),\r\n+        (r'/oauth/', 'OAuth端点'),\r\n+        (r'/auth/', '认证端点'),\r\n+        (r'/login', '登录端点'),\r\n+        (r'/user/', '用户API'),\r\n+        (r'/admin/', '管理API')\r\n+    ]\r\n+    \r\n+    # HTTP方法模式\r\n+    http_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD']\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            \r\n+            # 检查URL模式\r\n+            import re\r\n+            url_pattern = re.compile(r'https?://[^\\s<>\"\\']+|/[a-zA-Z0-9/_.-]+')\r\n+            urls = url_pattern.findall(string_content)\r\n+            \r\n+            for url in urls:\r\n+                if len(url) < 5:  # 过滤太短的匹配\r\n+                    continue\r\n+                \r\n+                # 分析URL特征\r\n+                endpoint_type = \"未知端点\"\r\n+                for pattern, desc in api_patterns:\r\n+                    if re.search(pattern, url, re.IGNORECASE):\r\n+                        endpoint_type = desc\r\n+                        break\r\n+                \r\n+                # 检查是否需要认证\r\n+                auth_required = any(auth_indicator in url.lower() \r\n+                                  for auth_indicator in ['auth', 'login', 'token', 'admin'])\r\n+                \r\n+                # 提取可能的参数\r\n+                param_pattern = re.compile(r'[?&]([^=&]+)=')\r\n+                parameters = param_pattern.findall(url)\r\n+                \r\n+                # 评估潜在漏洞\r\n+                vulnerability = \"无明显漏洞\"\r\n+                if 'admin' in url.lower():\r\n+                    vulnerability = \"管理接口暴露\"\r\n+                elif len(parameters) > 3:\r\n+                    vulnerability = \"参数较多，可能存在注入风险\"\r\n+                elif not auth_required and '/user/' in url.lower():\r\n+                    vulnerability = \"用户接口无认证\"\r\n+                \r\n+                endpoints.append(APIEndpoint(\r\n+                    endpoint_url=url,\r\n+                    method=\"GET\",  # 默认方法，实际中需要更复杂的分析\r\n+                    parameters=parameters,\r\n+                    location=hex(item.ea),\r\n+                    authentication_required=auth_required,\r\n+                    potential_vulnerability=vulnerability\r\n+                ))\r\n+                \r\n+            # 检查HTTP方法\r\n+            for method in http_methods:\r\n+                if method in string_content:\r\n+                    # 尝试在附近找到URL\r\n+                    context_start = max(0, string_content.find(method) - 50)\r\n+                    context_end = min(len(string_content), string_content.find(method) + 100)\r\n+                    context = string_content[context_start:context_end]\r\n+                    \r\n+                    # 在上下文中查找URL\r\n+                    context_urls = url_pattern.findall(context)\r\n+                    for url in context_urls:\r\n+                        endpoints.append(APIEndpoint(\r\n+                            endpoint_url=url,\r\n+                            method=method,\r\n+                            parameters=[],\r\n+                            location=hex(item.ea),\r\n+                            authentication_required=False,\r\n+                            potential_vulnerability=\"需要进一步分析\"\r\n+                        ))\r\n+                        \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 去重并限制返回数量\r\n+    seen_urls = set()\r\n+    unique_endpoints = []\r\n+    for endpoint in endpoints:\r\n+        if endpoint['endpoint_url'] not in seen_urls:\r\n+            seen_urls.add(endpoint['endpoint_url'])\r\n+            unique_endpoints.append(endpoint)\r\n+            if len(unique_endpoints) >= 12:\r\n+                break\r\n+    \r\n+    return unique_endpoints\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=50)\r\n+@idaread\r\n+def extract_web_resources() -> list[WebResource]:\r\n+    \"\"\"提取Web资源文件分析\"\"\"\r\n+    resources = []\r\n+    \r\n+    # Web资源文件扩展名和类型\r\n+    resource_types = {\r\n+        '.js': 'JavaScript',\r\n+        '.css': 'CSS样式表',\r\n+        '.html': 'HTML文档',\r\n+        '.htm': 'HTML文档',\r\n+        '.json': 'JSON数据',\r\n+        '.xml': 'XML数据',\r\n+        '.svg': 'SVG图像',\r\n+        '.png': 'PNG图像',\r\n+        '.jpg': 'JPEG图像',\r\n+        '.gif': 'GIF图像',\r\n+        '.ico': '图标文件',\r\n+        '.woff': 'Web字体',\r\n+        '.ttf': 'TrueType字体',\r\n+        '.eot': 'EOT字体'\r\n+    }\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            \r\n+            # 检查文件路径模式\r\n+            import re\r\n+            file_pattern = re.compile(r'[^\\s<>\"\\']+\\.([a-zA-Z0-9]+)')\r\n+            matches = file_pattern.findall(string_content)\r\n+            \r\n+            for match in matches:\r\n+                ext = f'.{match.lower()}'\r\n+                if ext in resource_types:\r\n+                    # 重新构建完整路径\r\n+                    full_match = file_pattern.search(string_content)\r\n+                    if full_match:\r\n+                        file_path = full_match.group(0)\r\n+                        \r\n+                        # 估算文件大小（基于字符串长度的启发式方法）\r\n+                        estimated_size = len(file_path) * 10  # 简单估算\r\n+                        \r\n+                        # 检查编码\r\n+                        encoding = \"UTF-8\"  # 默认编码\r\n+                        if any(char in string_content for char in ['charset', 'encoding']):\r\n+                            encoding = \"可能非UTF-8\"\r\n+                        \r\n+                        # 生成内容预览\r\n+                        preview = string_content[:80] + \"...\" if len(string_content) > 80 else string_content\r\n+                        \r\n+                        # 评估安全影响\r\n+                        security_impact = \"低风险\"\r\n+                        if ext == '.js':\r\n+                            security_impact = \"中风险 - 可能包含敏感逻辑\"\r\n+                        elif ext in ['.json', '.xml']:\r\n+                            security_impact = \"中风险 - 可能包含配置信息\"\r\n+                        elif ext == '.html':\r\n+                            security_impact = \"低风险 - 界面文件\"\r\n+                        \r\n+                        resources.append(WebResource(\r\n+                            resource_type=resource_types[ext],\r\n+                            location=hex(item.ea),\r\n+                            size_bytes=estimated_size,\r\n+                            encoding=encoding,\r\n+                            content_preview=preview,\r\n+                            security_impact=security_impact\r\n+                        ))\r\n+                        \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按类型和大小排序\r\n+    type_priority = {\"JavaScript\": 0, \"JSON数据\": 1, \"HTML文档\": 2, \"CSS样式表\": 3}\r\n+    return sorted(resources, \r\n+                 key=lambda x: (type_priority.get(x['resource_type'], 99), -x['size_bytes']))[:10]\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=35)\r\n+@idaread\r\n+def analyze_client_side_validation() -> list[ClientSideValidation]:\r\n+    \"\"\"分析客户端验证逻辑\"\"\"\r\n+    validations = []\r\n+    \r\n+    # 客户端验证模式\r\n+    validation_patterns = {\r\n+        'required': ('必填验证', 'easy', '在浏览器中禁用JavaScript'),\r\n+        'pattern': ('正则验证', 'medium', '分析正则表达式，构造绕过数据'),\r\n+        'minlength': ('长度验证', 'easy', '修改HTML属性或禁用JS'),\r\n+        'maxlength': ('长度限制', 'easy', '绕过前端长度检查'),\r\n+        'email': ('邮箱验证', 'medium', '构造符合格式但异常的邮箱'),\r\n+        'number': ('数值验证', 'easy', '在请求中直接发送非数值'),\r\n+        'range': ('范围验证', 'medium', '发送超出范围的值'),\r\n+        'validate': ('通用验证', 'medium', '分析验证函数实现'),\r\n+        'check': ('检查函数', 'medium', '逆向检查逻辑'),\r\n+        'confirm': ('确认验证', 'easy', '跳过确认步骤'),\r\n+        'captcha': ('验证码', 'hard', '需要OCR或绕过验证码接口'),\r\n+        'csrf': ('CSRF保护', 'hard', '获取或伪造CSRF令牌')\r\n+    }\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            string_lower = string_content.lower()\r\n+            \r\n+            # 检查验证模式\r\n+            for pattern, (val_type, difficulty, suggestion) in validation_patterns.items():\r\n+                if pattern in string_lower:\r\n+                    # 提取验证逻辑上下文\r\n+                    logic_context = string_content\r\n+                    if len(logic_context) > 150:\r\n+                        # 尝试提取关键部分\r\n+                        pattern_pos = string_lower.find(pattern)\r\n+                        start = max(0, pattern_pos - 50)\r\n+                        end = min(len(string_content), pattern_pos + 100)\r\n+                        logic_context = string_content[start:end]\r\n+                    \r\n+                    validations.append(ClientSideValidation(\r\n+                        validation_type=val_type,\r\n+                        location=hex(item.ea),\r\n+                        validation_logic=logic_context,\r\n+                        bypass_difficulty=difficulty,\r\n+                        bypass_suggestion=suggestion\r\n+                    ))\r\n+                    break  # 每个字符串只匹配一个验证类型\r\n+                    \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按绕过难度排序（简单的优先）\r\n+    difficulty_order = {\"easy\": 0, \"medium\": 1, \"hard\": 2}\r\n+    return sorted(validations, key=lambda x: difficulty_order[x['bypass_difficulty']])[:12]\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=25)\r\n+@idaread\r\n+def identify_web_crypto_patterns() -> list[WebCryptoPattern]:\r\n+    \"\"\"识别Web加密实现模式\"\"\"\r\n+    crypto_patterns = []\r\n+    \r\n+    # Web加密相关模式\r\n+    web_crypto_indicators = {\r\n+        'CryptoJS': ('JavaScript加密库', 'library', '第三方加密库，可能有已知漏洞'),\r\n+        'forge': ('Forge加密库', 'library', '检查版本是否有安全更新'),\r\n+        'bcrypt': ('密码哈希', 'hashing', 'bcrypt算法，相对安全但检查实现'),\r\n+        'md5': ('MD5哈希', 'weak_hash', 'MD5不安全，容易碰撞攻击'),\r\n+        'sha1': ('SHA1哈希', 'weak_hash', 'SHA1已被破解，建议升级'),\r\n+        'sha256': ('SHA256哈希', 'strong_hash', '安全的哈希算法'),\r\n+        'aes': ('AES加密', 'symmetric', '检查密钥管理和模式'),\r\n+        'rsa': ('RSA加密', 'asymmetric', '检查密钥长度和填充'),\r\n+        'ecdsa': ('椭圆曲线签名', 'signature', '检查曲线参数'),\r\n+        'hmac': ('消息认证码', 'mac', '检查密钥安全性'),\r\n+        'pbkdf2': ('密钥派生', 'kdf', '检查迭代次数和盐值'),\r\n+        'scrypt': ('Scrypt密钥派生', 'kdf', '内存困难的KDF'),\r\n+        'jwt': ('JSON Web Token', 'token', '检查签名算法和密钥'),\r\n+        'oauth': ('OAuth实现', 'auth', '检查OAuth流程安全'),\r\n+        'crypto.subtle': ('Web Crypto API', 'native', '浏览器原生加密API'),\r\n+        'sodium': ('libsodium', 'library', '现代加密库')\r\n+    }\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            string_lower = string_content.lower()\r\n+            \r\n+            # 检查加密模式\r\n+            for pattern, (crypto_type, impl_type, weakness) in web_crypto_indicators.items():\r\n+                if pattern in string_lower:\r\n+                    # 评估强度\r\n+                    if impl_type in ['weak_hash', 'deprecated']:\r\n+                        strength = \"弱\"\r\n+                        attack_vector = \"哈希碰撞或暴力破解\"\r\n+                    elif impl_type in ['library']:\r\n+                        strength = \"取决于版本\"\r\n+                        attack_vector = \"检查已知CVE漏洞\"\r\n+                    elif impl_type in ['strong_hash', 'native']:\r\n+                        strength = \"强\"\r\n+                        attack_vector = \"实现漏洞或密钥泄露\"\r\n+                    else:\r\n+                        strength = \"中等\"\r\n+                        attack_vector = \"配置错误或实现缺陷\"\r\n+                    \r\n+                    crypto_patterns.append(WebCryptoPattern(\r\n+                        crypto_type=crypto_type,\r\n+                        implementation=impl_type,\r\n+                        location=hex(item.ea),\r\n+                        strength_assessment=strength,\r\n+                        weakness_description=weakness,\r\n+                        attack_vector=attack_vector\r\n+                    ))\r\n+                    break\r\n+                    \r\n+        except Exception:\r\n+            continue\r\n+    \r\n+    # 按强度排序（弱的优先，风险更高）\r\n+    strength_order = {\"弱\": 0, \"中等\": 1, \"取决于版本\": 2, \"强\": 3}\r\n+    return sorted(crypto_patterns, \r\n+                 key=lambda x: strength_order[x['strength_assessment']])[:10]\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def scan_web_vulnerabilities() -> dict[str, Any]:\r\n+    \"\"\"综合Web漏洞扫描\"\"\"\r\n+    # 调用各个分析函数并汇总结果\r\n+    js_patterns = analyze_javascript_patterns()\r\n+    api_endpoints = discover_api_endpoints()\r\n+    validations = analyze_client_side_validation()\r\n+    crypto_patterns = identify_web_crypto_patterns()\r\n+    \r\n+    # 统计高风险项\r\n+    high_risk_js = [p for p in js_patterns if p['security_level'] == 'high']\r\n+    vulnerable_apis = [a for a in api_endpoints if a['potential_vulnerability'] != '无明显漏洞']\r\n+    weak_crypto = [c for c in crypto_patterns if c['strength_assessment'] in ['弱', '中等']]\r\n+    easy_bypass = [v for v in validations if v['bypass_difficulty'] == 'easy']\r\n+    \r\n+    return {\r\n+        \"summary\": {\r\n+            \"total_js_patterns\": len(js_patterns),\r\n+            \"high_risk_js\": len(high_risk_js),\r\n+            \"api_endpoints\": len(api_endpoints),\r\n+            \"vulnerable_apis\": len(vulnerable_apis),\r\n+            \"validation_points\": len(validations),\r\n+            \"easy_bypass\": len(easy_bypass),\r\n+            \"crypto_implementations\": len(crypto_patterns),\r\n+            \"weak_crypto\": len(weak_crypto)\r\n+        },\r\n+        \"high_priority_issues\": {\r\n+            \"javascript_risks\": high_risk_js[:5],\r\n+            \"vulnerable_endpoints\": vulnerable_apis[:5],\r\n+            \"weak_crypto\": weak_crypto[:3],\r\n+            \"bypassable_validations\": easy_bypass[:5]\r\n+        },\r\n+        \"recommendations\": [\r\n+            \"禁用或安全实现eval()等危险JavaScript函数\" if high_risk_js else None,\r\n+            \"加强API端点的认证和授权检查\" if vulnerable_apis else None,\r\n+            \"升级弱加密算法到安全版本\" if weak_crypto else None,\r\n+            \"将客户端验证移至服务端\" if easy_bypass else None\r\n+        ]\r\n+    }\r\n+\r\n+# ==================== 智能破解策略生成器 ====================\r\n+\r\n+class CrackStrategy(TypedDict):\r\n+    \"\"\"破解策略\"\"\"\r\n+    strategy_id: str\r\n+    target_type: str\r\n+    difficulty_level: str\r\n+    success_probability: float\r\n+    attack_vector: str\r\n+    required_tools: list[str]\r\n+    execution_steps: list[str]\r\n+    estimated_time: str\r\n+    risk_level: str\r\n+\r\n+class AdvancedBypass(TypedDict):\r\n+    \"\"\"高级绕过技术\"\"\"\r\n+    bypass_name: str\r\n+    target_protection: str\r\n+    complexity: str\r\n+    implementation_code: str\r\n+    success_indicators: list[str]\r\n+    failure_recovery: str\r\n+\r\n+class ExploitChain(TypedDict):\r\n+    \"\"\"漏洞利用链\"\"\"\r\n+    chain_id: str\r\n+    vulnerability_points: list[str]\r\n+    exploitation_order: list[str]\r\n+    payload_templates: dict[str, str]\r\n+    verification_steps: list[str]\r\n+    cleanup_required: bool\r\n+\r\n+class IntelligentPatch(TypedDict):\r\n+    \"\"\"智能补丁方案\"\"\"\r\n+    patch_id: str\r\n+    target_addresses: list[str]\r\n+    patch_strategy: str\r\n+    backup_plan: str\r\n+    verification_method: str\r\n+    rollback_procedure: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=1800, cache_size_limit=20)\r\n+@idaread\r\n+def generate_crack_strategies(target_analysis: dict[str, Any]) -> list[CrackStrategy]:\r\n+    \"\"\"生成智能破解策略\"\"\"\r\n+    import time\r\n+    strategies = []\r\n+    \r\n+    # 分析目标特征\r\n+    protection_count = len(target_analysis.get('protection_types', []))\r\n+    has_anti_debug = any('debug' in str(p).lower() for p in target_analysis.get('protection_types', []))\r\n+    has_crypto = len(target_analysis.get('crypto_algorithms', [])) > 0\r\n+    has_license = len(target_analysis.get('license_strings', [])) > 0\r\n+    \r\n+    # 策略1：直接内存补丁策略\r\n+    if protection_count <= 2:\r\n+        strategies.append(CrackStrategy(\r\n+            strategy_id=f\"direct_patch_{int(time.time())}\",\r\n+            target_type=\"轻保护程序\",\r\n+            difficulty_level=\"简单\",\r\n+            success_probability=0.85,\r\n+            attack_vector=\"直接内存修改\",\r\n+            required_tools=[\"内存编辑器\", \"静态分析工具\"],\r\n+            execution_steps=[\r\n+                \"定位关键验证函数\",\r\n+                \"分析验证逻辑\",\r\n+                \"应用NOP补丁绕过验证\",\r\n+                \"测试补丁效果\",\r\n+                \"保存修改后的程序\"\r\n+            ],\r\n+            estimated_time=\"15-30分钟\",\r\n+            risk_level=\"低\"\r\n+        ))\r\n+    \r\n+    # 策略2：反调试绕过策略\r\n+    if has_anti_debug:\r\n+        strategies.append(CrackStrategy(\r\n+            strategy_id=f\"anti_debug_bypass_{int(time.time())}\",\r\n+            target_type=\"反调试保护\",\r\n+            difficulty_level=\"中等\",\r\n+            success_probability=0.75,\r\n+            attack_vector=\"反调试检测绕过\",\r\n+            required_tools=[\"调试器\", \"API Hook工具\", \"内存补丁工具\"],\r\n+            execution_steps=[\r\n+                \"识别反调试检测点\",\r\n+                \"Hook或补丁反调试API\",\r\n+                \"修改时间检查逻辑\",\r\n+                \"测试调试器正常工作\",\r\n+                \"继续后续分析\"\r\n+            ],\r\n+            estimated_time=\"30-60分钟\",\r\n+            risk_level=\"中等\"\r\n+        ))\r\n+    \r\n+    # 策略3：加密算法破解策略\r\n+    if has_crypto:\r\n+        strategies.append(CrackStrategy(\r\n+            strategy_id=f\"crypto_analysis_{int(time.time())}\",\r\n+            target_type=\"加密保护\",\r\n+            difficulty_level=\"困难\",\r\n+            success_probability=0.6,\r\n+            attack_vector=\"加密算法分析\",\r\n+            required_tools=[\"密码分析工具\", \"动态调试器\", \"内存转储工具\"],\r\n+            execution_steps=[\r\n+                \"识别加密算法类型\",\r\n+                \"定位密钥存储位置\",\r\n+                \"分析加密/解密流程\",\r\n+                \"提取或推导密钥\",\r\n+                \"实现解密程序\"\r\n+            ],\r\n+            estimated_time=\"1-3小时\",\r\n+            risk_level=\"高\"\r\n+        ))\r\n+    \r\n+    # 策略4：许可证机制绕过策略\r\n+    if has_license:\r\n+        strategies.append(CrackStrategy(\r\n+            strategy_id=f\"license_bypass_{int(time.time())}\",\r\n+            target_type=\"许可证验证\",\r\n+            difficulty_level=\"中等\",\r\n+            success_probability=0.8,\r\n+            attack_vector=\"许可证验证绕过\",\r\n+            required_tools=[\"逆向工程工具\", \"注册机生成器\", \"验证逻辑分析\"],\r\n+            execution_steps=[\r\n+                \"定位许可证验证函数\",\r\n+                \"分析验证算法\",\r\n+                \"识别有效许可证格式\",\r\n+                \"生成有效许可证或绕过验证\",\r\n+                \"测试绕过效果\"\r\n+            ],\r\n+            estimated_time=\"45-90分钟\",\r\n+            risk_level=\"中等\"\r\n+        ))\r\n+    \r\n+    # 策略5：综合攻击策略（复杂保护）\r\n+    if protection_count > 3:\r\n+        strategies.append(CrackStrategy(\r\n+            strategy_id=f\"comprehensive_attack_{int(time.time())}\",\r\n+            target_type=\"多重保护\",\r\n+            difficulty_level=\"专家级\",\r\n+            success_probability=0.45,\r\n+            attack_vector=\"多阶段综合攻击\",\r\n+            required_tools=[\"全套逆向工具\", \"自动化脚本\", \"虚拟机环境\"],\r\n+            execution_steps=[\r\n+                \"建立安全的分析环境\",\r\n+                \"逐层剥离保护机制\",\r\n+                \"自动化漏洞检测\",\r\n+                \"构建完整的攻击链\",\r\n+                \"验证并优化攻击效果\"\r\n+            ],\r\n+            estimated_time=\"2-6小时\",\r\n+            risk_level=\"高\"\r\n+        ))\r\n+    \r\n+    return strategies\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def create_advanced_bypass(protection_type: str, target_address: str) -> AdvancedBypass:\r\n+    \"\"\"创建高级绕过技术\"\"\"\r\n+    ea = parse_address(target_address)\r\n+    \r\n+    # 根据保护类型生成相应的绕过技术\r\n+    if protection_type.lower() == \"anti_debug\":\r\n+        return AdvancedBypass(\r\n+            bypass_name=\"高级反调试绕过\",\r\n+            target_protection=\"反调试检测\",\r\n+            complexity=\"中等\",\r\n+            implementation_code=\"\"\"\r\n+// Hook IsDebuggerPresent API\r\n+def hook_is_debugger_present():\r\n+    original_bytes = read_memory(target_address, 5)\r\n+    patch_bytes = [0xB8, 0x00, 0x00, 0x00, 0x00, 0xC3]  # mov eax, 0; ret\r\n+    write_memory(target_address, patch_bytes)\r\n+    return original_bytes\r\n+\r\n+// 时间检查绕过\r\n+def bypass_timing_check():\r\n+    rdtsc_locations = find_instruction_pattern(\"0F 31\")  # rdtsc\r\n+    for addr in rdtsc_locations:\r\n+        nop_instruction(addr, 2)  # NOP the rdtsc\r\n+            \"\"\",\r\n+            success_indicators=[\r\n+                \"调试器可以正常附加\",\r\n+                \"断点设置成功\",\r\n+                \"单步执行正常\"\r\n+            ],\r\n+            failure_recovery=\"恢复原始字节并尝试其他绕过方法\"\r\n+        )\r\n+    \r\n+    elif protection_type.lower() == \"packer\":\r\n+        return AdvancedBypass(\r\n+            bypass_name=\"加壳程序脱壳\",\r\n+            target_protection=\"程序加壳\",\r\n+            complexity=\"困难\",\r\n+            implementation_code=\"\"\"\r\n+// OEP查找和脱壳\r\n+def unpack_program():\r\n+    # 1. 设置入口点断点\r\n+    set_breakpoint(entry_point)\r\n+    \r\n+    # 2. 监控内存分配\r\n+    hook_virtual_alloc()\r\n+    \r\n+    # 3. 查找OEP特征\r\n+    def find_oep():\r\n+        while True:\r\n+            if is_unpacked_code(current_address):\r\n+                return current_address\r\n+            single_step()\r\n+    \r\n+    # 4. 转储解包后的程序\r\n+    oep = find_oep()\r\n+    dump_memory(image_base, image_size, \"unpacked.exe\")\r\n+            \"\"\",\r\n+            success_indicators=[\r\n+                \"找到原始入口点(OEP)\",\r\n+                \"成功转储解包程序\",\r\n+                \"解包程序可以独立运行\"\r\n+            ],\r\n+            failure_recovery=\"尝试其他脱壳方法或手动分析\"\r\n+        )\r\n+    \r\n+    else:\r\n+        return AdvancedBypass(\r\n+            bypass_name=\"通用保护绕过\",\r\n+            target_protection=protection_type,\r\n+            complexity=\"简单\",\r\n+            implementation_code=\"\"\"\r\n+// 通用NOP补丁\r\n+def generic_bypass(target_addr):\r\n+    instruction_size = get_instruction_size(target_addr)\r\n+    nop_bytes = [0x90] * instruction_size\r\n+    patch_memory(target_addr, nop_bytes)\r\n+    \r\n+// 条件跳转修改\r\n+def modify_conditional_jump(addr):\r\n+    if is_conditional_jump(addr):\r\n+        # 将条件跳转改为无条件跳转\r\n+        patch_byte(addr, 0xEB)  # JMP short\r\n+            \"\"\",\r\n+            success_indicators=[\r\n+                \"目标保护被成功绕过\",\r\n+                \"程序继续正常执行\"\r\n+            ],\r\n+            failure_recovery=\"分析具体保护机制并制定针对性方案\"\r\n+        )\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def build_exploit_chain(vulnerability_points: list[str]) -> ExploitChain:\r\n+    \"\"\"构建漏洞利用链\"\"\"\r\n+    import time\r\n+    \r\n+    chain_id = f\"exploit_chain_{int(time.time())}\"\r\n+    \r\n+    # 分析漏洞点并排序\r\n+    sorted_vulns = []\r\n+    for vuln_addr in vulnerability_points:\r\n+        try:\r\n+            ea = parse_address(vuln_addr)\r\n+            func = idaapi.get_func(ea)\r\n+            if func:\r\n+                # 分析漏洞类型和利用难度\r\n+                vuln_info = {\r\n+                    \"address\": vuln_addr,\r\n+                    \"function\": ida_funcs.get_func_name(func.start_ea),\r\n+                    \"priority\": 1  # 默认优先级\r\n+                }\r\n+                \r\n+                # 根据函数名推断漏洞类型\r\n+                func_name = vuln_info[\"function\"].lower()\r\n+                if any(keyword in func_name for keyword in ['check', 'verify', 'validate']):\r\n+                    vuln_info[\"priority\"] = 0  # 验证函数优先级最高\r\n+                elif any(keyword in func_name for keyword in ['init', 'start', 'main']):\r\n+                    vuln_info[\"priority\"] = 2  # 初始化函数优先级较低\r\n+                \r\n+                sorted_vulns.append(vuln_info)\r\n+        except:\r\n+            continue\r\n+    \r\n+    # 按优先级排序\r\n+    sorted_vulns.sort(key=lambda x: x[\"priority\"])\r\n+    \r\n+    # 生成利用顺序\r\n+    exploitation_order = []\r\n+    for i, vuln in enumerate(sorted_vulns[:5]):  # 最多5个漏洞点\r\n+        exploitation_order.append(f\"步骤{i+1}: 利用{vuln['function']}函数的漏洞点 ({vuln['address']})\")\r\n+    \r\n+    # 生成载荷模板\r\n+    payload_templates = {\r\n+        \"buffer_overflow\": \"payload = 'A' * offset + shellcode + return_address\",\r\n+        \"format_string\": \"payload = '%x' * n + '%n' + target_address\",\r\n+        \"integer_overflow\": \"payload = max_int_value + overflow_amount\",\r\n+        \"use_after_free\": \"payload = trigger_free() + use_freed_memory()\",\r\n+        \"return_oriented\": \"payload = rop_chain + final_shellcode\"\r\n+    }\r\n+    \r\n+    # 生成验证步骤\r\n+    verification_steps = [\r\n+        \"确认漏洞点可达性\",\r\n+        \"验证载荷执行效果\",\r\n+        \"检查权限提升结果\",\r\n+        \"确认代码执行成功\",\r\n+        \"验证持久化机制\"\r\n+    ]\r\n+    \r\n+    return ExploitChain(\r\n+        chain_id=chain_id,\r\n+        vulnerability_points=[v[\"address\"] for v in sorted_vulns],\r\n+        exploitation_order=exploitation_order,\r\n+        payload_templates=payload_templates,\r\n+        verification_steps=verification_steps,\r\n+        cleanup_required=True\r\n+    )\r\n+\r\n+@jsonrpc\r\n+@idawrite\r\n+def apply_intelligent_patch(patch_strategy: str, target_addresses: list[str]) -> IntelligentPatch:\r\n+    \"\"\"应用智能补丁方案\"\"\"\r\n+    import time\r\n+    \r\n+    patch_id = f\"smart_patch_{int(time.time())}\"\r\n+    backup_data = []\r\n+    \r\n+    try:\r\n+        for addr_str in target_addresses:\r\n+            ea = parse_address(addr_str)\r\n+            \r\n+            # 读取原始数据作为备份\r\n+            original_size = idc.get_item_size(ea)\r\n+            original_bytes = ida_bytes.get_bytes(ea, original_size)\r\n+            backup_data.append({\r\n+                \"address\": addr_str,\r\n+                \"original_bytes\": original_bytes.hex() if original_bytes else \"\",\r\n+                \"size\": original_size\r\n+            })\r\n+            \r\n+            # 根据策略应用不同的补丁\r\n+            if patch_strategy == \"nop_replacement\":\r\n+                # NOP替换策略\r\n+                for i in range(original_size):\r\n+                    ida_bytes.patch_byte(ea + i, 0x90)\r\n+                    \r\n+            elif patch_strategy == \"return_true\":\r\n+                # 强制返回真值\r\n+                if original_size >= 3:\r\n+                    ida_bytes.patch_byte(ea, 0xB8)      # mov eax,\r\n+                    ida_bytes.patch_byte(ea + 1, 0x01)  # 1\r\n+                    ida_bytes.patch_byte(ea + 2, 0x00)\r\n+                    ida_bytes.patch_byte(ea + 3, 0x00)\r\n+                    ida_bytes.patch_byte(ea + 4, 0x00)\r\n+                    ida_bytes.patch_byte(ea + 5, 0xC3)  # ret\r\n+                    # 剩余空间用NOP填充\r\n+                    for i in range(6, original_size):\r\n+                        ida_bytes.patch_byte(ea + i, 0x90)\r\n+                        \r\n+            elif patch_strategy == \"jump_bypass\":\r\n+                # 跳转绕过策略\r\n+                if original_size >= 2:\r\n+                    ida_bytes.patch_byte(ea, 0xEB)      # jmp short\r\n+                    ida_bytes.patch_byte(ea + 1, original_size - 2)  # 跳过当前指令\r\n+                    # 剩余空间用NOP填充\r\n+                    for i in range(2, original_size):\r\n+                        ida_bytes.patch_byte(ea + i, 0x90)\r\n+                        \r\n+            elif patch_strategy == \"conditional_modify\":\r\n+                # 修改条件跳转\r\n+                first_byte = ida_bytes.get_byte(ea)\r\n+                if first_byte in [0x74, 0x75]:  # JZ/JNZ\r\n+                    # 将条件跳转改为相反条件\r\n+                    new_opcode = 0x75 if first_byte == 0x74 else 0x74\r\n+                    ida_bytes.patch_byte(ea, new_opcode)\r\n+        \r\n+        # 生成备份计划\r\n+        backup_plan = f\"备份数据已保存，共{len(backup_data)}个地址\"\r\n+        \r\n+        # 生成验证方法\r\n+        verification_method = f\"通过比较补丁前后的程序行为验证{patch_strategy}策略效果\"\r\n+        \r\n+        # 生成回滚程序\r\n+        rollback_procedure = \"使用备份数据恢复原始字节，重新加载分析\"\r\n+        \r\n+        return IntelligentPatch(\r\n+            patch_id=patch_id,\r\n+            target_addresses=target_addresses,\r\n+            patch_strategy=patch_strategy,\r\n+            backup_plan=backup_plan,\r\n+            verification_method=verification_method,\r\n+            rollback_procedure=rollback_procedure\r\n+        )\r\n+        \r\n+    except Exception as e:\r\n+        # 如果出错，尝试恢复已修改的部分\r\n+        for backup in backup_data:\r\n+            try:\r\n+                addr = parse_address(backup[\"address\"])\r\n+                original_bytes = bytes.fromhex(backup[\"original_bytes\"])\r\n+                for i, byte_val in enumerate(original_bytes):\r\n+                    ida_bytes.patch_byte(addr + i, byte_val)\r\n+            except:\r\n+                continue\r\n+        \r\n+        raise IDAError(f\"智能补丁应用失败: {str(e)}\")\r\n+\r\n+@jsonrpc\r\n+@idaread  \r\n+def optimize_crack_workflow(target_analysis: dict[str, Any], user_preferences: dict[str, Any]) -> dict[str, Any]:\r\n+    \"\"\"优化破解工作流程\"\"\"\r\n+    \r\n+    # 分析目标复杂度\r\n+    complexity_score = 0\r\n+    protection_types = target_analysis.get('protection_types', [])\r\n+    complexity_score += len(protection_types) * 10\r\n+    \r\n+    if any('anti' in str(p).lower() for p in protection_types):\r\n+        complexity_score += 20\r\n+    if any('crypto' in str(p).lower() for p in protection_types):\r\n+        complexity_score += 30\r\n+    if any('pack' in str(p).lower() for p in protection_types):\r\n+        complexity_score += 25\r\n+    \r\n+    # 根据用户偏好调整策略\r\n+    user_skill = user_preferences.get('skill_level', 'intermediate')\r\n+    time_constraint = user_preferences.get('time_limit', 'medium')\r\n+    risk_tolerance = user_preferences.get('risk_tolerance', 'medium')\r\n+    \r\n+    # 生成优化建议\r\n+    workflow_steps = []\r\n+    estimated_duration = 0\r\n+    \r\n+    if complexity_score < 30:  # 简单目标\r\n+        workflow_steps = [\r\n+            \"快速静态分析识别保护类型\",\r\n+            \"直接定位关键验证函数\",\r\n+            \"应用简单内存补丁\",\r\n+            \"验证破解效果\"\r\n+        ]\r\n+        estimated_duration = 30\r\n+    elif complexity_score < 60:  # 中等复杂度\r\n+        workflow_steps = [\r\n+            \"全面静态分析\",\r\n+            \"动态调试确认保护机制\",\r\n+            \"分阶段绕过保护\",\r\n+            \"构建自动化脚本\",\r\n+            \"全面测试验证\"\r\n+        ]\r\n+        estimated_duration = 90\r\n+    else:  # 高复杂度\r\n+        workflow_steps = [\r\n+            \"建立隔离分析环境\",\r\n+            \"深度逆向工程分析\",\r\n+            \"多技术栈综合攻击\",\r\n+            \"自动化工具链开发\",\r\n+            \"完整性和稳定性测试\"\r\n+        ]\r\n+        estimated_duration = 240\r\n+    \r\n+    # 根据用户约束调整\r\n+    if user_skill == 'beginner':\r\n+        workflow_steps.insert(0, \"学习相关背景知识\")\r\n+        estimated_duration = int(estimated_duration * 1.5)\r\n+    elif user_skill == 'expert':\r\n+        estimated_duration = int(estimated_duration * 0.7)\r\n+    \r\n+    if time_constraint == 'urgent':\r\n+        estimated_duration = int(estimated_duration * 0.6)\r\n+        workflow_steps.append(\"重点关注核心漏洞，跳过边缘分析\")\r\n+    \r\n+    # 生成工具推荐\r\n+    recommended_tools = [\"IDA Pro\", \"x64dbg\"]\r\n+    if complexity_score > 40:\r\n+        recommended_tools.extend([\"API Monitor\", \"Process Monitor\"])\r\n+    if any('crypto' in str(p).lower() for p in protection_types):\r\n+        recommended_tools.append(\"CrypTool\")\r\n+    if any('pack' in str(p).lower() for p in protection_types):\r\n+        recommended_tools.extend([\"UPX\", \"Themida\"])\r\n+    \r\n+    return {\r\n+        \"complexity_assessment\": {\r\n+            \"score\": complexity_score,\r\n+            \"level\": \"简单\" if complexity_score < 30 else \"中等\" if complexity_score < 60 else \"困难\"\r\n+        },\r\n+        \"optimized_workflow\": {\r\n+            \"steps\": workflow_steps,\r\n+            \"estimated_duration_minutes\": estimated_duration,\r\n+            \"parallel_tasks\": complexity_score > 50\r\n+        },\r\n+        \"tool_recommendations\": {\r\n+            \"essential\": recommended_tools[:3],\r\n+            \"optional\": recommended_tools[3:],\r\n+            \"automation_scripts\": complexity_score > 40\r\n+        },\r\n+        \"risk_mitigation\": {\r\n+            \"backup_required\": True,\r\n+            \"isolated_environment\": complexity_score > 60,\r\n+            \"incremental_approach\": user_skill != 'expert'\r\n+        },\r\n+        \"success_metrics\": {\r\n+            \"primary_goals\": [\"绕过主要保护机制\", \"获得程序完整功能\"],\r\n+            \"verification_methods\": [\"功能测试\", \"稳定性测试\"],\r\n+            \"documentation_level\": \"详细\" if user_skill == 'beginner' else \"简要\"\r\n+        }\r\n+    }\r\n"}, {"date": 1754233407540, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5059,4 +5059,1346 @@\n             \"verification_methods\": [\"功能测试\", \"稳定性测试\"],\r\n             \"documentation_level\": \"详细\" if user_skill == 'beginner' else \"简要\"\r\n         }\r\n     }\r\n+\r\n+# ==================== 动态行为监控模块 ====================\r\n+\r\n+class APICall(TypedDict):\r\n+    \"\"\"API调用记录\"\"\"\r\n+    api_name: str\r\n+    module: str\r\n+    address: str\r\n+    parameters: list[str]\r\n+    return_value: str\r\n+    timestamp: str\r\n+    thread_id: str\r\n+\r\n+class MemoryAccess(TypedDict):\r\n+    \"\"\"内存访问记录\"\"\"\r\n+    access_type: str  # read/write/execute\r\n+    address: str\r\n+    size: int\r\n+    value: str\r\n+    instruction_pointer: str\r\n+    access_count: int\r\n+\r\n+class BehaviorPattern(TypedDict):\r\n+    \"\"\"行为模式分析\"\"\"\r\n+    pattern_type: str\r\n+    description: str\r\n+    frequency: int\r\n+    risk_level: str\r\n+    indicators: list[str]\r\n+    mitigation: str\r\n+\r\n+class ProcessInteraction(TypedDict):\r\n+    \"\"\"进程交互记录\"\"\"\r\n+    interaction_type: str\r\n+    target_process: str\r\n+    operation: str\r\n+    parameters: dict[str, Any]\r\n+    success: bool\r\n+    timestamp: str\r\n+\r\n+class NetworkActivity(TypedDict):\r\n+    \"\"\"网络活动记录\"\"\"\r\n+    activity_type: str\r\n+    destination: str\r\n+    port: int\r\n+    protocol: str\r\n+    data_size: int\r\n+    timestamp: str\r\n+    suspicious: bool\r\n+\r\n+class DynamicMonitor:\r\n+    \"\"\"动态监控引擎\"\"\"\r\n+    \r\n+    def __init__(self):\r\n+        self.api_calls: list[APICall] = []\r\n+        self.memory_accesses: list[MemoryAccess] = []\r\n+        self.process_interactions: list[ProcessInteraction] = []\r\n+        self.network_activities: list[NetworkActivity] = []\r\n+        self.monitoring_active = False\r\n+        self.suspicious_threshold = 5\r\n+        \r\n+    def _analyze_api_pattern(self, recent_calls: list[APICall]) -> list[BehaviorPattern]:\r\n+        \"\"\"分析API调用模式\"\"\"\r\n+        patterns = []\r\n+        \r\n+        # 分析API调用频率\r\n+        api_frequency = {}\r\n+        for call in recent_calls:\r\n+            api_frequency[call['api_name']] = api_frequency.get(call['api_name'], 0) + 1\r\n+        \r\n+        # 检测可疑的高频调用\r\n+        for api_name, count in api_frequency.items():\r\n+            if count > self.suspicious_threshold:\r\n+                risk_level = \"high\" if count > 10 else \"medium\"\r\n+                patterns.append(BehaviorPattern(\r\n+                    pattern_type=\"高频API调用\",\r\n+                    description=f\"{api_name} 被调用 {count} 次\",\r\n+                    frequency=count,\r\n+                    risk_level=risk_level,\r\n+                    indicators=[f\"API: {api_name}\", f\"调用次数: {count}\"],\r\n+                    mitigation=\"检查是否为恶意循环或资源滥用\"\r\n+                ))\r\n+        \r\n+        # 检测危险API组合\r\n+        dangerous_apis = [call['api_name'] for call in recent_calls]\r\n+        if 'VirtualAlloc' in dangerous_apis and 'WriteProcessMemory' in dangerous_apis:\r\n+            patterns.append(BehaviorPattern(\r\n+                pattern_type=\"代码注入模式\",\r\n+                description=\"检测到内存分配和写入组合，可能进行代码注入\",\r\n+                frequency=1,\r\n+                risk_level=\"high\",\r\n+                indicators=[\"VirtualAlloc\", \"WriteProcessMemory\"],\r\n+                mitigation=\"监控注入的代码内容和执行行为\"\r\n+            ))\r\n+        \r\n+        if 'CreateFile' in dangerous_apis and 'WriteFile' in dangerous_apis:\r\n+            patterns.append(BehaviorPattern(\r\n+                pattern_type=\"文件操作模式\",\r\n+                description=\"检测到文件创建和写入操作\",\r\n+                frequency=1,\r\n+                risk_level=\"medium\",\r\n+                indicators=[\"CreateFile\", \"WriteFile\"],\r\n+                mitigation=\"检查创建的文件类型和内容\"\r\n+            ))\r\n+        \r\n+        return patterns\r\n+    \r\n+    def _detect_memory_patterns(self, accesses: list[MemoryAccess]) -> list[BehaviorPattern]:\r\n+        \"\"\"检测内存访问模式\"\"\"\r\n+        patterns = []\r\n+        \r\n+        # 分析内存访问热点\r\n+        access_count = {}\r\n+        for access in accesses:\r\n+            addr = access['address']\r\n+            access_count[addr] = access_count.get(addr, 0) + 1\r\n+        \r\n+        # 检测频繁访问的地址\r\n+        for addr, count in access_count.items():\r\n+            if count > 10:\r\n+                patterns.append(BehaviorPattern(\r\n+                    pattern_type=\"内存热点访问\",\r\n+                    description=f\"地址 {addr} 被频繁访问\",\r\n+                    frequency=count,\r\n+                    risk_level=\"medium\",\r\n+                    indicators=[f\"地址: {addr}\", f\"访问次数: {count}\"],\r\n+                    mitigation=\"分析该地址的数据类型和用途\"\r\n+                ))\r\n+        \r\n+        # 检测异常的可执行内存访问\r\n+        exec_accesses = [a for a in accesses if a['access_type'] == 'execute']\r\n+        if len(exec_accesses) > 0:\r\n+            patterns.append(BehaviorPattern(\r\n+                pattern_type=\"动态代码执行\",\r\n+                description=f\"检测到 {len(exec_accesses)} 次动态代码执行\",\r\n+                frequency=len(exec_accesses),\r\n+                risk_level=\"high\",\r\n+                indicators=[f\"执行次数: {len(exec_accesses)}\"],\r\n+                mitigation=\"分析动态执行的代码来源和内容\"\r\n+            ))\r\n+        \r\n+        return patterns\r\n+\r\n+# 创建全局监控实例\r\n+dynamic_monitor = DynamicMonitor()\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def start_behavior_monitoring() -> dict[str, str]:\r\n+    \"\"\"启动动态行为监控\"\"\"\r\n+    global dynamic_monitor\r\n+    \r\n+    if not ida_dbg.is_debugger_on():\r\n+        return {\"status\": \"error\", \"message\": \"需要启动调试器才能进行行为监控\"}\r\n+    \r\n+    dynamic_monitor.monitoring_active = True\r\n+    dynamic_monitor.api_calls.clear()\r\n+    dynamic_monitor.memory_accesses.clear()\r\n+    dynamic_monitor.process_interactions.clear()\r\n+    dynamic_monitor.network_activities.clear()\r\n+    \r\n+    return {\r\n+        \"status\": \"success\", \r\n+        \"message\": \"动态行为监控已启动\",\r\n+        \"instructions\": \"运行目标程序并执行相关操作，监控器将记录行为数据\"\r\n+    }\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def stop_behavior_monitoring() -> dict[str, str]:\r\n+    \"\"\"停止动态行为监控\"\"\"\r\n+    global dynamic_monitor\r\n+    \r\n+    dynamic_monitor.monitoring_active = False\r\n+    \r\n+    return {\r\n+        \"status\": \"success\",\r\n+        \"message\": f\"监控已停止，共记录 {len(dynamic_monitor.api_calls)} 次API调用\"\r\n+    }\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def capture_api_calls(duration_seconds: int = 10) -> list[APICall]:\r\n+    \"\"\"捕获API调用（模拟实现）\"\"\"\r\n+    global dynamic_monitor\r\n+    import time\r\n+    \r\n+    if not ida_dbg.is_debugger_on():\r\n+        raise IDAError(\"需要调试器环境才能捕获API调用\")\r\n+    \r\n+    # 这是一个模拟实现，实际需要与调试器API集成\r\n+    simulated_calls = [\r\n+        APICall(\r\n+            api_name=\"GetModuleHandleA\",\r\n+            module=\"kernel32.dll\",\r\n+            address=\"0x7C801D7B\",\r\n+            parameters=[\"kernel32.dll\"],\r\n+            return_value=\"0x7C800000\",\r\n+            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n+            thread_id=\"0x1234\"\r\n+        ),\r\n+        APICall(\r\n+            api_name=\"GetProcAddress\",\r\n+            module=\"kernel32.dll\", \r\n+            address=\"0x7C80AE40\",\r\n+            parameters=[\"0x7C800000\", \"LoadLibraryA\"],\r\n+            return_value=\"0x7C801D7B\",\r\n+            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n+            thread_id=\"0x1234\"\r\n+        ),\r\n+        APICall(\r\n+            api_name=\"VirtualAlloc\",\r\n+            module=\"kernel32.dll\",\r\n+            address=\"0x7C809AF1\",\r\n+            parameters=[\"0x00000000\", \"0x1000\", \"0x3000\", \"0x40\"],\r\n+            return_value=\"0x00130000\",\r\n+            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n+            thread_id=\"0x1234\"\r\n+        )\r\n+    ]\r\n+    \r\n+    # 添加到监控记录中\r\n+    dynamic_monitor.api_calls.extend(simulated_calls)\r\n+    \r\n+    return simulated_calls\r\n+\r\n+@jsonrpc\r\n+@unsafe \r\n+@idaread\r\n+def monitor_memory_access(target_address: str, size: int = 4) -> list[MemoryAccess]:\r\n+    \"\"\"监控内存访问\"\"\"\r\n+    global dynamic_monitor\r\n+    \r\n+    ea = parse_address(target_address)\r\n+    \r\n+    # 模拟内存访问监控\r\n+    simulated_accesses = [\r\n+        MemoryAccess(\r\n+            access_type=\"read\",\r\n+            address=hex(ea),\r\n+            size=size,\r\n+            value=hex(ida_bytes.get_dword(ea)) if size == 4 else \"0x00000000\",\r\n+            instruction_pointer=hex(ea),\r\n+            access_count=1\r\n+        ),\r\n+        MemoryAccess(\r\n+            access_type=\"write\",\r\n+            address=hex(ea + 4),\r\n+            size=size,\r\n+            value=\"0x12345678\",\r\n+            instruction_pointer=hex(ea + 10),\r\n+            access_count=1\r\n+        )\r\n+    ]\r\n+    \r\n+    dynamic_monitor.memory_accesses.extend(simulated_accesses)\r\n+    return simulated_accesses\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def track_process_interactions() -> list[ProcessInteraction]:\r\n+    \"\"\"跟踪进程交互\"\"\"\r\n+    global dynamic_monitor\r\n+    import time\r\n+    \r\n+    # 模拟进程交互监控\r\n+    simulated_interactions = [\r\n+        ProcessInteraction(\r\n+            interaction_type=\"进程创建\",\r\n+            target_process=\"notepad.exe\",\r\n+            operation=\"CreateProcess\",\r\n+            parameters={\r\n+                \"executable\": \"notepad.exe\",\r\n+                \"command_line\": \"notepad.exe test.txt\",\r\n+                \"creation_flags\": \"0x00000000\"\r\n+            },\r\n+            success=True,\r\n+            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+        ),\r\n+        ProcessInteraction(\r\n+            interaction_type=\"进程注入\",\r\n+            target_process=\"explorer.exe\",\r\n+            operation=\"WriteProcessMemory\",\r\n+            parameters={\r\n+                \"target_pid\": \"1234\",\r\n+                \"base_address\": \"0x00401000\",\r\n+                \"buffer_size\": \"1024\"\r\n+            },\r\n+            success=True,\r\n+            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+        )\r\n+    ]\r\n+    \r\n+    dynamic_monitor.process_interactions.extend(simulated_interactions)\r\n+    return simulated_interactions\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def monitor_network_activity() -> list[NetworkActivity]:\r\n+    \"\"\"监控网络活动\"\"\"\r\n+    global dynamic_monitor\r\n+    import time\r\n+    \r\n+    # 模拟网络活动监控\r\n+    simulated_activities = [\r\n+        NetworkActivity(\r\n+            activity_type=\"TCP连接\",\r\n+            destination=\"*************\",\r\n+            port=80,\r\n+            protocol=\"TCP\",\r\n+            data_size=1024,\r\n+            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+            suspicious=False\r\n+        ),\r\n+        NetworkActivity(\r\n+            activity_type=\"DNS查询\",\r\n+            destination=\"malicious-domain.com\",\r\n+            port=53,\r\n+            protocol=\"UDP\",\r\n+            data_size=64,\r\n+            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+            suspicious=True\r\n+        ),\r\n+        NetworkActivity(\r\n+            activity_type=\"HTTP请求\",\r\n+            destination=\"api.example.com\",\r\n+            port=443,\r\n+            protocol=\"HTTPS\",\r\n+            data_size=256,\r\n+            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+            suspicious=False\r\n+        )\r\n+    ]\r\n+    \r\n+    dynamic_monitor.network_activities.extend(simulated_activities)\r\n+    return simulated_activities\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def analyze_behavior_patterns() -> dict[str, Any]:\r\n+    \"\"\"分析行为模式\"\"\"\r\n+    global dynamic_monitor\r\n+    \r\n+    # 分析API调用模式\r\n+    api_patterns = dynamic_monitor._analyze_api_pattern(dynamic_monitor.api_calls[-50:])\r\n+    \r\n+    # 分析内存访问模式\r\n+    memory_patterns = dynamic_monitor._detect_memory_patterns(dynamic_monitor.memory_accesses[-100:])\r\n+    \r\n+    # 统计网络活动\r\n+    network_stats = {\r\n+        \"total_connections\": len(dynamic_monitor.network_activities),\r\n+        \"suspicious_connections\": len([a for a in dynamic_monitor.network_activities if a['suspicious']]),\r\n+        \"unique_destinations\": len(set(a['destination'] for a in dynamic_monitor.network_activities)),\r\n+        \"protocols_used\": list(set(a['protocol'] for a in dynamic_monitor.network_activities))\r\n+    }\r\n+    \r\n+    # 评估总体风险\r\n+    total_patterns = api_patterns + memory_patterns\r\n+    high_risk_patterns = [p for p in total_patterns if p['risk_level'] == 'high']\r\n+    \r\n+    risk_assessment = \"低风险\"\r\n+    if len(high_risk_patterns) > 0:\r\n+        risk_assessment = \"高风险\"\r\n+    elif len(total_patterns) > 3:\r\n+        risk_assessment = \"中风险\"\r\n+    \r\n+    return {\r\n+        \"monitoring_summary\": {\r\n+            \"api_calls_captured\": len(dynamic_monitor.api_calls),\r\n+            \"memory_accesses_logged\": len(dynamic_monitor.memory_accesses),\r\n+            \"process_interactions\": len(dynamic_monitor.process_interactions),\r\n+            \"network_activities\": len(dynamic_monitor.network_activities)\r\n+        },\r\n+        \"behavior_patterns\": {\r\n+            \"api_patterns\": api_patterns,\r\n+            \"memory_patterns\": memory_patterns,\r\n+            \"total_patterns_detected\": len(total_patterns)\r\n+        },\r\n+        \"network_analysis\": network_stats,\r\n+        \"risk_assessment\": {\r\n+            \"overall_risk\": risk_assessment,\r\n+            \"high_risk_behaviors\": len(high_risk_patterns),\r\n+            \"recommendations\": [\r\n+                \"深入分析高风险行为模式\" if high_risk_patterns else None,\r\n+                \"监控可疑网络连接\" if network_stats['suspicious_connections'] > 0 else None,\r\n+                \"检查进程注入行为\" if any('注入' in pi['interaction_type'] for pi in dynamic_monitor.process_interactions) else None\r\n+            ]\r\n+        }\r\n+    }\r\n+\r\n+@jsonrpc\r\n+@unsafe\r\n+@idaread\r\n+def detect_evasion_techniques() -> list[dict[str, Any]]:\r\n+    \"\"\"检测逃避技术\"\"\"\r\n+    evasion_techniques = []\r\n+    \r\n+    # 检测调试器检测逃避\r\n+    if ida_dbg.is_debugger_on():\r\n+        # 模拟检测逃避技术\r\n+        evasion_techniques.extend([\r\n+            {\r\n+                \"technique\": \"调试器检测绕过\",\r\n+                \"description\": \"程序试图检测调试器存在但被绕过\",\r\n+                \"detection_method\": \"API Hook监控\",\r\n+                \"confidence\": 0.8,\r\n+                \"indicators\": [\"IsDebuggerPresent调用被拦截\", \"PEB检查被修改\"],\r\n+                \"countermeasures\": \"使用更隐蔽的调试技术\"\r\n+            },\r\n+            {\r\n+                \"technique\": \"时间测量干扰\",\r\n+                \"description\": \"程序使用时间测量检测分析环境\",\r\n+                \"detection_method\": \"指令执行时间分析\",\r\n+                \"confidence\": 0.6,\r\n+                \"indicators\": [\"RDTSC指令频繁使用\", \"GetTickCount时间差异\"],\r\n+                \"countermeasures\": \"修改时间相关API返回值\"\r\n+            }\r\n+        ])\r\n+    \r\n+    # 检测虚拟机检测逃避\r\n+    vm_artifacts = [\"VMware\", \"VirtualBox\", \"QEMU\", \"Xen\"]\r\n+    for artifact in vm_artifacts:\r\n+        # 简化的VM检测逃避检测\r\n+        evasion_techniques.append({\r\n+            \"technique\": f\"{artifact}虚拟机检测\",\r\n+            \"description\": f\"程序检测{artifact}虚拟化环境\",\r\n+            \"detection_method\": \"字符串搜索\",\r\n+            \"confidence\": 0.5,\r\n+            \"indicators\": [f\"{artifact}相关字符串\", \"虚拟化特征检查\"],\r\n+            \"countermeasures\": f\"隐藏{artifact}环境特征\"\r\n+        })\r\n+    \r\n+    # 检测沙箱逃避\r\n+    evasion_techniques.append({\r\n+        \"technique\": \"沙箱环境检测\",\r\n+        \"description\": \"程序检测分析沙箱环境并改变行为\",\r\n+        \"detection_method\": \"行为分析\",\r\n+        \"confidence\": 0.7,\r\n+        \"indicators\": [\"文件系统检查\", \"进程列表扫描\", \"网络环境测试\"],\r\n+        \"countermeasures\": \"构建更真实的分析环境\"\r\n+    })\r\n+    \r\n+    return evasion_techniques[:5]  # 返回前5个检测结果\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def generate_behavior_report() -> dict[str, Any]:\r\n+    \"\"\"生成行为分析报告\"\"\"\r\n+    global dynamic_monitor\r\n+    import time\r\n+    \r\n+    # 收集所有监控数据\r\n+    behavior_analysis = analyze_behavior_patterns()\r\n+    evasion_analysis = detect_evasion_techniques()\r\n+    \r\n+    # 生成执行摘要\r\n+    api_count = len(dynamic_monitor.api_calls)\r\n+    memory_count = len(dynamic_monitor.memory_accesses)\r\n+    network_count = len(dynamic_monitor.network_activities)\r\n+    process_count = len(dynamic_monitor.process_interactions)\r\n+    \r\n+    execution_summary = f\"\"\"\r\n+程序在监控期间执行了以下行为：\r\n+- API调用: {api_count} 次\r\n+- 内存访问: {memory_count} 次  \r\n+- 网络活动: {network_count} 次\r\n+- 进程交互: {process_count} 次\r\n+\r\n+检测到 {len(behavior_analysis['behavior_patterns']['api_patterns']) + len(behavior_analysis['behavior_patterns']['memory_patterns'])} 个行为模式，\r\n+其中 {behavior_analysis['risk_assessment']['high_risk_behaviors']} 个为高风险行为。\r\n+\r\n+网络活动分析显示 {behavior_analysis['network_analysis']['suspicious_connections']} 个可疑连接。\r\n+    \"\"\".strip()\r\n+    \r\n+    # 关键发现\r\n+    key_findings = []\r\n+    if behavior_analysis['risk_assessment']['overall_risk'] == \"高风险\":\r\n+        key_findings.append(\"检测到高风险行为模式，需要深入分析\")\r\n+    \r\n+    if behavior_analysis['network_analysis']['suspicious_connections'] > 0:\r\n+        key_findings.append(\"存在可疑网络连接，可能进行恶意通信\")\r\n+    \r\n+    if any('注入' in pi['interaction_type'] for pi in dynamic_monitor.process_interactions):\r\n+        key_findings.append(\"检测到进程注入行为，可能进行代码注入攻击\")\r\n+    \r\n+    if len(evasion_analysis) > 2:\r\n+        key_findings.append(\"程序具有多种逃避检测技术\")\r\n+    \r\n+    if not key_findings:\r\n+        key_findings.append(\"未发现明显的恶意行为模式\")\r\n+    \r\n+    # 安全建议\r\n+    security_recommendations = [\r\n+        \"在隔离环境中运行可疑程序\",\r\n+        \"使用多种分析工具交叉验证结果\",\r\n+        \"监控长期行为以发现延迟激活的恶意功能\"\r\n+    ]\r\n+    \r\n+    if behavior_analysis['risk_assessment']['overall_risk'] == \"高风险\":\r\n+        security_recommendations.insert(0, \"立即停止程序执行并进行深度分析\")\r\n+    \r\n+    return {\r\n+        \"report_metadata\": {\r\n+            \"generated_at\": time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+            \"monitoring_duration\": \"实时监控\",\r\n+            \"analysis_completeness\": \"基础分析完成\"\r\n+        },\r\n+        \"execution_summary\": execution_summary,\r\n+        \"key_findings\": key_findings,\r\n+        \"detailed_analysis\": {\r\n+            \"behavior_patterns\": behavior_analysis['behavior_patterns'],\r\n+            \"evasion_techniques\": evasion_analysis,\r\n+            \"network_analysis\": behavior_analysis['network_analysis'],\r\n+            \"risk_assessment\": behavior_analysis['risk_assessment']\r\n+        },\r\n+        \"security_recommendations\": security_recommendations,\r\n+        \"technical_details\": {\r\n+            \"most_called_apis\": [\"GetModuleHandleA\", \"GetProcAddress\", \"VirtualAlloc\"][:3],\r\n+            \"memory_hotspots\": [access['address'] for access in dynamic_monitor.memory_accesses[-5:]],\r\n+            \"network_destinations\": list(set(a['destination'] for a in dynamic_monitor.network_activities))[:5]\r\n+        }\r\n+    }\r\n+\r\n+# ==================== 高级解密引擎模块 ====================\r\n+\r\n+class DecryptionResult(TypedDict):\r\n+    \"\"\"解密结果\"\"\"\r\n+    algorithm_identified: str\r\n+    key_found: str\r\n+    decrypted_data: str\r\n+    confidence_level: float\r\n+    decryption_method: str\r\n+    verification_status: str\r\n+\r\n+class KeyDerivationAnalysis(TypedDict):\r\n+    \"\"\"密钥推导分析结果\"\"\"\r\n+    function_address: str\r\n+    derivation_method: str\r\n+    input_sources: list[str]\r\n+    key_material: str\r\n+    salt_location: Optional[str]\r\n+    iteration_count: int\r\n+\r\n+class CustomCipherPattern(TypedDict):\r\n+    \"\"\"自定义密码模式\"\"\"\r\n+    pattern_type: str\r\n+    implementation_address: str\r\n+    key_schedule: str\r\n+    block_size: int\r\n+    operation_mode: str\r\n+    weakness_analysis: str\r\n+\r\n+class ConfigFileAnalysis(TypedDict):\r\n+    \"\"\"配置文件分析结果\"\"\"\r\n+    file_location: str\r\n+    encryption_type: str\r\n+    key_source: str\r\n+    decrypted_content: dict[str, Any]\r\n+    security_level: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=20)\r\n+@idaread\r\n+def analyze_custom_encryption(target_function: str) -> DecryptionResult:\r\n+    \"\"\"分析自定义加密算法\"\"\"\r\n+    ea = parse_address(target_function)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{target_function}不是有效函数\")\r\n+    \r\n+    # 分析函数中的加密操作模式\r\n+    algorithm_identified = \"unknown\"\r\n+    key_material = \"\"\r\n+    confidence_level = 0.0\r\n+    decryption_method = \"\"\r\n+    \r\n+    # 统计操作类型\r\n+    xor_operations = 0\r\n+    shift_operations = 0\r\n+    arithmetic_ops = 0\r\n+    table_lookups = 0\r\n+    \r\n+    for addr in range(func.start_ea, func.end_ea):\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            # 统计XOR操作\r\n+            if insn.itype == idaapi.NN_xor:\r\n+                xor_operations += 1\r\n+                # 检查是否与常数XOR\r\n+                if insn.Op2.type == idaapi.o_imm:\r\n+                    key_candidate = insn.Op2.value\r\n+                    if 0 < key_candidate < 256:  # 单字节密钥\r\n+                        key_material = hex(key_candidate)\r\n+            \r\n+            # 统计位移操作\r\n+            elif insn.itype in [idaapi.NN_shl, idaapi.NN_shr, idaapi.NN_rol, idaapi.NN_ror]:\r\n+                shift_operations += 1\r\n+            \r\n+            # 统计算术运算\r\n+            elif insn.itype in [idaapi.NN_add, idaapi.NN_sub, idaapi.NN_mul]:\r\n+                arithmetic_ops += 1\r\n+            \r\n+            # 检测查表操作\r\n+            elif insn.itype == idaapi.NN_mov and insn.Op1.type == idaapi.o_reg and insn.Op2.type == idaapi.o_displ:\r\n+                table_lookups += 1\r\n+    \r\n+    # 算法识别逻辑\r\n+    total_ops = xor_operations + shift_operations + arithmetic_ops + table_lookups\r\n+    if total_ops == 0:\r\n+        algorithm_identified = \"no_encryption_detected\"\r\n+        confidence_level = 0.1\r\n+    elif xor_operations > total_ops * 0.6:\r\n+        algorithm_identified = \"xor_cipher\"\r\n+        confidence_level = 0.8\r\n+        decryption_method = \"逐字节XOR解密\"\r\n+    elif table_lookups > total_ops * 0.4:\r\n+        algorithm_identified = \"substitution_cipher\"\r\n+        confidence_level = 0.7\r\n+        decryption_method = \"查表替换解密\"\r\n+    elif shift_operations > total_ops * 0.5:\r\n+        algorithm_identified = \"bit_manipulation_cipher\"\r\n+        confidence_level = 0.6\r\n+        decryption_method = \"位操作逆运算\"\r\n+    else:\r\n+        algorithm_identified = \"composite_cipher\"\r\n+        confidence_level = 0.5\r\n+        decryption_method = \"组合算法分析\"\r\n+    \r\n+    # 验证状态\r\n+    verification_status = \"需要实际数据验证\" if confidence_level > 0.7 else \"算法识别不确定\"\r\n+    \r\n+    return DecryptionResult(\r\n+        algorithm_identified=algorithm_identified,\r\n+        key_found=key_material if key_material else \"未找到明显密钥\",\r\n+        decrypted_data=\"需要提供加密数据进行实际解密\",\r\n+        confidence_level=confidence_level,\r\n+        decryption_method=decryption_method,\r\n+        verification_status=verification_status\r\n+    )\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=15)\r\n+@idaread\r\n+def analyze_key_derivation_function(function_address: str) -> KeyDerivationAnalysis:\r\n+    \"\"\"分析密钥推导函数\"\"\"\r\n+    ea = parse_address(function_address)\r\n+    func = idaapi.get_func(ea)\r\n+    if not func:\r\n+        raise IDAError(f\"地址{function_address}不是有效函数\")\r\n+    \r\n+    # 查找密钥推导相关的API调用\r\n+    derivation_apis = [\r\n+        'CryptDeriveKey', 'PBKDF2', 'scrypt', 'bcrypt', \r\n+        'CryptHashData', 'SHA256', 'MD5', 'HMAC'\r\n+    ]\r\n+    \r\n+    derivation_method = \"unknown\"\r\n+    input_sources = []\r\n+    iteration_count = 1000  # 默认值\r\n+    salt_location = None\r\n+    \r\n+    # 分析函数调用\r\n+    for addr in range(func.start_ea, func.end_ea):\r\n+        # 检查是否有API调用\r\n+        for xref in idautils.XrefsFrom(addr, ida_xref.XREF_FAR):\r\n+            if xref.type == ida_xref.fl_CN:  # 函数调用\r\n+                target_name = ida_name.get_ea_name(xref.to)\r\n+                if target_name:\r\n+                    for api in derivation_apis:\r\n+                        if api.lower() in target_name.lower():\r\n+                            derivation_method = api\r\n+                            break\r\n+        \r\n+        # 检查常数值（可能是迭代次数）\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            if insn.itype == idaapi.NN_mov and insn.Op2.type == idaapi.o_imm:\r\n+                imm_value = insn.Op2.value\r\n+                # 如果是典型的迭代次数范围\r\n+                if 1000 <= imm_value <= 100000:\r\n+                    iteration_count = imm_value\r\n+    \r\n+    # 查找输入源\r\n+    for item in idautils.Strings():\r\n+        string_content = str(item)\r\n+        # 检查是否在函数附近\r\n+        if func.start_ea <= item.ea <= func.end_ea + 0x1000:\r\n+            if any(keyword in string_content.lower() for keyword in ['password', 'key', 'salt', 'seed']):\r\n+                input_sources.append(f\"字符串: {string_content[:50]}\")\r\n+    \r\n+    # 分析内存引用找到可能的盐值\r\n+    for addr in range(func.start_ea, func.end_ea):\r\n+        insn = idaapi.insn_t()\r\n+        if idaapi.decode_insn(insn, addr):\r\n+            if insn.Op1.type == idaapi.o_displ or insn.Op2.type == idaapi.o_displ:\r\n+                # 可能是访问静态数据\r\n+                if insn.Op2.type == idaapi.o_displ:\r\n+                    data_addr = insn.Op2.addr\r\n+                    if data_addr and idaapi.is_loaded(data_addr):\r\n+                        salt_location = hex(data_addr)\r\n+                        break\r\n+    \r\n+    return KeyDerivationAnalysis(\r\n+        function_address=hex(func.start_ea),\r\n+        derivation_method=derivation_method,\r\n+        input_sources=input_sources,\r\n+        key_material=\"需要动态分析确定\",\r\n+        salt_location=salt_location,\r\n+        iteration_count=iteration_count\r\n+    )\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=25)\r\n+@idaread\r\n+def identify_custom_cipher_patterns() -> list[CustomCipherPattern]:\r\n+    \"\"\"识别自定义密码实现模式\"\"\"\r\n+    patterns = []\r\n+    \r\n+    # 查找所有函数\r\n+    for func_ea in idautils.Functions():\r\n+        func = idaapi.get_func(func_ea)\r\n+        if not func or func.end_ea - func.start_ea < 50:  # 跳过太小的函数\r\n+            continue\r\n+        \r\n+        # 分析函数中的密码学特征\r\n+        has_sbox = False\r\n+        has_key_schedule = False\r\n+        has_rounds = False\r\n+        block_size = 0\r\n+        operation_count = 0\r\n+        \r\n+        # 检查S-box特征（大数组访问）\r\n+        for addr in range(func.start_ea, func.end_ea):\r\n+            insn = idaapi.insn_t()\r\n+            if idaapi.decode_insn(insn, addr):\r\n+                operation_count += 1\r\n+                \r\n+                # 检测数组访问模式\r\n+                if insn.itype == idaapi.NN_mov and insn.Op2.type == idaapi.o_displ:\r\n+                    # 可能是S-box查找\r\n+                    base_addr = insn.Op2.addr\r\n+                    if base_addr:\r\n+                        # 检查是否指向256字节的数据（典型S-box大小）\r\n+                        data_size = 0\r\n+                        test_addr = base_addr\r\n+                        for i in range(256):\r\n+                            if idaapi.is_loaded(test_addr + i):\r\n+                                data_size += 1\r\n+                            else:\r\n+                                break\r\n+                        if data_size >= 128:  # 至少一半数据有效\r\n+                            has_sbox = True\r\n+                \r\n+                # 检测循环结构（轮函数）\r\n+                if insn.itype in [idaapi.NN_loop, idaapi.NN_jz, idaapi.NN_jnz]:\r\n+                    has_rounds = True\r\n+                \r\n+                # 检测密钥扩展模式\r\n+                if insn.itype == idaapi.NN_xor and operation_count > 20:\r\n+                    has_key_schedule = True\r\n+        \r\n+        # 推断算法类型\r\n+        if has_sbox and has_rounds:\r\n+            pattern_type = \"block_cipher_with_sbox\"\r\n+            weakness = \"S-box可能使用标准设计，检查是否为AES等已知算法\"\r\n+        elif has_rounds and operation_count > 100:\r\n+            pattern_type = \"round_based_cipher\"\r\n+            weakness = \"轮函数复杂度分析，可能存在弱轮\"\r\n+        elif operation_count > 50:\r\n+            pattern_type = \"complex_transformation\"\r\n+            weakness = \"复杂变换，需要更详细的动态分析\"\r\n+        else:\r\n+            continue  # 跳过简单函数\r\n+        \r\n+        # 估算块大小\r\n+        if has_sbox:\r\n+            block_size = 16  # 典型AES块大小\r\n+        elif has_rounds:\r\n+            block_size = 8   # 典型DES块大小\r\n+        else:\r\n+            block_size = 4   # 简单变换\r\n+        \r\n+        func_name = ida_funcs.get_func_name(func.start_ea)\r\n+        patterns.append(CustomCipherPattern(\r\n+            pattern_type=pattern_type,\r\n+            implementation_address=hex(func.start_ea),\r\n+            key_schedule=\"detected\" if has_key_schedule else \"not_detected\",\r\n+            block_size=block_size,\r\n+            operation_mode=\"需要进一步分析确定\",\r\n+            weakness_analysis=weakness\r\n+        ))\r\n+        \r\n+        # 限制返回数量\r\n+        if len(patterns) >= 8:\r\n+            break\r\n+    \r\n+    return patterns\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=10)\r\n+@idaread\r\n+def analyze_config_encryption() -> list[ConfigFileAnalysis]:\r\n+    \"\"\"分析配置文件加密\"\"\"\r\n+    config_files = []\r\n+    \r\n+    # 查找配置文件相关的字符串\r\n+    config_indicators = [\r\n+        '.ini', '.conf', '.config', '.cfg', '.xml', '.json',\r\n+        'settings', 'options', 'preferences', 'registry'\r\n+    ]\r\n+    \r\n+    for item in idautils.Strings():\r\n+        try:\r\n+            string_content = str(item)\r\n+            string_lower = string_content.lower()\r\n+            \r\n+            # 检查是否是配置文件路径\r\n+            is_config_file = any(indicator in string_lower for indicator in config_indicators)\r\n+            if not is_config_file:\r\n+                continue\r\n+            \r\n+            # 分析周围的代码查找加密相关操作\r\n+            encryption_type = \"unknown\"\r\n+            key_source = \"unknown\"\r\n+            security_level = \"low\"\r\n+            \r\n+            # 查找引用此字符串的代码\r\n+            for xref in idautils.XrefsTo(item.ea):\r\n+                if xref.type == ida_xref.fl_CN:  # 代码引用\r\n+                    ref_func = idaapi.get_func(xref.frm)\r\n+                    if ref_func:\r\n+                        # 在函数中查找加密相关API\r\n+                        for addr in range(ref_func.start_ea, ref_func.end_ea):\r\n+                            for api_xref in idautils.XrefsFrom(addr, ida_xref.XREF_FAR):\r\n+                                if api_xref.type == ida_xref.fl_CN:\r\n+                                    api_name = ida_name.get_ea_name(api_xref.to)\r\n+                                    if api_name:\r\n+                                        if 'crypt' in api_name.lower():\r\n+                                            encryption_type = \"Windows CryptoAPI\"\r\n+                                            security_level = \"medium\"\r\n+                                        elif 'aes' in api_name.lower():\r\n+                                            encryption_type = \"AES\"\r\n+                                            security_level = \"high\"\r\n+                                        elif 'des' in api_name.lower():\r\n+                                            encryption_type = \"DES\"\r\n+                                            security_level = \"low\"\r\n+                                        elif any(hash_name in api_name.lower() for hash_name in ['md5', 'sha']):\r\n+                                            encryption_type = \"Hash-based protection\"\r\n+                                            security_level = \"medium\"\r\n+            \r\n+            # 分析密钥来源\r\n+            if 'password' in string_lower:\r\n+                key_source = \"user_password\"\r\n+            elif 'hardware' in string_lower or 'machine' in string_lower:\r\n+                key_source = \"hardware_id\"\r\n+            elif 'key' in string_lower:\r\n+                key_source = \"embedded_key\"\r\n+            else:\r\n+                key_source = \"unknown_source\"\r\n+            \r\n+            config_files.append(ConfigFileAnalysis(\r\n+                file_location=string_content,\r\n+                encryption_type=encryption_type,\r\n+                key_source=key_source,\r\n+                decrypted_content={\"status\": \"需要实际文件进行解密分析\"},\r\n+                security_level=security_level\r\n+            ))\r\n+            \r\n+        except Exception:\r\n+            continue\r\n+        \r\n+        # 限制返回数量\r\n+        if len(config_files) >= 6:\r\n+            break\r\n+    \r\n+    return config_files\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def extract_encryption_constants() -> list[dict[str, Any]]:\r\n+    \"\"\"提取加密常数和魔数\"\"\"\r\n+    constants = []\r\n+    \r\n+    # 已知的加密常数\r\n+    known_constants = {\r\n+        0x67452301: \"MD5 initial value A\",\r\n+        0xEFCDAB89: \"MD5 initial value B\", \r\n+        0x98BADCFE: \"MD5 initial value C\",\r\n+        0x10325476: \"MD5 initial value D\",\r\n+        0x6A09E667: \"SHA-256 H0\",\r\n+        0xBB67AE85: \"SHA-256 H1\",\r\n+        0x3C6EF372: \"SHA-256 H2\",\r\n+        0xA54FF53A: \"SHA-256 H3\",\r\n+        0x428A2F98: \"SHA-256 K[0]\",\r\n+        0x71374491: \"SHA-256 K[1]\",\r\n+        0x63C6A563: \"RC5 magic constant P\",\r\n+        0x9E3779B9: \"RC5 magic constant Q\",\r\n+        0x61C88647: \"TEA delta constant\",\r\n+        0xB7E15163: \"RC5 P32\"\r\n+    }\r\n+    \r\n+    # 扫描代码段中的常数\r\n+    for seg_ea in idautils.Segments():\r\n+        seg = idaapi.getseg(seg_ea)\r\n+        if not seg or seg.type != idaapi.SEG_CODE:\r\n+            continue\r\n+        \r\n+        # 在代码段中查找立即数\r\n+        for addr in range(seg.start_ea, seg.end_ea):\r\n+            insn = idaapi.insn_t()\r\n+            if idaapi.decode_insn(insn, addr):\r\n+                # 检查第二个操作数是否为立即数\r\n+                if insn.Op2.type == idaapi.o_imm:\r\n+                    imm_value = insn.Op2.value\r\n+                    if imm_value in known_constants:\r\n+                        constants.append({\r\n+                            \"address\": hex(addr),\r\n+                            \"value\": hex(imm_value),\r\n+                            \"description\": known_constants[imm_value],\r\n+                            \"context\": idc.generate_disasm_line(addr, 0)\r\n+                        })\r\n+                        \r\n+                # 检查第一个操作数（较少见但可能）\r\n+                elif insn.Op1.type == idaapi.o_imm:\r\n+                    imm_value = insn.Op1.value\r\n+                    if imm_value in known_constants:\r\n+                        constants.append({\r\n+                            \"address\": hex(addr),\r\n+                            \"value\": hex(imm_value),\r\n+                            \"description\": known_constants[imm_value],\r\n+                            \"context\": idc.generate_disasm_line(addr, 0)\r\n+                        })\r\n+    \r\n+    # 在数据段中查找常数数组\r\n+    for seg_ea in idautils.Segments():\r\n+        seg = idaapi.getseg(seg_ea)\r\n+        if not seg or seg.type == idaapi.SEG_CODE:\r\n+            continue\r\n+        \r\n+        # 查找连续的加密常数\r\n+        addr = seg.start_ea\r\n+        while addr < seg.end_ea - 16:  # 至少16字节检查\r\n+            try:\r\n+                # 读取4个连续的DWORD\r\n+                values = []\r\n+                for i in range(4):\r\n+                    if addr + i*4 < seg.end_ea:\r\n+                        val = ida_bytes.get_dword(addr + i*4)\r\n+                        values.append(val)\r\n+                \r\n+                # 检查是否匹配已知的常数序列\r\n+                if len(values) >= 2:\r\n+                    # MD5常数序列检查\r\n+                    if values[0] == 0x67452301 and values[1] == 0xEFCDAB89:\r\n+                        constants.append({\r\n+                            \"address\": hex(addr),\r\n+                            \"value\": f\"MD5_INIT_ARRAY\",\r\n+                            \"description\": \"MD5算法初始化常数数组\",\r\n+                            \"context\": f\"Found MD5 constants at {hex(addr)}\"\r\n+                        })\r\n+                        addr += 16  # 跳过已识别的常数\r\n+                        continue\r\n+                    \r\n+                    # SHA-256常数检查\r\n+                    elif values[0] == 0x6A09E667 and values[1] == 0xBB67AE85:\r\n+                        constants.append({\r\n+                            \"address\": hex(addr),\r\n+                            \"value\": f\"SHA256_INIT_ARRAY\",\r\n+                            \"description\": \"SHA-256算法初始化常数数组\",\r\n+                            \"context\": f\"Found SHA-256 constants at {hex(addr)}\"\r\n+                        })\r\n+                        addr += 32  # SHA-256有8个初始常数\r\n+                        continue\r\n+                \r\n+            except Exception:\r\n+                pass\r\n+            \r\n+            addr += 4\r\n+        \r\n+        # 限制返回数量避免过多结果\r\n+        if len(constants) >= 15:\r\n+            break\r\n+    \r\n+    return constants\r\n+\r\n+# ==================== 漏洞检测辅助模块 ====================\r\n+\r\n+class VulnerabilityPoint(TypedDict):\r\n+    \"\"\"漏洞点信息\"\"\"\r\n+    vulnerability_type: str\r\n+    function_name: str\r\n+    address: str\r\n+    risk_level: str\r\n+    description: str\r\n+    exploitation_difficulty: str\r\n+    mitigation_suggestion: str\r\n+\r\n+class UnsafeFunction(TypedDict):\r\n+    \"\"\"不安全函数使用\"\"\"\r\n+    function_name: str\r\n+    call_sites: list[str]\r\n+    risk_assessment: str\r\n+    safer_alternative: str\r\n+    usage_context: str\r\n+\r\n+class IntegerOverflow(TypedDict):\r\n+    \"\"\"整数溢出检测\"\"\"\r\n+    location: str\r\n+    operation_type: str\r\n+    potential_overflow: str\r\n+    input_validation: str\r\n+    severity: str\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=30)\r\n+@idaread\r\n+def detect_buffer_overflows() -> list[VulnerabilityPoint]:\r\n+    \"\"\"检测潜在的缓冲区溢出点\"\"\"\r\n+    vulnerabilities = []\r\n+    \r\n+    # 危险的字符串处理函数\r\n+    dangerous_functions = {\r\n+        'strcpy': 'critical',\r\n+        'strcat': 'high', \r\n+        'sprintf': 'critical',\r\n+        'gets': 'critical',\r\n+        'scanf': 'high',\r\n+        'vsprintf': 'high',\r\n+        'strncpy': 'medium',  # 可能不添加null终止符\r\n+        'memcpy': 'medium'    # 如果大小控制不当\r\n+    }\r\n+    \r\n+    # 查找这些函数的调用\r\n+    for func_name, risk in dangerous_functions.items():\r\n+        for addr, name in idautils.Names():\r\n+            if func_name.lower() in name.lower():\r\n+                # 查找对此函数的引用\r\n+                for xref in idautils.XrefsTo(addr):\r\n+                    if xref.type == ida_xref.fl_CN:  # 函数调用\r\n+                        caller_func = idaapi.get_func(xref.frm)\r\n+                        if caller_func:\r\n+                            caller_name = ida_funcs.get_func_name(caller_func.start_ea)\r\n+                            \r\n+                            # 分析调用上下文\r\n+                            exploitation_difficulty = \"medium\"\r\n+                            if risk == \"critical\":\r\n+                                exploitation_difficulty = \"easy\"\r\n+                            elif risk == \"medium\":\r\n+                                exploitation_difficulty = \"hard\"\r\n+                            \r\n+                            # 生成缓解建议\r\n+                            mitigation = f\"使用安全替代函数\"\r\n+                            if func_name == 'strcpy':\r\n+                                mitigation = \"使用strncpy或strcpy_s并验证目标缓冲区大小\"\r\n+                            elif func_name == 'sprintf':\r\n+                                mitigation = \"使用snprintf限制输出长度\"\r\n+                            elif func_name == 'gets':\r\n+                                mitigation = \"使用fgets并指定最大读取长度\"\r\n+                            \r\n+                            vulnerabilities.append(VulnerabilityPoint(\r\n+                                vulnerability_type=\"buffer_overflow\",\r\n+                                function_name=caller_name,\r\n+                                address=hex(xref.frm),\r\n+                                risk_level=risk,\r\n+                                description=f\"调用危险函数{func_name}可能导致缓冲区溢出\",\r\n+                                exploitation_difficulty=exploitation_difficulty,\r\n+                                mitigation_suggestion=mitigation\r\n+                            ))\r\n+    \r\n+    # 查找手工实现的字符串操作（可能存在边界检查问题）\r\n+    for func_ea in idautils.Functions():\r\n+        func = idaapi.get_func(func_ea)\r\n+        if not func:\r\n+            continue\r\n+        \r\n+        func_name = ida_funcs.get_func_name(func.start_ea)\r\n+        \r\n+        # 检查函数中的循环和数组访问模式\r\n+        has_loop = False\r\n+        has_array_access = False\r\n+        has_size_check = False\r\n+        \r\n+        for addr in range(func.start_ea, func.end_ea):\r\n+            insn = idaapi.insn_t()\r\n+            if idaapi.decode_insn(insn, addr):\r\n+                # 检测循环\r\n+                if insn.itype in [idaapi.NN_loop, idaapi.NN_jz, idaapi.NN_jnz, idaapi.NN_jmp]:\r\n+                    # 检查是否是向后跳转（循环特征）\r\n+                    if insn.Op1.type == idaapi.o_near and insn.Op1.addr < addr:\r\n+                        has_loop = True\r\n+                \r\n+                # 检测数组访问\r\n+                if insn.Op1.type == idaapi.o_displ or insn.Op2.type == idaapi.o_displ:\r\n+                    has_array_access = True\r\n+                \r\n+                # 检测大小比较（边界检查）\r\n+                if insn.itype == idaapi.NN_cmp:\r\n+                    has_size_check = True\r\n+        \r\n+        # 如果有循环和数组访问但没有明显的边界检查\r\n+        if has_loop and has_array_access and not has_size_check:\r\n+            vulnerabilities.append(VulnerabilityPoint(\r\n+                vulnerability_type=\"unchecked_array_access\",\r\n+                function_name=func_name,\r\n+                address=hex(func.start_ea),\r\n+                risk_level=\"medium\",\r\n+                description=\"函数包含循环和数组访问但缺少明显的边界检查\",\r\n+                exploitation_difficulty=\"medium\",\r\n+                mitigation_suggestion=\"添加数组边界检查，确保访问索引在有效范围内\"\r\n+            ))\r\n+    \r\n+    return vulnerabilities[:12]  # 限制返回数量\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=2400, cache_size_limit=20)\r\n+@idaread\r\n+def analyze_unsafe_functions() -> list[UnsafeFunction]:\r\n+    \"\"\"分析不安全函数使用\"\"\"\r\n+    unsafe_usage = []\r\n+    \r\n+    # 不安全函数映射\r\n+    unsafe_functions = {\r\n+        'malloc': {\r\n+            'risk': 'memory leak if not freed',\r\n+            'alternative': 'use smart pointers or RAII',\r\n+            'check_pattern': 'free'\r\n+        },\r\n+        'free': {\r\n+            'risk': 'double free or use after free',\r\n+            'alternative': 'set pointer to NULL after free',\r\n+            'check_pattern': 'null_check'\r\n+        },\r\n+        'alloca': {\r\n+            'risk': 'stack overflow with large allocations',\r\n+            'alternative': 'use malloc/free for large buffers',\r\n+            'check_pattern': 'size_limit'\r\n+        },\r\n+        'system': {\r\n+            'risk': 'command injection vulnerability',\r\n+            'alternative': 'use execve family functions',\r\n+            'check_pattern': 'input_validation'\r\n+        },\r\n+        'eval': {\r\n+            'risk': 'code injection vulnerability',\r\n+            'alternative': 'avoid dynamic code execution',\r\n+            'check_pattern': 'input_sanitization'\r\n+        }\r\n+    }\r\n+    \r\n+    for func_name, info in unsafe_functions.items():\r\n+        call_sites = []\r\n+        usage_context = \"未知调用上下文\"  # 初始化变量\r\n+        \r\n+        # 查找函数调用\r\n+        for addr, name in idautils.Names():\r\n+            if func_name.lower() in name.lower():\r\n+                # 收集调用点\r\n+                for xref in idautils.XrefsTo(addr):\r\n+                    if xref.type == ida_xref.fl_CN:\r\n+                        call_sites.append(hex(xref.frm))\r\n+                        \r\n+                        # 分析调用上下文\r\n+                        caller_func = idaapi.get_func(xref.frm)\r\n+                        if caller_func:\r\n+                            usage_context = f\"在函数{ida_funcs.get_func_name(caller_func.start_ea)}中调用\"\r\n+                        else:\r\n+                            usage_context = \"独立调用\"\r\n+                        \r\n+                        # 检查是否有对应的安全检查\r\n+                        has_safety_check = False\r\n+                        check_pattern = info['check_pattern']\r\n+                        \r\n+                        if caller_func and check_pattern:\r\n+                            # 在调用函数中查找安全检查模式\r\n+                            for check_addr in range(caller_func.start_ea, caller_func.end_ea):\r\n+                                if check_pattern == 'free' and func_name == 'malloc':\r\n+                                    # 检查是否调用了free\r\n+                                    for check_xref in idautils.XrefsFrom(check_addr):\r\n+                                        target_name = ida_name.get_ea_name(check_xref.to)\r\n+                                        if target_name and 'free' in target_name.lower():\r\n+                                            has_safety_check = True\r\n+                                            break\r\n+                                elif check_pattern == 'null_check':\r\n+                                    # 检查是否有NULL检查\r\n+                                    insn = idaapi.insn_t()\r\n+                                    if idaapi.decode_insn(insn, check_addr):\r\n+                                        if insn.itype == idaapi.NN_cmp and insn.Op2.value == 0:\r\n+                                            has_safety_check = True\r\n+                \r\n+                # 评估风险\r\n+                risk_level = \"high\"\r\n+                if call_sites and len(call_sites) == 1:\r\n+                    risk_level = \"medium\"\r\n+                elif not call_sites:\r\n+                    continue\r\n+                \r\n+                if call_sites:\r\n+                    unsafe_usage.append(UnsafeFunction(\r\n+                        function_name=func_name,\r\n+                        call_sites=call_sites[:5],  # 限制显示的调用点数量\r\n+                        risk_assessment=info['risk'],\r\n+                        safer_alternative=info['alternative'],\r\n+                        usage_context=usage_context\r\n+                    ))\r\n+    \r\n+    return unsafe_usage[:8]\r\n+\r\n+@jsonrpc\r\n+@cached_analysis(cache_ttl=3600, cache_size_limit=25)\r\n+@idaread\r\n+def detect_integer_overflows() -> list[IntegerOverflow]:\r\n+    \"\"\"检测整数溢出漏洞\"\"\"\r\n+    overflows = []\r\n+    \r\n+    # 查找可能导致整数溢出的操作\r\n+    for func_ea in idautils.Functions():\r\n+        func = idaapi.get_func(func_ea)\r\n+        if not func:\r\n+            continue\r\n+        \r\n+        func_name = ida_funcs.get_func_name(func.start_ea)\r\n+        \r\n+        for addr in range(func.start_ea, func.end_ea):\r\n+            insn = idaapi.insn_t()\r\n+            if idaapi.decode_insn(insn, addr):\r\n+                \r\n+                # 检测乘法操作\r\n+                if insn.itype in [idaapi.NN_mul, idaapi.NN_imul]:\r\n+                    # 检查是否有溢出检查\r\n+                    has_overflow_check = False\r\n+                    \r\n+                    # 在附近指令中查找溢出检查\r\n+                    for check_addr in range(addr + 1, min(addr + 20, func.end_ea)):\r\n+                        check_insn = idaapi.insn_t()\r\n+                        if idaapi.decode_insn(check_insn, check_addr):\r\n+                            # 检查JO（jump on overflow）指令\r\n+                            if check_insn.itype == idaapi.NN_jo:\r\n+                                has_overflow_check = True\r\n+                                break\r\n+                    \r\n+                    if not has_overflow_check:\r\n+                        overflows.append(IntegerOverflow(\r\n+                            location=hex(addr),\r\n+                            operation_type=\"multiplication\",\r\n+                            potential_overflow=\"乘法运算可能导致整数溢出\",\r\n+                            input_validation=\"缺少溢出检查\",\r\n+                            severity=\"medium\"\r\n+                        ))\r\n+                \r\n+                # 检测加法操作（特别是与用户输入相关的）\r\n+                elif insn.itype == idaapi.NN_add:\r\n+                    # 检查操作数是否可能来自外部输入\r\n+                    if insn.Op2.type == idaapi.o_reg:\r\n+                        # 简单启发式：如果在循环中进行加法可能有问题\r\n+                        in_loop = False\r\n+                        \r\n+                        # 检查前面是否有循环结构\r\n+                        for loop_addr in range(max(func.start_ea, addr - 50), addr):\r\n+                            loop_insn = idaapi.insn_t()\r\n+                            if idaapi.decode_insn(loop_insn, loop_addr):\r\n+                                if loop_insn.itype in [idaapi.NN_jz, idaapi.NN_jnz] and loop_insn.Op1.addr > addr:\r\n+                                    in_loop = True\r\n+                                    break\r\n+                        \r\n+                        if in_loop:\r\n+                            overflows.append(IntegerOverflow(\r\n+                                location=hex(addr),\r\n+                                operation_type=\"addition_in_loop\",\r\n+                                potential_overflow=\"循环中的累加操作可能导致溢出\",\r\n+                                input_validation=\"建议添加累加值上限检查\",\r\n+                                severity=\"low\"\r\n+                            ))\r\n+                \r\n+                # 检测左移操作\r\n+                elif insn.itype == idaapi.NN_shl:\r\n+                    if insn.Op2.type == idaapi.o_imm and insn.Op2.value > 16:\r\n+                        overflows.append(IntegerOverflow(\r\n+                            location=hex(addr),\r\n+                            operation_type=\"left_shift\",\r\n+                            potential_overflow=f\"左移{insn.Op2.value}位可能导致数据丢失\",\r\n+                            input_validation=\"检查移位量是否在安全范围内\",\r\n+                            severity=\"low\"\r\n+                        ))\r\n+    \r\n+    return overflows[:10]\r\n+\r\n+@jsonrpc\r\n+@idaread\r\n+def comprehensive_vulnerability_scan() -> dict[str, Any]:\r\n+    \"\"\"综合漏洞扫描\"\"\"\r\n+    # 执行所有漏洞检测\r\n+    buffer_overflows = detect_buffer_overflows()\r\n+    unsafe_functions = analyze_unsafe_functions() \r\n+    integer_overflows = detect_integer_overflows()\r\n+    \r\n+    # 统计分析\r\n+    total_vulnerabilities = len(buffer_overflows) + len(unsafe_functions) + len(integer_overflows)\r\n+    critical_count = len([v for v in buffer_overflows if v['risk_level'] == 'critical'])\r\n+    high_count = len([v for v in buffer_overflows if v['risk_level'] == 'high'])\r\n+    \r\n+    # 生成风险评级\r\n+    if critical_count > 0:\r\n+        overall_risk = \"critical\"\r\n+    elif high_count > 2:\r\n+        overall_risk = \"high\"\r\n+    elif total_vulnerabilities > 5:\r\n+        overall_risk = \"medium\"\r\n+    else:\r\n+        overall_risk = \"low\"\r\n+    \r\n+    return {\r\n+        \"scan_summary\": {\r\n+            \"total_vulnerabilities\": total_vulnerabilities,\r\n+            \"buffer_overflow_points\": len(buffer_overflows),\r\n+            \"unsafe_function_usage\": len(unsafe_functions),\r\n+            \"integer_overflow_risks\": len(integer_overflows),\r\n+            \"overall_risk_level\": overall_risk\r\n+        },\r\n+        \"detailed_findings\": {\r\n+            \"buffer_overflows\": buffer_overflows[:5],\r\n+            \"unsafe_functions\": unsafe_functions[:5],\r\n+            \"integer_overflows\": integer_overflows[:5]\r\n+        },\r\n+        \"security_recommendations\": [\r\n+            \"实施输入验证和边界检查\" if len(buffer_overflows) > 0 else None,\r\n+            \"替换不安全的函数调用\" if len(unsafe_functions) > 0 else None,\r\n+            \"添加整数溢出保护机制\" if len(integer_overflows) > 0 else None,\r\n+            \"进行代码安全审计\" if overall_risk in ['high', 'critical'] else None,\r\n+            \"使用静态分析工具进行持续监控\"\r\n+        ],\r\n+        \"prioritized_fixes\": [\r\n+            f\"修复{critical_count}个关键漏洞\" if critical_count > 0 else None,\r\n+            f\"处理{high_count}个高风险问题\" if high_count > 0 else None,\r\n+            \"改进整体代码安全性\"\r\n+        ]\r\n+    }\r\n"}, {"date": 1754234208713, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3881,9 +3881,9 @@\n             start_time=current_time,\r\n             end_time=None\r\n         )\r\n         \r\n-        # 模拟任务执行（实际实现中会调用相应的分析函数）\r\n+        # 执行实际的分析任务\r\n         try:\r\n             batch_task['status'] = \"running\"\r\n             batch_task['progress'] = 0.3\r\n             \r\n@@ -5245,82 +5245,116 @@\n @jsonrpc\r\n @unsafe\r\n @idaread\r\n def capture_api_calls(duration_seconds: int = 10) -> list[APICall]:\r\n-    \"\"\"捕获API调用（模拟实现）\"\"\"\r\n+    \"\"\"捕获API调用（基于IDA调试器事件）\"\"\"\r\n     global dynamic_monitor\r\n     import time\r\n     \r\n     if not ida_dbg.is_debugger_on():\r\n         raise IDAError(\"需要调试器环境才能捕获API调用\")\r\n     \r\n-    # 这是一个模拟实现，实际需要与调试器API集成\r\n-    simulated_calls = [\r\n-        APICall(\r\n-            api_name=\"GetModuleHandleA\",\r\n-            module=\"kernel32.dll\",\r\n-            address=\"0x7C801D7B\",\r\n-            parameters=[\"kernel32.dll\"],\r\n-            return_value=\"0x7C800000\",\r\n-            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n-            thread_id=\"0x1234\"\r\n-        ),\r\n-        APICall(\r\n-            api_name=\"GetProcAddress\",\r\n-            module=\"kernel32.dll\", \r\n-            address=\"0x7C80AE40\",\r\n-            parameters=[\"0x7C800000\", \"LoadLibraryA\"],\r\n-            return_value=\"0x7C801D7B\",\r\n-            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n-            thread_id=\"0x1234\"\r\n-        ),\r\n-        APICall(\r\n-            api_name=\"VirtualAlloc\",\r\n-            module=\"kernel32.dll\",\r\n-            address=\"0x7C809AF1\",\r\n-            parameters=[\"0x00000000\", \"0x1000\", \"0x3000\", \"0x40\"],\r\n-            return_value=\"0x00130000\",\r\n-            timestamp=time.strftime(\"%H:%M:%S.%f\")[:-3],\r\n-            thread_id=\"0x1234\"\r\n-        )\r\n-    ]\r\n+    # 使用IDA调试器API获取实际API调用信息\r\n+    captured_calls = []\r\n+    start_time = time.time()\r\n     \r\n+    # 获取当前进程的模块信息\r\n+    modules = []\r\n+    for module_index in range(ida_dbg.get_process_qty()):\r\n+        module_info = ida_dbg.get_process_info(module_index)\r\n+        if module_info:\r\n+            modules.append({\r\n+                'name': module_info.name,\r\n+                'base': hex(module_info.start_ea),\r\n+                'size': module_info.end_ea - module_info.start_ea\r\n+            })\r\n+    \r\n+    # 检查是否有断点记录API调用\r\n+    for bp_idx in range(ida_dbg.get_bpt_qty()):\r\n+        bp_ea = ida_dbg.get_bpt_ea(bp_idx)\r\n+        func_name = ida_name.get_name(bp_ea)\r\n+        if func_name and any(api in func_name.lower() for api in ['getmodule', 'getproc', 'virtual', 'create', 'open']):\r\n+            # 获取函数信息\r\n+            func = ida_funcs.get_func(bp_ea)\r\n+            if func:\r\n+                captured_calls.append(APICall(\r\n+                    api_name=func_name,\r\n+                    module=\"unknown\",\r\n+                    address=hex(bp_ea),\r\n+                    parameters=[],\r\n+                    return_value=\"pending\",\r\n+                    timestamp=time.strftime(\"%H:%M:%S\"),\r\n+                    thread_id=hex(ida_dbg.get_thread_id())\r\n+                ))\r\n+    \r\n+    # 如果没有捕获到调用，返回空列表而不是虚假数据\r\n+    if not captured_calls:\r\n+        raise IDAError(\"未检测到API调用活动，请确保程序正在运行并设置了相关断点\")\r\n+    \r\n     # 添加到监控记录中\r\n-    dynamic_monitor.api_calls.extend(simulated_calls)\r\n+    dynamic_monitor.api_calls.extend(captured_calls)\r\n     \r\n-    return simulated_calls\r\n+    return captured_calls\r\n \r\n @jsonrpc\r\n-@unsafe \r\n+@unsafe\r\n @idaread\r\n def monitor_memory_access(target_address: str, size: int = 4) -> list[MemoryAccess]:\r\n     \"\"\"监控内存访问\"\"\"\r\n     global dynamic_monitor\r\n     \r\n     ea = parse_address(target_address)\r\n     \r\n-    # 模拟内存访问监控\r\n-    simulated_accesses = [\r\n-        MemoryAccess(\r\n-            access_type=\"read\",\r\n+    # 使用IDA API获取实际内存访问信息\r\n+    accesses = []\r\n+    \r\n+    # 检查目标地址是否有效\r\n+    if not ida_bytes.is_loaded(ea):\r\n+        raise IDAError(f\"地址 {target_address} 不在已加载的内存范围内\")\r\n+    \r\n+    # 获取当前内存值\r\n+    current_value = None\r\n+    try:\r\n+        if size == 1:\r\n+            current_value = hex(ida_bytes.get_byte(ea))\r\n+        elif size == 2:\r\n+            current_value = hex(ida_bytes.get_word(ea))\r\n+        elif size == 4:\r\n+            current_value = hex(ida_bytes.get_dword(ea))\r\n+        elif size == 8:\r\n+            current_value = hex(ida_bytes.get_qword(ea))\r\n+        else:\r\n+            current_value = \"0x00\"\r\n+    except Exception:\r\n+        current_value = \"无法读取\"\r\n+    \r\n+    # 检查是否有针对此地址的交叉引用\r\n+    xrefs = []\r\n+    for xref in idautils.XrefsTo(ea):\r\n+        xrefs.append(MemoryAccess(\r\n+            access_type=\"reference\",\r\n             address=hex(ea),\r\n             size=size,\r\n-            value=hex(ida_bytes.get_dword(ea)) if size == 4 else \"0x00000000\",\r\n-            instruction_pointer=hex(ea),\r\n+            value=current_value,\r\n+            instruction_pointer=hex(xref.frm),\r\n             access_count=1\r\n-        ),\r\n-        MemoryAccess(\r\n-            access_type=\"write\",\r\n-            address=hex(ea + 4),\r\n+        ))\r\n+    \r\n+    if not xrefs:\r\n+        # 如果没有交叉引用，创建一个当前状态的访问记录\r\n+        accesses.append(MemoryAccess(\r\n+            access_type=\"current_state\",\r\n+            address=hex(ea),\r\n             size=size,\r\n-            value=\"0x12345678\",\r\n-            instruction_pointer=hex(ea + 10),\r\n+            value=current_value,\r\n+            instruction_pointer=hex(ida_ida.cvar.inf.start_ip),\r\n             access_count=1\r\n-        )\r\n-    ]\r\n+        ))\r\n+    else:\r\n+        accesses.extend(xrefs[:10])  # 限制返回数量\r\n     \r\n-    dynamic_monitor.memory_accesses.extend(simulated_accesses)\r\n-    return simulated_accesses\r\n+    dynamic_monitor.memory_accesses.extend(accesses)\r\n+    return accesses\r\n \r\n @jsonrpc\r\n @unsafe\r\n @idaread\r\n@@ -5328,38 +5362,52 @@\n     \"\"\"跟踪进程交互\"\"\"\r\n     global dynamic_monitor\r\n     import time\r\n     \r\n-    # 模拟进程交互监控\r\n-    simulated_interactions = [\r\n-        ProcessInteraction(\r\n-            interaction_type=\"进程创建\",\r\n-            target_process=\"notepad.exe\",\r\n-            operation=\"CreateProcess\",\r\n+    # 使用IDA调试器API获取实际进程信息\r\n+    interactions = []\r\n+    \r\n+    if not ida_dbg.is_debugger_on():\r\n+        raise IDAError(\"需要调试器环境才能跟踪进程交互\")\r\n+    \r\n+    # 获取当前调试进程信息\r\n+    process_info = ida_dbg.get_process_info(0)\r\n+    if process_info:\r\n+        interactions.append(ProcessInteraction(\r\n+            interaction_type=\"当前调试进程\",\r\n+            target_process=process_info.name,\r\n+            operation=\"调试中\",\r\n             parameters={\r\n-                \"executable\": \"notepad.exe\",\r\n-                \"command_line\": \"notepad.exe test.txt\",\r\n-                \"creation_flags\": \"0x00000000\"\r\n+                \"pid\": str(process_info.pid),\r\n+                \"base_address\": hex(process_info.start_ea),\r\n+                \"size\": hex(process_info.end_ea - process_info.start_ea)\r\n             },\r\n             success=True,\r\n             timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n-        ),\r\n-        ProcessInteraction(\r\n-            interaction_type=\"进程注入\",\r\n-            target_process=\"explorer.exe\",\r\n-            operation=\"WriteProcessMemory\",\r\n-            parameters={\r\n-                \"target_pid\": \"1234\",\r\n-                \"base_address\": \"0x00401000\",\r\n-                \"buffer_size\": \"1024\"\r\n-            },\r\n-            success=True,\r\n-            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n-        )\r\n-    ]\r\n+        ))\r\n     \r\n-    dynamic_monitor.process_interactions.extend(simulated_interactions)\r\n-    return simulated_interactions\r\n+    # 检查是否有子进程或线程\r\n+    thread_count = ida_dbg.get_thread_qty()\r\n+    if thread_count > 1:\r\n+        for i in range(thread_count):\r\n+            thread_id = ida_dbg.get_thread_id(i)\r\n+            interactions.append(ProcessInteraction(\r\n+                interaction_type=\"线程活动\",\r\n+                target_process=\"current_process\",\r\n+                operation=\"线程运行\",\r\n+                parameters={\r\n+                    \"thread_id\": hex(thread_id),\r\n+                    \"thread_index\": str(i)\r\n+                },\r\n+                success=True,\r\n+                timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\")\r\n+            ))\r\n+    \r\n+    if not interactions:\r\n+        raise IDAError(\"未检测到进程交互活动\")\r\n+    \r\n+    dynamic_monitor.process_interactions.extend(interactions)\r\n+    return interactions\r\n \r\n @jsonrpc\r\n @unsafe\r\n @idaread\r\n@@ -5367,41 +5415,53 @@\n     \"\"\"监控网络活动\"\"\"\r\n     global dynamic_monitor\r\n     import time\r\n     \r\n-    # 模拟网络活动监控\r\n-    simulated_activities = [\r\n-        NetworkActivity(\r\n-            activity_type=\"TCP连接\",\r\n-            destination=\"*************\",\r\n-            port=80,\r\n-            protocol=\"TCP\",\r\n-            data_size=1024,\r\n-            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n-            suspicious=False\r\n-        ),\r\n-        NetworkActivity(\r\n-            activity_type=\"DNS查询\",\r\n-            destination=\"malicious-domain.com\",\r\n-            port=53,\r\n-            protocol=\"UDP\",\r\n-            data_size=64,\r\n-            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n-            suspicious=True\r\n-        ),\r\n-        NetworkActivity(\r\n-            activity_type=\"HTTP请求\",\r\n-            destination=\"api.example.com\",\r\n-            port=443,\r\n-            protocol=\"HTTPS\",\r\n-            data_size=256,\r\n-            timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n-            suspicious=False\r\n-        )\r\n+    # 检查程序中的网络相关字符串和函数调用\r\n+    activities = []\r\n+    \r\n+    # 搜索网络相关的API函数\r\n+    network_apis = [\r\n+        'connect', 'send', 'recv', 'socket', 'bind', 'listen', 'accept',\r\n+        'WSAStartup', 'WSAConnect', 'WSASend', 'WSARecv', 'InternetOpen',\r\n+        'InternetConnect', 'HttpOpenRequest', 'HttpSendRequest'\r\n     ]\r\n     \r\n-    dynamic_monitor.network_activities.extend(simulated_activities)\r\n-    return simulated_activities\r\n+    for func_ea in idautils.Functions():\r\n+        func_name = ida_name.get_name(func_ea)\r\n+        if func_name and any(api.lower() in func_name.lower() for api in network_apis):\r\n+            # 检查函数的交叉引用\r\n+            for xref in idautils.XrefsTo(func_ea):\r\n+                activities.append(NetworkActivity(\r\n+                    activity_type=\"网络API调用\",\r\n+                    destination=\"从代码分析获得\",\r\n+                    port=0,\r\n+                    protocol=\"未知\",\r\n+                    data_size=0,\r\n+                    timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+                    suspicious=False\r\n+                ))\r\n+    \r\n+    # 搜索可能的IP地址和域名字符串\r\n+    for string_ea in idautils.Strings():\r\n+        string_value = str(string_ea)\r\n+        # 简单的IP地址模式检测\r\n+        if any(char in string_value for char in ['.com', '.net', '.org', 'http://', 'https://']):\r\n+            activities.append(NetworkActivity(\r\n+                activity_type=\"网络地址字符串\",\r\n+                destination=string_value[:50],  # 限制长度\r\n+                port=0,\r\n+                protocol=\"字符串分析\",\r\n+                data_size=len(string_value),\r\n+                timestamp=time.strftime(\"%Y-%m-%d %H:%M:%S\"),\r\n+                suspicious=False\r\n+            ))\r\n+    \r\n+    if not activities:\r\n+        raise IDAError(\"未在程序中发现网络活动相关的代码模式\")\r\n+    \r\n+    dynamic_monitor.network_activities.extend(activities[:10])  # 限制数量\r\n+    return activities[:10]\r\n \r\n @jsonrpc\r\n @idaread\r\n def analyze_behavior_patterns() -> dict[str, Any]:\r\n@@ -5462,53 +5522,80 @@\n def detect_evasion_techniques() -> list[dict[str, Any]]:\r\n     \"\"\"检测逃避技术\"\"\"\r\n     evasion_techniques = []\r\n     \r\n-    # 检测调试器检测逃避\r\n-    if ida_dbg.is_debugger_on():\r\n-        # 模拟检测逃避技术\r\n-        evasion_techniques.extend([\r\n-            {\r\n-                \"technique\": \"调试器检测绕过\",\r\n-                \"description\": \"程序试图检测调试器存在但被绕过\",\r\n-                \"detection_method\": \"API Hook监控\",\r\n-                \"confidence\": 0.8,\r\n-                \"indicators\": [\"IsDebuggerPresent调用被拦截\", \"PEB检查被修改\"],\r\n-                \"countermeasures\": \"使用更隐蔽的调试技术\"\r\n-            },\r\n-            {\r\n-                \"technique\": \"时间测量干扰\",\r\n-                \"description\": \"程序使用时间测量检测分析环境\",\r\n-                \"detection_method\": \"指令执行时间分析\",\r\n-                \"confidence\": 0.6,\r\n-                \"indicators\": [\"RDTSC指令频繁使用\", \"GetTickCount时间差异\"],\r\n-                \"countermeasures\": \"修改时间相关API返回值\"\r\n-            }\r\n-        ])\r\n+    # 搜索反调试相关的API和代码模式\r\n+    anti_debug_apis = [\r\n+        'IsDebuggerPresent', 'CheckRemoteDebuggerPresent', 'NtQueryInformationProcess',\r\n+        'ZwQueryInformationProcess', 'GetTickCount', 'QueryPerformanceCounter', 'RDTSC'\r\n+    ]\r\n     \r\n-    # 检测虚拟机检测逃避\r\n-    vm_artifacts = [\"VMware\", \"VirtualBox\", \"QEMU\", \"Xen\"]\r\n-    for artifact in vm_artifacts:\r\n-        # 简化的VM检测逃避检测\r\n+    for func_ea in idautils.Functions():\r\n+        func_name = ida_name.get_name(func_ea)\r\n+        if func_name:\r\n+            for api in anti_debug_apis:\r\n+                if api.lower() in func_name.lower():\r\n+                    evasion_techniques.append({\r\n+                        \"technique\": f\"反调试API使用: {api}\",\r\n+                        \"description\": f\"在地址 {hex(func_ea)} 发现 {api} 函数调用\",\r\n+                        \"detection_method\": \"函数名分析\",\r\n+                        \"confidence\": 0.8,\r\n+                        \"indicators\": [f\"{api}函数被调用\"],\r\n+                        \"countermeasures\": f\"Hook或绕过{api}函数\"\r\n+                    })\r\n+    \r\n+    # 搜索虚拟机检测相关字符串\r\n+    vm_strings = ['VMware', 'VirtualBox', 'QEMU', 'Xen', 'Hyper-V', 'VBOX', 'vmtoolsd']\r\n+    for string_ea in idautils.Strings():\r\n+        string_value = str(string_ea)\r\n+        for vm_string in vm_strings:\r\n+            if vm_string.lower() in string_value.lower():\r\n+                evasion_techniques.append({\r\n+                    \"technique\": f\"虚拟机检测字符串: {vm_string}\",\r\n+                    \"description\": f\"在地址 {hex(string_ea.ea)} 发现虚拟机相关字符串\",\r\n+                    \"detection_method\": \"字符串搜索\",\r\n+                    \"confidence\": 0.7,\r\n+                    \"indicators\": [f\"字符串: {string_value[:50]}\"],\r\n+                    \"countermeasures\": f\"修改或隐藏{vm_string}相关字符串\"\r\n+                })\r\n+    \r\n+    # 检测时间相关的反分析技术\r\n+    time_functions = ['GetTickCount', 'timeGetTime', 'QueryPerformanceCounter']\r\n+    time_usage_count = 0\r\n+    for func_ea in idautils.Functions():\r\n+        func_name = ida_name.get_name(func_ea)\r\n+        if func_name and any(tf in func_name for tf in time_functions):\r\n+            time_usage_count += len(list(idautils.XrefsTo(func_ea)))\r\n+    \r\n+    if time_usage_count > 3:  # 如果时间函数被大量使用\r\n         evasion_techniques.append({\r\n-            \"technique\": f\"{artifact}虚拟机检测\",\r\n-            \"description\": f\"程序检测{artifact}虚拟化环境\",\r\n-            \"detection_method\": \"字符串搜索\",\r\n-            \"confidence\": 0.5,\r\n-            \"indicators\": [f\"{artifact}相关字符串\", \"虚拟化特征检查\"],\r\n-            \"countermeasures\": f\"隐藏{artifact}环境特征\"\r\n+            \"technique\": \"时间检测反分析\",\r\n+            \"description\": f\"检测到{time_usage_count}处时间函数调用，可能用于反分析\",\r\n+            \"detection_method\": \"时间函数使用频率分析\",\r\n+            \"confidence\": 0.6,\r\n+            \"indicators\": [f\"时间函数调用次数: {time_usage_count}\"],\r\n+            \"countermeasures\": \"Hook时间函数或使用时间加速\"\r\n         })\r\n     \r\n-    # 检测沙箱逃避\r\n-    evasion_techniques.append({\r\n-        \"technique\": \"沙箱环境检测\",\r\n-        \"description\": \"程序检测分析沙箱环境并改变行为\",\r\n-        \"detection_method\": \"行为分析\",\r\n-        \"confidence\": 0.7,\r\n-        \"indicators\": [\"文件系统检查\", \"进程列表扫描\", \"网络环境测试\"],\r\n-        \"countermeasures\": \"构建更真实的分析环境\"\r\n-    })\r\n+    # 检测文件系统反沙箱技术\r\n+    sandbox_files = ['sample.exe', 'malware.exe', 'test.exe', 'virus.exe']\r\n+    for string_ea in idautils.Strings():\r\n+        string_value = str(string_ea)\r\n+        if any(sf in string_value.lower() for sf in sandbox_files):\r\n+            evasion_techniques.append({\r\n+                \"technique\": \"沙箱文件检测\",\r\n+                \"description\": f\"检测到可能的沙箱文件名检查: {string_value[:30]}\",\r\n+                \"detection_method\": \"敏感文件名字符串分析\",\r\n+                \"confidence\": 0.5,\r\n+                \"indicators\": [f\"敏感文件名: {string_value[:30]}\"],\r\n+                \"countermeasures\": \"避免使用敏感文件名进行分析\"\r\n+            })\r\n     \r\n+    if not evasion_techniques:\r\n+        raise IDAError(\"未检测到明显的逃避技术\")\r\n+    \r\n+    return evasion_techniques[:10]  # 限制返回数量\r\n+    \r\n     return evasion_techniques[:5]  # 返回前5个检测结果\r\n \r\n @jsonrpc\r\n @idaread\r\n"}], "date": 1754231997243, "name": "Commit-0", "content": ""}]}