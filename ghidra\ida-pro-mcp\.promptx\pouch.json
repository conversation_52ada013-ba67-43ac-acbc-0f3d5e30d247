{"currentState": "role_activated_with_memory", "stateHistory": [{"from": "initial", "command": "action", "timestamp": "2025-08-04T02:51:10.111Z", "args": ["sean"]}, {"from": "role_activated_with_memory", "command": "init", "timestamp": "2025-08-04T02:51:19.333Z", "args": [{"workingDirectory": "d:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp", "ideType": "cursor"}]}, {"from": "initialized", "command": "init", "timestamp": "2025-08-04T03:11:25.347Z", "args": [{"workingDirectory": "d:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp"}]}, {"from": "initialized", "command": "action", "timestamp": "2025-08-04T03:54:36.821Z", "args": ["sean"]}, {"from": "role_activated_with_memory", "command": "learn", "timestamp": "2025-08-04T03:54:50.052Z", "args": ["@file://D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\mcp_enhancement_analysis.md"]}, {"from": "learn_error", "command": "learn", "timestamp": "2025-08-04T03:54:57.688Z", "args": ["@file://D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\technical_roadmap.md"]}, {"from": "learn_error", "command": "learn", "timestamp": "2025-08-04T03:55:05.463Z", "args": ["@file://D:\\lianghua\\QHXX\\mcp\\ghidra\\ida-pro-mcp\\executive_summary.md"]}, {"from": "learn_error", "command": "action", "timestamp": "2025-08-04T04:26:00.141Z", "args": ["sean"]}], "lastUpdated": "2025-08-04T04:26:00.237Z"}